CREATE TABLE dwm_im_online_base_info
(
    conversation_id                 STRING          COMMENT '会话ID',
    record_update_time              DATETIME        COMMENT '更新时间',
    conversation_ticket_ids         STRING          COMMENT 'MC会话工单',
    ticket_ids                      STRING          COMMENT 'MC工单ID',
    source                          STRING          COMMENT '来源',
    customer_name                   STRING          COMMENT '客户名称',
    customer_type                   STRING          COMMENT '客户类型 0个人,1企业,2政府,3组织(只有个人和企业的信息可靠)',
    owner_uin                       STRING          COMMENT '官网用户子账号',
    uin                             STRING          COMMENT '官网用户子账号',
    uid                             STRING          COMMENT '非腾讯云用户唯一标识，如微信openid',
    title                           STRING          COMMENT '会话标题，即客户在本次会话中的第一个问题',
    staff_title                     STRING          COMMENT '客户没有发言时，获取客服的首次发言作为标题',
    status                          STRING          COMMENT '状态',
    category_level1_name            STRING          COMMENT  '一级分类',
    category_level2_name            STRING          COMMENT  '二级分类',
    category_level3_name            STRING          COMMENT '三级分类',
    create_time                     DATETIME        COMMENT '会话创建时间',
    first_should_assign_id          BIGINT          COMMENT '首次应派队列ID',
    should_assign_id                BIGINT          COMMENT '应派队列ID',
    fact_assign_id                  BIGINT          COMMENT '实派队列ID',
    service_scene                   BIGINT          COMMENT '归档',
    staffs_user_ids                 STRING          COMMENT '经手客服id',
    solve_status                    STRING          COMMENT '客户评价是否解决',
    appraise                        STRING          COMMENT '满意度评价内容',
    service_rate                    STRING          COMMENT '评价星级',
    unsatisfy_reason                STRING          COMMENT '不满意原因',
    conversation_service_rate       STRING          COMMENT '服务评价星级',
    conversation_unsatisfy_reason   STRING          COMMENT '服务不满意原因',
    product_rate                    STRING          COMMENT '产品评价星级',
    product_unsatisfy_reason        STRING          COMMENT '产品不满意原因',
    recommend_score                 STRING          COMMENT 'NPS推荐分值',
    parent                          STRING          COMMENT '父会话id',
    creator_type                    STRING          COMMENT '创建人类型：0-web3.0以前的默认值 1-客户 2-坐席',
    finish_type                     STRING          COMMENT '结束人类型：1-客户 2-坐席 3-系统',
    alarm_level                     BIGINT          COMMENT '告警级别',
    origin_status                   BIGINT          COMMENT '会话状态原始值',
    current_staff                   STRING          COMMENT '当前服务客服id',
    company_id                      BIGINT          COMMENT '当前服务商ID',
    chat_type                       STRING          COMMENT '企微聊天类型 0-未定义 1-单聊 2-群聊'
) ENGINE=OLAP
PRIMARY KEY(`conversation_id`)
COMMENT "DWM层在线会话webim表"
DISTRIBUTED BY HASH(`conversation_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "conversation_id"
);

ALTER TABLE dwm_im_online_base_info ADD COLUMN (
conversation_ticket_count	int COMMENT 'MC会话工单个数',
ticket_count	int COMMENT 'MC工单个数',
status_name	string COMMENT '状态',
all_level_category	string COMMENT '所有分类',
fact_assign	string COMMENT '实派队列',
service_scene_level1_name	string COMMENT '一级归档',
service_scene_level2_name	string COMMENT '二级归档',
service_scene_level3_name	string COMMENT '三级归档',
service_scene_level4_name	string COMMENT '四级归档',
current_staff_user_name	string COMMENT '当前服务客服姓名',
staffs_user_names	string COMMENT '经手客服姓名',
current_staff_post	string COMMENT '当前服务客服岗位',
staffs	string COMMENT '经手客服'
);