alter table tdw_dwm_im_online_customer_service_backend_data
    add column record_update_time timestamp(6) without time zone default null,
    add column conversation_ticket_creator text default '',
    add column conversation_ticket_count int default 0,
    add column ticket_ids text default '',
    add column ticket_count int default 0,
    add column customer_type varchar(1024) default '',
    add column owner_uin varchar(256) default '',
    add column staff_title text default '',
    add column status_name varchar(256) default '',
    add column all_level_category varchar(1024) default '',
    add column category_level1_name varchar(1024) default '',
    add column category_level2_name varchar(1024) default '',
    add column category_level3_name varchar(1024) default '',
    add column is_line int default 0,
    add column line_cancel_duration int default 0,
    add column "first_fact_assign" varchar(1024) not null default '',
    add column fact_assign varchar(1024) default '',
    add column is_change_assign int default 0,
    add column service_scene_level1_name varchar(1024) default '',
    add column service_scene_level2_name varchar(1024) default '',
    add column service_scene_level3_name varchar(1024) default '',
    add column service_scene_level4_name varchar(1024) default '',
    add column current_staff varchar(1024) default '',
    add column current_staff_user_id varchar(1024) default '',
    add column current_staff_user_name varchar(1024) default '',
    add column staffs text default '',
    add column staffs_user_ids text default '',
    add column staffs_user_names text default '',
    add column customer_msg_cnt int default 0,
    add column staff_msg_cnt int default 0,
    add column response_total_duration int default 0,
    add column resonse_alarm_cnt int default 0,
    add column end_time timestamp(6) without time zone default null,
    add column appraise text default '',
    add column service_rate varchar(1024) default '',
    add column is_re_consult_24h int default 0,
    add column re_consult_24h text default '',
    add column is_re_consult_48h int default 0,
    add column re_consult_48h text default '',
    add column is_re_consult_same_question_24h int default 0,
    add column is_re_consult_same_question_48h int default 0,
    add column is_trans_second_line int default 0,
    add column first_trans_second_line_time timestamp(6) without time zone default null,
    add column second_line_first_response_time timestamp(6) without time zone default null,
    add column second_line_first_response_duration int default 0,
    add column second_line_first_fact_assign varchar(1024) default '',
    add column second_line_first_current_staff_user_name varchar(1024) default '',
    add column second_line_first_current_staff_user_name_architecture varchar(1024) null default '',
    add column current_staff_architecture varchar(1024) null default '',
    add column duration int default 0,
    add column first_line_duration int default 0,
    add column second_line_duration int default 0,
    add column first_line_msg_cnt int default 0,
    add column second_line_msg_cnt int default 0,
    add column first_line_overtime_responce_cnt int default 0,
    add column second_line_overtime_responce_cnt int default 0,
    add column close_time timestamp(6) without time zone default null,
    add column first_current_staff_user_name_post varchar(1024) default '',
    add column first_current_staff_user_name_architecture varchar(1024) null default '',
    add column first_line_last_staff varchar(1024) default '',
    add column first_line_last_staff_architecture varchar(1024) default '',
    add column second_line_first_should_assign varchar(1024) default '',
    add column second_line_last_staff varchar(1024) default '',
    add column second_line_last_staff_architecture varchar(1024) default '',
    add column current_staff_post varchar(1024) default '',
    add column staffs_post varchar(1024) default '',
    add column parent varchar(1024) default '',
    add column children varchar(1024) default '',
    add column children_num int default 0,
    add column creator_type varchar(1024) default '',
    add column finish_type varchar(1024) default '',
    add column first_fact_assign_time timestamp(6) without time zone default null,
    add column first_trans_second_line_assign varchar(1024) default '',
    add column first_trans_second_line_staff varchar(1024) default '',
    add column first_line_total_response_cnt int default 0,
    add column second_line_total_response_cnt int default 0,
    add column first_line_total_response_duration int default 0,
    add column second_line_total_response_duration int default 0,
    add column first_should_assign_id int default 0,
    add column should_assign_id int default 0,
    add column "unsatisfy_reason" text default '',
    add column "solve_status" varchar(1024) default '',
    add column chat_type varchar(1024) default '',
    add column conversation_service_rate varchar(1024) default '',
    add column conversation_unsatisfy_reason text default '',
    add column product_rate varchar(1024) default '',
    add column product_unsatisfy_reason text default '',
    add column company_id int default 0,
    add column recommend_score varchar(1024) default '',
    add column alarm_level int default 0,
    add column origin_status int default 0;