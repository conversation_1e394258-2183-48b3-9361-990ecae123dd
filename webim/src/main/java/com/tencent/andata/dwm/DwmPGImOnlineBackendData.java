package com.tencent.andata.dwm;

import static com.tencent.andata.sql.DwmImOnlineBackendDataSql.getInsertStatement;
import static com.tencent.andata.tablemap.DwmImOnlineBackendDataTableMap.starRocksTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.sql.DwmImOnlineBackendDataSql;
import com.tencent.andata.tablemap.DwmImOnlineBackendDataTableMap;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

@Builder
public class DwmPGImOnlineBackendData {

    private final String icebergDbName;
    private final String pgDbName;
    private final String parallelism;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        // 注册starrocks表
        // starrocks table mapping to flink table
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        // 注册PG表
        ArrayNode pgsqlSinkS360Table2FlinkTableMap = mapper.readValue(
                DwmImOnlineBackendDataTableMap.pgsqlSinkDataware2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(pgsqlDBConf, pgsqlSinkS360Table2FlinkTableMap, PGSQL, tEnv,"sink");

        // Dwd数据 去重
        Table dwmImOnlineBackendDataTable = tEnv.sqlQuery(
                DwmImOnlineBackendDataSql.DWM_IM_ONLINE_BACKEND_DATA_SQL
        );
        // 注册计算后的DWD表
        tEnv.createTemporaryView(
                "dwm_in_online_backend_data",
                dwmImOnlineBackendDataTable
        );
        // 入库
        flinkEnv.stmtSet().addInsertSql(
                getInsertStatement(
                        "pg_sink_tdw_dwm_im_online_customer_service_backend_data",
                        "dwm_in_online_backend_data"))
                .addInsertSql(insertIntoSql(
                        "dwm_in_online_backend_data",
                        "flink_starrocks_dwm_im_online_base_info",
                        tEnv.from("flink_starrocks_dwm_im_online_base_info"),
                        ROCKS
                        )
                );
    }
}
