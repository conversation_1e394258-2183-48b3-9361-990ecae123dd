package com.tencent.andata.dwm;

import com.tencent.andata.dct.api.source.kafka.builder.KafkaSourceStreamBuilder;
import com.tencent.andata.dct.api.source.kafka.deserializer.FlinkKafkaDeserializationSchema;
import com.tencent.andata.operator.DwmImOnlineProcess;
import com.tencent.andata.tablemap.DwmImOnlineBackendDataTableMap;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.api.Table;
import org.apache.iceberg.flink.TableLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Properties;

import static com.tencent.andata.sql.DwmImOnlineBackendDataSql.DWM_IM_BACKEND_DATA_SQL;
import static com.tencent.andata.sql.DwmImOnlineBackendDataSql.getInsertStatement;
import static com.tencent.andata.tablemap.DwmImOnlineBackendDataTableMap.starRocksTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;


@Builder
public class DwmImOnlineBackendDataKafkaToPG {
    private static final Logger logger = LoggerFactory.getLogger(DwmImOnlineBackendDataKafkaToPG.class);
    public static final JSONUtils jsonUtil = new JSONUtils();
    private final String icebergDbName;
    private final String pgDbName;
    private final String parallelism;
    private final ParameterTool parameterTool;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        DatabaseConf pcdcPsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", this.pgDbName))
                .build();

        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        ArrayNode pgsqlSinkS360Table2FlinkTableMap = mapper.readValue(
                DwmImOnlineBackendDataTableMap.pgsqlSinkDataware2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(pgsqlDBConf, pgsqlSinkS360Table2FlinkTableMap, PGSQL, tEnv,"sink");

        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        String groupMessageKafkaGroup = "mq.kafka.webim_online_fk";
        String topic = rainbowUtils.getStringValue(groupMessageKafkaGroup,"topic");
        String address = rainbowUtils.getStringValue(groupMessageKafkaGroup,"address");
        String groupID = rainbowUtils.getStringValue(groupMessageKafkaGroup,"group_id");
        // 注册Kafka数据源
        DataStream<String> kafkaStream = new KafkaSourceStreamBuilder()
                .setAddress(address)
                .setGroupID(groupID)
                .setTopic(topic)
                .setEnv(flinkEnv.env())
                .setDeserializationSchema(new FlinkKafkaDeserializationSchema())
                .setParallelism(Integer.parseInt(parallelism))
                .build();

        // Catalog Reader
        IcebergCatalogReader icebergCatalogReader = new IcebergCatalogReader();
        TableLoader dwdIMBackendTableLoader = icebergCatalogReader.getTableLoaderInstance(
                this.icebergDbName,
                "tdw_dwd_im_online_backend_data_model"
        );
        org.apache.iceberg.Table dwdIMBackendTable = dwdIMBackendTableLoader.loadTable();
        RowType backendTableRowType = icebergCatalogReader.getTableRowType(dwdIMBackendTable);
        JsonRowDataDeserializationSchema dwdIMBackendDeserializationSchema =
                new JsonRowDataDeserializationSchema(
                        backendTableRowType,
                        InternalTypeInfo.of(backendTableRowType),
                        false,
                        true,
                        TimestampFormat.SQL
                );

        // 将Kafka数据转换为RowData类型
        DataStream<RowData> odsIMBackendDS = kafkaStream.filter(json -> {
            return jsonUtil.getJSONObjectNodeByString(json).get("dst_table_name").asText()
                .equals("ods_im_online_customer_service_backend_data"); })
                .process(new DwmImOnlineProcess(pcdcPsqlDBConf, dwdIMBackendDeserializationSchema))
                .returns(dwdIMBackendDeserializationSchema.getProducedType());

        // 流转换为Table
        Table odsTable = tEnv.fromDataStream(odsIMBackendDS);
        tEnv.createTemporaryView("ods_im_online_backend_data_view", odsTable);
        Table dwmTable = tEnv.sqlQuery(DWM_IM_BACKEND_DATA_SQL);
        // 注册计算后的DWD表
        tEnv.createTemporaryView(
                "dwm_in_online_backend_data_view",
                dwmTable
        );

        flinkEnv.stmtSet().addInsertSql(
                getInsertStatement(
                        "pg_sink_tdw_dwm_im_online_customer_service_backend_data",
                        "dwm_in_online_backend_data_view"))
                .addInsertSql(insertIntoSql(
                                "dwm_in_online_backend_data_view",
                                "flink_starrocks_dwm_im_online_base_info",
                                tEnv.from("flink_starrocks_dwm_im_online_base_info"),
                                ROCKS
                        )
                );
    }
}
