package com.tencent.andata.sql;

public class DwmImOnlineBackendDataSql {
    public static String DWM_IM_BACKEND_DATA_SQL = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        *,\n"
            + "        row_number() over(partition by conversationid order by ftime desc,appraisetime desc) as rn\n"
            + "    from ods_im_online_backend_data_view\n"
            + ")\n"
            + "    select\n"
            + "        cast(record_update_time as timestamp) as record_update_time,"
            + "        now() as `dwm_create_time`,\n"
            + "        conversationid as conversation_id,\n"
            + "        COALESCE(conversationticketids,'') as conversation_ticket_ids,\n"
            + "        case when COALESCE(conversationticketids,'')='' then 0 "
            + "        else cardinality(get_array_set(conversationticketids, ',')) end as conversation_ticket_count,\n"
            + "        COALESCE(ticketids,'') as ticket_ids,\n"
            + "        case when COALESCE(ticketids,'')='' then 0 else "
            + "        cardinality(get_array_set(ticketids, ',')) end as ticket_count,\n"
            + "        source as source,\n"
            + "        customername as customer_name,\n"
            + "        `customertype` as customer_type,\n"
            + "        owneruin as owner_uin,\n"
            + "        uin as uin,\n"
            + "        uid as uid,\n"
            + "        regexp_replace(title,'\\u0000',' ') as title,\n"
            + "        regexp_replace(stafftitle,'\\u0000',' ') as staff_title,\n"
            + "        COALESCE(`status`,'-1') as status,\n"
            + "        case when status = '0' then '初建群' when status='1' then '智能客服服务中' "
            + "        when status='2' then '排队中' when status='3' then '待客服回复' when status='4' then '待客户回复' "
            + "        when status='5' then '待激活(用户超时未响应转待激活)' when status='6' then '已解决'"
            + "        when status='7' then '未解决' when status='8' then '超时结束（用户超时转结束）' "
            + "        when status='9' then '取消' when status='10' then '坐席主动退出了企业微信群聊' "
            + "        when status='11' then '待激活(用户超时未响应转待激活, 超过30分钟)' when status='12' then '结束' "
            + "        when status='13' then '待补充' when status='14' then '待确认结束' else status end as status_name,\n"
            + "        alllevelcategory as all_level_category,\n"
            + "        get_json_object(`alllevelcategory`, '$.first_level.name') as category_level1_name,\n"
            + "        get_json_object(`alllevelcategory`, '$.second_level.name') as category_level2_name, \n"
            + "        get_json_object(`alllevelcategory`, '$.third_level.name') as category_level3_name,\n"
            + "        fact_assign,\n"
            + "        simple_string_trans(`servicescene`, 0) as service_scene,\n"
            + "        service_scene_level1_name,\n"
            + "        service_scene_level2_name,\n"
            + "        service_scene_level3_name,\n"
            + "        service_scene_level4_name,\n"
            + "        currentstaff as current_staff,\n"
            + "        current_staff_user_id,\n"
            + "        current_staff_user_name,\n"
            + "        staffs,\n"
            + "        staffs_user_ids,\n"
            + "        staffs_user_names,\n"
            + "        long_to_sql_timestamp(cast(simple_string_trans(`createtime`, 0) as bigint) * 1000) "
            + "             as create_time,\n"
            + "        appraise as appraise,\n"
            + "        servicerate as service_rate,\n"
            + "        post as current_staff_post,\n"
            + "        parent as parent,\n"
            + "        `creatortype` as creator_type,\n"
            + "        `finishtype` as finish_type,\n"
            + "        simple_string_trans(`firstshouldassign`, 0) as first_should_assign_id,\n"
            + "        simple_string_trans(`shouldassign`, 0) as should_assign_id,\n"
            + "        simple_string_trans(`factassign`, 0) as fact_assign_id,\n"
            + "        unsatisfyreason as unsatisfy_reason,\n"
            + "        `solvestatus` as solve_status,\n"
            + "        chattype as chat_type,\n"
            + "        conversationservicerate as conversation_service_rate,\n"
            + "        conversationunsatisfyreason as conversation_unsatisfy_reason,\n"
            + "        productrate as product_rate,\n"
            + "        productunsatisfyreason as product_unsatisfy_reason,\n"
            + "        simple_string_trans(companyid,0) as company_id,\n"
            + "        recommendscore as recommend_score,\n"
            + "        simple_string_trans(alarmlevel,0) as alarm_level,\n"
            + "        simple_string_trans(status,0) as origin_status\n"
            + "    from tmp1\n"
            + "    where rn = 1\n";
    public static String DWM_IM_ONLINE_BACKEND_DATA_SQL = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        *,\n"
            + "        row_number() over(partition by conversation_id order by ftime desc,appraise_time desc) as rn\n"
            + "    from dwd_im_online_backend_data\n"
            + ")\n"
            + "select\n"
            + "     conversation_id,\n"
            + "     long_to_sql_timestamp(CAST((UNIX_TIMESTAMP()*1000) AS BIGINT)) AS dwm_create_time,\n"
            + "     record_update_time,\n"
            + "     COALESCE(conversation_ticket_ids,'') as conversation_ticket_ids,\n"
            + "     COALESCE(ticket_ids,'') as ticket_ids,\n"
            + "     cast(customer_type as string) as customer_type,\n"
            + "     owner_uin,\n"
            + "     uin,\n"
            + "     uid,\n"
            + "     category_level1_name,\n"
            + "     category_level2_name,\n"
            + "     category_level3_name,\n"
            + "     customer_name,\n"
            + "     source,\n"
            + "     title,\n"
            + "     staff_title,\n"
            + "     status,\n"
            + "     create_time,\n"
            + "     first_should_assign_id,\n"
            + "     should_assign_id,\n"
            + "     fact_assign_id,\n"
            + "     service_scene,\n"
            + "     staffs as staffs_user_ids,\n"
            + "     cast(solve_status as string) as solve_status,\n"
            + "     appraise,\n"
            + "     service_rate,\n"
            + "     unsatisfy_reason,\n"
            + "     conversation_service_rate,\n"
            + "     conversation_unsatisfy_reason,\n"
            + "     product_rate,\n"
            + "     product_unsatisfy_reason,\n"
            + "     recommend_score,\n"
            + "     parent,\n"
            + "     cast(creator_type as string) as creator_type,\n"
            + "     cast(finish_type as string) as finish_type,\n"
            + "     cast(alarm_level as int) as alarm_level,\n"
            + "     cast(origin_status as int) as origin_status,\n"
            + "     current_staff,\n"
            + "     cast(company_id as int) as company_id,\n"
            + "     chat_type,\n"
            + "     0 as conversation_ticket_count,\n"
            + "     0 as ticket_count,\n"
            + "     '' as all_level_category,\n"
            + "     '' as fact_assign,\n"
            + "     '' as service_scene_level1_name,\n"
            + "     '' as service_scene_level2_name,\n"
            + "     '' as service_scene_level3_name,\n"
            + "     '' as service_scene_level4_name,\n"
            + "     '' as current_staff_user_id,\n"
            + "     '' as current_staff_user_name,\n"
            + "     '' as staffs_user_names,\n"
            + "     '' as current_staff_post,\n"
            + "     '' as staffs,\n"
            + "     '' as status_name\n"
            + "from tmp1\n"
            + "where rn = 1";

    /**
     * 获取 pginsert 语句
     * @param pgSinkTableName PG TABLE Name
     * @param view flink view
     * @return pginsert 语句
     */
    public static String getInsertStatement(String pgSinkTableName, String view) {
        return String.format("INSERT INTO " + pgSinkTableName
                + " SELECT"
                + "     `dwm_create_time`,"
                + "     `conversation_id`,"
                + "     `conversation_ticket_ids`,"
                + "     `conversation_ticket_count`,"
                + "     `ticket_ids`,"
                + "     `ticket_count`,"
                + "     `source`,"
                + "     `customer_name`,"
                + "     `customer_type`,"
                + "     `owner_uin`,"
                + "     `uin`,"
                + "     `uid`,"
                + "     `title`,"
                + "     `staff_title`,"
                + "     `status`,"
                + "     `status_name`,"
                + "     `all_level_category`,"
                + "     `category_level1_name`,"
                + "     `category_level2_name`,"
                + "     `category_level3_name`,"
                + "     `fact_assign`,"
                + "     `service_scene`,"
                + "     `service_scene_level1_name`,"
                + "     `service_scene_level2_name`,"
                + "     `service_scene_level3_name`,"
                + "     `service_scene_level4_name`,"
                + "     `current_staff`,"
                + "     `current_staff_user_id`,"
                + "     `current_staff_user_name`,"
                + "     `staffs_user_ids`,"
                + "     `staffs_user_names`,"
                + "     `create_time`,"
                + "     `appraise`,"
                + "     `service_rate`,"
                + "     `current_staff_post`,"
                + "     `staffs`,"
                + "     `parent`,"
                + "     `creator_type`,"
                + "     `finish_type`,"
                + "     `first_should_assign_id`,"
                + "     `should_assign_id`,"
                + "     `fact_assign_id`,"
                + "     `unsatisfy_reason`,"
                + "     `solve_status`,"
                + "     `chat_type`,"
                + "     `conversation_service_rate`,"
                + "     `conversation_unsatisfy_reason`,"
                + "     `product_rate`,"
                + "     `product_unsatisfy_reason`,"
                + "     `company_id`,"
                + "     `recommend_score`,"
                + "     `alarm_level`,"
                + "     `origin_status`,"
                + "     `record_update_time`"
                + "FROM %s", view);
    }
}
