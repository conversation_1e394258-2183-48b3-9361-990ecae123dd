package com.tencent.andata.sql;

public class DwdImOnlineBackendDataSql {
    public static String DWD_IM_ONLINE_BACKEND_DATA_SQL = ""
            + "    select\n"
            + "        long_to_sql_timestamp(CAST((UNIX_TIMESTAMP()*1000) AS BIGINT)) AS `dwd_create_time`,\n"
            + "        ftime,\n"
            + "        value_of_primary_key,\n"
            + "        string_to_sql_timestamp(`record_update_time`) as record_update_time,\n"
            + "        requestid as request_id,\n"
            + "        rpc_name as operation,\n"
            + "        ConversationId as conversation_id,\n"
            + "        OwnerUin as owner_uin,\n"
            + "        Uin as uin,\n"
            + "        Uid as uid,\n"
            + "        Source as source,\n"
            + "        Status as status,\n"
            + "        simple_string_trans(`CategoryId`, 0) as category_id,\n"
            + "        simple_string_trans(`FirstShouldAssign`, 0) as first_should_assign_id,\n"
            + "        simple_string_trans(`ShouldAssign`, 0) as should_assign_id,\n"
            + "        simple_string_trans(`FactAssign`, 0) as fact_assign_id,\n"
            + "        simple_string_trans(`ServiceScene`, 0) as service_scene,\n"
            + "        CurrentStaff as current_staff,\n"
            + "        Staffs as staffs,\n"
            + "        Appraise as appraise,\n"
            + "        ServiceRate as service_rate,\n"
            + "        UnsatisfyReason as unsatisfy_reason,\n"
            + "        long_to_sql_timestamp(CAST(simple_string_trans(`AppraiseTime`, 0) AS BIGINT) * 1000) "
            + "             as appraise_time,\n"
            + "        long_to_sql_timestamp(CAST(simple_string_trans(`CreateTime`, 0) AS BIGINT) * 1000) "
            + "             as create_time,\n"
            + "        long_to_sql_timestamp("
            + "             CAST(simple_string_trans(`CustomerUpdatedTime`, 0) AS BIGINT) * 1000"
            + "         ) as customer_updated_time,\n"
            + "        long_to_sql_timestamp(CAST(simple_string_trans(`StaffUpdatedTime`, 0) AS BIGINT) * 1000) "
            + "             as staff_updated_time,\n"
            + "        TicketIds as ticket_ids,\n"
            + "        ConversationTicketIds as conversation_ticket_ids,\n"
            + "        Title as title,\n"
            + "        AllLevelCategory as all_level_category,\n"
            + "        long_to_sql_timestamp("
            + "             CAST(simple_string_trans(`CustomerFirstUpdatedTime`, 0) AS BIGINT) * 1000"
            + "         ) as customer_first_updated_time,\n"
            + "        long_to_sql_timestamp("
            + "             CAST(simple_string_trans(`StaffFirstUpdatedTime`, 0) AS BIGINT) * 1000"
            + "         ) as staff_first_updated_time,\n"
            + "        case\n"
            + "             when IsAlarm = 'true' then 1\n"
            + "             else 0\n"
            + "        end as is_alarm,\n"
            + "        simple_string_trans(`SolveStatus`, 0) as solve_status,\n"
            + "        CustomerName as customer_name,\n"
            + "        simple_string_trans(`IsClean`, 0) as is_clean,\n"
            + "        simple_string_trans(`CustomerType`, 0) as customer_type,\n"
            + "        long_to_sql_timestamp(CAST(simple_string_trans(`FinishTime`, 0) AS BIGINT) * 1000) "
            + "             as finish_time,\n"
            + "        StaffTitle as staff_title,\n"
            + "        get_json_object(`AllLevelCategory`, '$.first_level.name') AS category_level1_name,\n"
            + "        get_json_object(`AllLevelCategory`, '$.second_level.name') AS category_level2_name, \n"
            + "        get_json_object(`AllLevelCategory`, '$.third_level.name') AS category_level3_name,\n"
            + "        IsTransferred as is_transferred,\n"
            + "        ChatType as chat_type,\n"
            + "        msgdata as msg_data,\n"
            + "        ConversationServiceRate as conversation_service_rate,\n"
            + "        IsTicketCreated as is_ticket_created,\n"
            + "        ConversationUnsatisfyReason as conversation_unsatisfy_reason,\n"
            + "        ProductRate as product_rate,\n"
            + "        ProductUnsatisfyReason as product_unsatisfy_reason,\n"
            + "        CompanyId as company_id,\n"
            + "        RecommendScore as recommend_score,\n"
            + "        Post as post,\n"
            + "        Parent as parent,\n"
            + "        simple_string_trans(`CreatorType`, 0) as creator_type,\n"
            + "        Finisher as finisher,\n"
            + "        simple_string_trans(`FinishType`, 0) as finish_type,\n"
            + "        long_to_sql_timestamp(CAST(simple_string_trans(`ApplyFinishTime`, 0) AS BIGINT) * 1000) "
            + "             as apply_finish_time,\n"
            + "        Creator as creator,\n"
            + "        long_to_sql_timestamp("
            + "             CAST(simple_string_trans(`AwaitingSupplementTime`, 0) AS BIGINT) * 1000"
            + "         ) as awaiting_supplement_time,\n"
            + "        Contact as contact,\n"
            + "        AlarmLevel as alarm_level,\n"
            + "        Status as origin_status,\n"
            + "        msgseq as msg_seq,\n"
            + "        senderid as sender_id\n"
            + "    from f_ods_im_online_backend_data\n";

    public static String DIM_CUSTOMER_STAFF_INFO = ""
            + "select \n"
            + "    uid,\n"
            + "    user_id,\n"
            + "    user_name,\n"
            + "    company_id,\n"
            + "    group_id,\n"
            + "    assign_company_id\n"
            + "from iceberg_source_dim_customer_staff_info/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n";

    public static String DIM_INCIDENT_DUTY = ""
            + "select \n"
            + "    duty_id,\n"
            + "    duty_name\n"
            + "from iceberg_dim_duty /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/";
}
