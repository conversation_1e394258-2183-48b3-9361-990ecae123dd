package com.tencent.andata.tablemap;

public class DwmImOnlineBackendDataTableMap {
    public static String starRocksTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_im_online_base_info\",\n"
            + "        \"fTable\":\"flink_starrocks_dwm_im_online_base_info\",\n"
            + "        \"primaryKey\": \"conversation_id\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlSinkDataware2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"tdw_dwm_im_online_customer_service_backend_data\",\n"
            + "        \"fTable\":\"pg_sink_tdw_dwm_im_online_customer_service_backend_data\",\n"
            + "        \"fSchema\":\"(dwm_create_time timestamp,conversation_id string,conversation_ticket_ids string,"
            + " conversation_ticket_count int,ticket_ids string,ticket_count int,source string,customer_name string,"
            + " customer_type string,owner_uin string,uin string,uid string,title string,staff_title string,"
            + " status string,status_name string,all_level_category string,category_level1_name string,"
            + " category_level2_name string,category_level3_name string,fact_assign string,"
            + " service_scene bigint,service_scene_level1_name string,service_scene_level2_name string,"
            + " service_scene_level3_name string,service_scene_level4_name string,current_staff string,"
            + " current_staff_user_id string,current_staff_user_name string,staffs_user_ids string,"
            + " staffs_user_names string,create_time timestamp,appraise string,service_rate string,"
            + " current_staff_post string,staffs string,parent string,creator_type string,finish_type string,"
            + " first_should_assign_id int,should_assign_id int,fact_assign_id int,unsatisfy_reason string,"
            + " solve_status string,chat_type string,conversation_service_rate string,"
            + " conversation_unsatisfy_reason string,product_rate string,product_unsatisfy_reason string,"
            + " company_id int,recommend_score string,alarm_level int,origin_status int,"
            + " record_update_time timestamp,PRIMARY KEY (conversation_id) NOT ENFORCED)\"\n"
            + "    }\n"
            + "]";
}
