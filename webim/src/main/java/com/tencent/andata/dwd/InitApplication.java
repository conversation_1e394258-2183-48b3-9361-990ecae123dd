package com.tencent.andata.dwd;

import com.tencent.andata.tablemap.DwdImOnlineBackendDataTableMap;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.udf.GetJsonObject;
import com.tencent.andata.utils.udf.LongTimestampTransform;
import com.tencent.andata.utils.udf.SimpleStringTransform;
import com.tencent.andata.utils.udf.SqlTimestampTransform;
import com.tencent.andata.utils.udf.StrToTimestamp;
import com.tencent.andata.utils.udf.StringTimestampTransform;
import com.tencent.andata.utils.udf.StringToArray;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

@Builder
public class InitApplication {
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        ObjectMapper mapper = new ObjectMapper();
        // 注册iceberg ods表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(
                        DwdImOnlineBackendDataTableMap.icebergTable2FlinkTable,
                        ArrayNode.class
                ),
                tEnv,
                catalog
        );
        // 初始化UDF
        tEnv.createFunction("to_timestamp", StrToTimestamp.class);
        tEnv.createFunction("string_to_long_timestamp", StringTimestampTransform.StringToLong.class);
        tEnv.createFunction("string_to_sql_timestamp", StringTimestampTransform.StringToSqlTimestamp.class);
        tEnv.createFunction("long_to_string_timestamp", LongTimestampTransform.LongToString.class);
        tEnv.createFunction("long_to_sql_timestamp", LongTimestampTransform.LongToSqlTimestamp.class);
        tEnv.createFunction("sql_to_long_timestamp", SqlTimestampTransform.SqlToLong.class);
        tEnv.createFunction("simple_string_trans", SimpleStringTransform.class);
        tEnv.createFunction("get_json_object", GetJsonObject.class);
        tEnv.createFunction("get_array_set", StringToArray.class);
    }
}
