package com.tencent.andata.dwd;

import com.tencent.andata.sql.DwdImOnlineBackendDataSql;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseEnum;
import lombok.Builder;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.source.FlinkSource;


@Builder
public class DwdImOnlineBackendData {
    private final String icebergDbName;
    private final String pgDbName;
    private final long startSnapshot;
    private final Boolean streaming;
    private final String parallelism;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 获取ODS Iceberg表
        org.apache.iceberg.Table table = catalog.getTableInstance(
                icebergDbName, "ods_im_online_customer_service_backend_data"
        );
        TableLoader tableLoader = catalog.getTableLoaderInstance(
                icebergDbName, "ods_im_online_customer_service_backend_data"
        );
        DataStream<RowData> dataStream;
        if (streaming) {
            // 获取Streaming数据
            dataStream = FlinkSource
                    .forRowData()
                    .env(flinkEnv.env())
                    .startSnapshotId(startSnapshot)
                    .table(table)
                    .tableLoader(tableLoader)
                    .streaming(true)
                    .build();
        } else {
            Configuration configuration = new Configuration();
            configuration.setString("table.exec.resource.default-parallelism", parallelism);
            configuration.setString("table.exec.iceberg.infer-source-parallelism.max", parallelism);
            // 批处理
            dataStream = FlinkSource
                    .forRowData()
                    .env(flinkEnv.env())
                    .table(table)
                    .tableLoader(tableLoader)
                    .flinkConf(configuration)
                    .build();
        }

        // 流转表
        Table odsTable = tEnv.fromDataStream(dataStream);
        tEnv.createTemporaryView("f_ods_im_online_backend_data", odsTable);
        // ODS数据计算 & 去重
        Table dwdImOnlineBackendDataTable = tEnv.sqlQuery(
                DwdImOnlineBackendDataSql.DWD_IM_ONLINE_BACKEND_DATA_SQL
        );
        org.apache.iceberg.Table dwdIcebergTable = catalog.getTableInstance(
                icebergDbName,
                "dwd_im_online_customer_service_backend_data"
        );
        // 注册计算后的DWD表
        tEnv.createTemporaryView(
                "dwd_im_online_backend_data",
                dwdImOnlineBackendDataTable
        );
        // 注册DWD Iceberg表
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(dwdIcebergTable)
                                        .primaryKeyList(
                                                new String[]{"value_of_primary_key", "ftime"}
                                        )
                        )
                        .flinkTableName("iceberg_sink_dwd_im_online_backend_data")
                        .build()
        );
        // 入库
        flinkEnv.stmtSet().addInsertSql(
                TableUtils.insertIntoSql(
                        "dwd_im_online_backend_data",
                        "iceberg_sink_dwd_im_online_backend_data",
                        tEnv.from("iceberg_sink_dwd_im_online_backend_data"),
                        DatabaseEnum.ICEBERG
                )
        );
    }
}
