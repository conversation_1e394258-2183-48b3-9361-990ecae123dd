package com.tencent.andata.operator;

import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class DwdImOnlineBackendDataTransformer {
    public static class StaffInfoProcessFunction extends BroadcastProcessFunction<Tuple2<Boolean, Row>, Row, Row> {

        private final MapStateDescriptor<String, String> staffInfoStateDesc = new MapStateDescriptor<>(
                "staffInfoBroadcastState", BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO);


        @Override
        public void processElement(
                Tuple2<Boolean, Row> booleanRowTuple2,
                BroadcastProcessFunction<Tuple2<Boolean, Row>, Row, Row>.ReadOnlyContext ctx,
                Collector<Row> out
        ) throws Exception {
            if (!booleanRowTuple2.f0) {
                return;
            }
            Row row = booleanRowTuple2.f1;
            // 获取state
            ReadOnlyBroadcastState<String, String> staffNameMap = ctx.getBroadcastState(staffInfoStateDesc);
            // 获取当前处理人ID
            String currentStaff = row.getFieldAs("current_staff");
            // 获取当前处理人名字
            String currentStaffUserName = staffNameMap.get(currentStaff);
            // 设置值
            row.setField("current_staff_user_id", currentStaffUserName);
            row.setField("current_staff_user_name", currentStaffUserName);
            // 获取经手人
            String staffs = row.getFieldAs("staffs");
            List<String> staffNameList = new ArrayList<String>();
            List<String> staffIdList = Arrays.stream(staffs.split(",")).collect(Collectors.toList());
            staffIdList.forEach(id -> {
                try {
                    staffNameList.add(staffNameMap.get(id));
                } catch (Exception ignored) {
                }
            });
            // 设置值
            row.setField("staffs_user_ids", String.join(",", staffNameList));
            row.setField("staffs_user_names", String.join(",", staffNameList));
            out.collect(row);
        }

        @Override
        public void processBroadcastElement(
                Row row,
                BroadcastProcessFunction<Tuple2<Boolean, Row>, Row, Row>.Context ctx,
                Collector<Row> collector
        ) throws Exception {
            // 写入维表数据
            ctx.getBroadcastState(staffInfoStateDesc)
                    .put(row.getFieldAs("uid"), row.getFieldAs("user_name"));
        }
    }
}
