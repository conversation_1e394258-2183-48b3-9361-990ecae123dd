package com.tencent.andata.operator;

import com.tencent.andata.utils.lookup.exception.QueryException;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.tencent.andata.dwm.DwmImOnlineBackendDataKafkaToPG.jsonUtil;

public class DwmImOnlineProcess extends ProcessFunction<String, RowData> {
    private static final Logger logger = LoggerFactory.getLogger(DwmImOnlineProcess.class);
    private final ConcurrentHashMap<String, HashMapJDBCLookupQuery> hashMapJDBCQueryMap = new ConcurrentHashMap<>();

    private static final DateTimeFormatter dateFormat =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS'Z'");
    private final JsonRowDataDeserializationSchema schema;
    private final DatabaseConf dbConf;

    public DwmImOnlineProcess(DatabaseConf dbConf, JsonRowDataDeserializationSchema schema) {
        this.schema = schema;
        this.dbConf = dbConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        DwmImOnlineTableQuery query = new DwmImOnlineTableQuery();
        addQueryToMap("dim_service_scenes", query.getServiceSceneQuery());
        addQueryToMap("dim_customer_staff_info", query.getStaffQuery());
        addQueryToMap("dim_incident_duty", query.getAssignNameQuery());
    }

    private void addQueryToMap(String key, JDBCSqlBuilderImpl jdbcSqlBuilder) throws ValidationException {
        hashMapJDBCQueryMap.put(key, new HashMapJDBCLookupQuery(DatabaseEnum.PGSQL, dbConf, jdbcSqlBuilder));
    }

    private TimestampData parseTimestamp(String timeString) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(timeString, dateFormat);
            return TimestampData.fromEpochMillis(dateTime.toInstant(ZoneOffset.UTC).toEpochMilli());
        } catch (Exception e) {
            logger.error("timeString is error: {}", timeString, e);
            return null;
        }
    }

    private Map<String, String> queryDatabase(String tableName, String key, Object value) throws QueryException {
        List<HashMap<String, Object>> result = hashMapJDBCQueryMap.get(tableName).query(new HashMap<String, Object>() {{
            put(key, value);
        }});
        Map<String, String> resultMap = new HashMap<>();
        if (!result.isEmpty()) {
            result.get(0).forEach((k, v) -> resultMap.put(k, v != null ? v.toString() : ""));
        }
        return resultMap;
    }

    private Map<String, String> queryServiceSceneNames(int serviceSceneId) throws QueryException {
        return queryDatabase("dim_service_scenes", "dim_id", serviceSceneId);
    }

    private Map<String, String> queryStaffInfo(String staffId) throws QueryException {
        return queryDatabase("dim_customer_staff_info", "uid", staffId);
    }

    private String queryStaffNames(String[] staffArray, String fieldName) throws QueryException {
        StringBuffer result = new StringBuffer();
        for (int i = 0; i < staffArray.length; i++) {
            String staffId = staffArray[i];
            Map<String, String> staffInfo = queryStaffInfo(staffId);
            if (staffInfo.containsKey(fieldName)) {
                result.append(staffInfo.get(fieldName));
                if (i < staffArray.length - 1) {
                    result.append(",");
                }
            }
        }
        return result.toString();
    }

    private String queryFactAssign(int factAssignId) throws QueryException {
        Map<String, String> result = queryDatabase("dim_incident_duty", "duty_id", factAssignId);
        return result.getOrDefault("duty_name", "");
    }

    @Override
    public void processElement(String json,
                               ProcessFunction<String, RowData>.Context context,
                               Collector<RowData> collector) throws Exception {
        ObjectNode jsonl1 = jsonUtil.getJSONObjectNodeByString(json);
        ObjectNode flatten =
                jsonUtil.flatten(jsonUtil.getJsonNodeByString(jsonl1.get("message").asText()), true);
        String serviceSceneLevel1Name = "";
        String serviceSceneLevel2Name = "";
        String serviceSceneLevel3Name = "";
        String serviceSceneLevel4Name = "";
        if (flatten.has("servicescene")) {
            Map<String, String> serviceSceneNames = queryServiceSceneNames(flatten.get("servicescene").asInt());
            serviceSceneLevel1Name = serviceSceneNames.getOrDefault("service_scene_level1_name", "");
            serviceSceneLevel2Name = serviceSceneNames.getOrDefault("service_scene_level2_name", "");
            serviceSceneLevel3Name = serviceSceneNames.getOrDefault("service_scene_level3_name", "");
            serviceSceneLevel4Name = serviceSceneNames.getOrDefault("service_scene_level4_name", "");
        }
        String currentStaffUserName = "";
        String currentStaffUserId = "";
        if (flatten.has("currentstaff")) {
            Map<String, String> currentStaffInfo = queryStaffInfo(flatten.get("currentstaff").asText());
            currentStaffUserId = currentStaffInfo.getOrDefault("user_id", "");
            currentStaffUserName = currentStaffInfo.getOrDefault("user_name", "");
        }
        String staffsUserNames = "";
        String staffsUserIds = "";
        if (flatten.has("staffs")) {
            String[] staffArray = flatten.get("staffs").asText().split(",");
            staffsUserNames = queryStaffNames(staffArray, "user_name");
            staffsUserIds = queryStaffNames(staffArray, "user_id");
        }
        String factAssign = "";
        if (flatten.has("factassign")) {
            factAssign = queryFactAssign(flatten.get("factassign").asInt());
        }
        RowData record = schema.convertToRowData(flatten);
        GenericRowData genericRowData = (GenericRowData) record;
        genericRowData.setField(0, parseTimestamp(jsonl1.get("mq_msg_publish_time").asText()));
        genericRowData.setField(1, StringData.fromString(serviceSceneLevel1Name));
        genericRowData.setField(2, StringData.fromString(serviceSceneLevel2Name));
        genericRowData.setField(3, StringData.fromString(serviceSceneLevel3Name));
        genericRowData.setField(4, StringData.fromString(serviceSceneLevel4Name));
        genericRowData.setField(5, StringData.fromString(currentStaffUserName));
        genericRowData.setField(6, StringData.fromString(currentStaffUserId));
        genericRowData.setField(7, StringData.fromString(staffsUserNames));
        genericRowData.setField(8, StringData.fromString(staffsUserIds));
        genericRowData.setField(9, StringData.fromString(factAssign));
        collector.collect(genericRowData);
    }
}
