package com.tencent.andata.operator;

import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DwmImOnlineTableQuery {
    /**
     * 获取ServiceSceneJDBC
     * @return JDBCSqlBuilderImpl
     * @throws Exception 异常
     */
    public JDBCSqlBuilderImpl getServiceSceneQuery() {
        Map<String, String> fieldAliasMap = new HashMap<>();
        fieldAliasMap.put("service_scene_level1_name", "service_scene_level1_name");
        fieldAliasMap.put("service_scene_level2_name", "service_scene_level2_name");
        fieldAliasMap.put("service_scene_level3_name", "service_scene_level3_name");
        fieldAliasMap.put("service_scene_level4_name", "service_scene_level4_name");

        List<String> conditionKeyList = new ArrayList<>();
        conditionKeyList.add("dim_id");
        // DB conf
        return JDBCSqlBuilderImpl.builder()
                .tableName("dim_service_scenes")
                .selectFieldWithAlias(fieldAliasMap)
                .conditionKeyList(conditionKeyList)
                .databaseEnum(DatabaseEnum.PGSQL)
                .limit(1);
    }

    public JDBCSqlBuilderImpl getStaffQuery() {
        Map<String, String> fieldAliasMap = new HashMap<>();
        fieldAliasMap.put("user_id", "user_id");
        fieldAliasMap.put("user_name", "user_name");

        List<String> conditionKeyList = new ArrayList<>();
        conditionKeyList.add("uid");
        // DB conf
        return JDBCSqlBuilderImpl.builder()
                .tableName("dim_customer_staff_info")
                .selectFieldWithAlias(fieldAliasMap)
                .conditionKeyList(conditionKeyList)
                .databaseEnum(DatabaseEnum.PGSQL)
                .limit(1);
    }


    public JDBCSqlBuilderImpl getAssignNameQuery() {
        Map<String, String> fieldAliasMap = new HashMap<>();
        fieldAliasMap.put("duty_name", "duty_name");

        List<String> conditionKeyList = new ArrayList<>();
        conditionKeyList.add("duty_id");
        // DB conf
        return JDBCSqlBuilderImpl.builder()
                .tableName("dim_incident_duty")
                .selectFieldWithAlias(fieldAliasMap)
                .conditionKeyList(conditionKeyList)
                .databaseEnum(DatabaseEnum.PGSQL)
                .limit(1);
    }

}
