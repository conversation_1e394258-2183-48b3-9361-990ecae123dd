-- 废弃
create table dwd_tapd_story_fsm_service_relation
(
    dwd_id               serial primary key,
    dwd_is_valid         boolean       not null         default true,
    value_of_primary_key varchar(1024) not null         default '',
    dwd_create_time      timestamp(6) without time zone default (now() at time zone ('utc-8')),
    record_update_time   timestamp(6) without time zone default (now() at time zone ('utc-8')),

    "id"                 bigint,
    title                varchar(1024) not null         default '',
    story_id             bigint,
    channel              bigint,
    item_id              varchar(1024) not null         default '',
    item_title           varchar(1024) not null         default '',
    create_by            varchar(1024) not null         default '',
    create_at            timestamp(6) without time zone default null,
    uin                  bigint,
    customer_name        varchar(1024) not null         default '',
    customer_class       varchar(1024) not null         default '',
    grade                bigint,
    expected_time        timestamp(6) without time zone default null,
    dst_story_id         bigint,
    status               bigint
);


alter table dwd_tapd_story_fsm_service_relation owner to andata;

create index idx_dwd_tapd_story_fsm_service_relation_vpky on dwd_tapd_story_fsm_service_relation(value_of_primary_key);
create index idx_dwd_tapd_story_fsm_service_relation_dwd_id on dwd_tapd_story_fsm_service_relation(dwd_id);
create index idx_dwd_tapd_story_fsm_service_relation_time on dwd_tapd_story_fsm_service_relation(record_update_time);

comment on table dwd_tapd_story_fsm_service_relation is '需求信息';

comment on column dwd_tapd_story_fsm_service_relation.value_of_primary_key is '消息主键';
comment on column dwd_tapd_story_fsm_service_relation.id is '唯一性主键';
comment on column dwd_tapd_story_fsm_service_relation.title is '标题';
comment on column dwd_tapd_story_fsm_service_relation.status is '关联状态，0，正常，1：已解除关联';
comment on column dwd_tapd_story_fsm_service_relation.create_by is '操作人';
comment on column dwd_tapd_story_fsm_service_relation.create_at is '关联时间';
comment on column dwd_tapd_story_fsm_service_relation.dst_story_id is '源需求单被关联时，被关联到的需求单id，方便查询';
comment on column dwd_tapd_story_fsm_service_relation.channel is '被哪个需求关联,0未知；1服务请求；2 事件单；3用户之声；4 产品体验；5竞品分析；6开发者群；7调研问卷';
comment on column dwd_tapd_story_fsm_service_relation.story_id is '需求单id';
comment on column dwd_tapd_story_fsm_service_relation.item_id is '关联来源的唯一标识，比如工单id等';
comment on column dwd_tapd_story_fsm_service_relation.item_title is '单据标题';
comment on column dwd_tapd_story_fsm_service_relation.uin is '客户uin';
comment on column dwd_tapd_story_fsm_service_relation.customer_name is '客户名称';
comment on column dwd_tapd_story_fsm_service_relation.customer_class is '客户等级';
comment on column dwd_tapd_story_fsm_service_relation.grade is '重要性等级：1.一般 2.重要';
comment on column dwd_tapd_story_fsm_service_relation.expected_time is '预期上线时间';

-- 增加字段(2021-12-28 jingzhenhe)
alter table dwd_tapd_story_fsm_service_relation add column way_by int;
comment on column dwd_tapd_story_fsm_service_relation.way_by is '创建方式，0，从其它渠道创建，1： 手动直接创建';

-- add by baymaxxu 2022-03-22
alter table dwd_tapd_story_fsm_service_relation
    add column customer_category varchar(1024) not null default '',
    add column create_by_id varchar(1024) not null default '';
comment on column dwd_tapd_story_fsm_service_relation.customer_category is '客户分类，中长尾、大客户';
comment on column dwd_tapd_story_fsm_service_relation.create_by_id is '创建人的andon用户id';