package dbus;

import com.tencent.andata.utils.rowdata.RowDataConverter;
import java.io.IOException;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.junit.Before;
import org.junit.Test;

import static dbus.utils.TestDataWith2Col.getPreConstructRowData;
import static dbus.utils.TestDataWith2Col.preConstructF1;
import static dbus.utils.TestDataWith2Col.preConstructF2;
import static dbus.utils.TestDataWith2Col.preConstructRowKind;
import static dbus.utils.TestDataWith2Col.preConstructRowType;
import static org.junit.Assert.assertEquals;

public class RowDataConverterTest {
    GenericRowData preConstructRowData = getPreConstructRowData();

    @Before
    public void construct() {
    }

    @Test
    public void testRowDataConverterWithoutDataCompress() throws IOException {
        RowDataConverter converter = new RowDataConverter(preConstructRowType, false);
        byte[] bytes = converter.serializeRowDataToBytes(preConstructRowData);
        assertRowDataEqual(converter.deserializeBytesToGenericRowData(bytes));
    }

    @Test
    public void testRowDataConverterWithDataCompress() throws IOException {
        RowDataConverter converter = new RowDataConverter(preConstructRowType, true);
        byte[] bytes = converter.serializeRowDataToBytes(preConstructRowData);
        assertRowDataEqual(converter.deserializeBytesToGenericRowData(bytes));
    }

    public void assertRowDataEqual(RowData desRowData) {
        assertEquals(desRowData.getRowKind().toByteValue(), preConstructRowKind.toByteValue());
        assertEquals(desRowData.getInt(0), preConstructF1.intValue());
        assertEquals(desRowData.getString(1).toString(), preConstructF2.toString());
    }
}