package dbus.utils;

import com.tencent.andata.utils.meta.IcebergTableMeta;
import com.tencent.andata.utils.speedlayer.LocalCacheSpeedLayer;
import org.apache.iceberg.catalog.TableIdentifier;
import org.junit.Assert;
import org.junit.Test;

public class SpeedLayerTest {

    @Test
    public void LocalCacheSpeedLayerTest() {
        LocalCacheSpeedLayer localCacheSpeedLayer = LocalCacheSpeedLayer.builder().build();
        TableIdentifier tableIdentifier = TableIdentifier.of("andata", "test_tb");
        IcebergTableMeta buildMeta = IcebergTableMeta.builder()
                .database("andata")
                .tableName("test_tb")
                .build();
        localCacheSpeedLayer.setIcebergTableMeta(
                tableIdentifier,
                buildMeta
        );
        IcebergTableMeta icebergTableMeta = localCacheSpeedLayer
                .getIcebergTableMeta(TableIdentifier.of("andata", "test_tb"));
        Assert.assertEquals(icebergTableMeta, buildMeta);
    }
}
