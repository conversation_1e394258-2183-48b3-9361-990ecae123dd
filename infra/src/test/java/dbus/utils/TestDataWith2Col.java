package dbus.utils;

import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.IntType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.VarCharType;
import org.apache.flink.types.RowKind;

public class TestDataWith2Col {
    public static final RowType preConstructRowType = RowType.of(
            new LogicalType[]{new IntType(), new VarCharType()},
            new String[]{"col1", "col2"}
    );
    public static final RowType preConstructNewRowType = RowType.of(
            new LogicalType[]{new IntType(), new VarCharType(), new VarCharType()},
            new String[]{"col1", "col2","col3"}
    );
    public static final RowKind preConstructRowKind = RowKind.INSERT;
    public static final Integer preConstructF1 = Integer.valueOf(1);
    public static final StringData preConstructF2 = StringData.fromString("testString");
    public static final String schemaName = "testSrcTable";

    public static GenericRowData getPreConstructRowData() {
        // 基于测试数据构造测试用例
        GenericRowData preConstructRowData = new GenericRowData(2);
        preConstructRowData.setRowKind(preConstructRowKind);
        preConstructRowData.setField(0, preConstructF1);
        preConstructRowData.setField(1, preConstructF2);
        return preConstructRowData;
    }
}