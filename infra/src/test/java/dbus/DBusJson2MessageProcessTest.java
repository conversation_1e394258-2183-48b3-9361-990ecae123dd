package dbus;

import com.tencent.andata.dct.api.operator.map.Convert2MessageProcess;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import java.util.List;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.util.OneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.apache.flink.table.data.RowData;
import org.junit.Test;

import com.tencent.andata.struct.avro.message.MessageType;

import static dbus.utils.TestDataWith2Col.preConstructRowType;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class DBusJson2MessageProcessTest {
    final String forwardPatternName = "testForwardPattern";
    final Integer col1 = 1;
    final String col2 = "testString";
    final String testJsonData = String.format("{\"col1\":%d,\"col2\":\"%s\"}", col1, col2);

    @Test
    public void testCompressedDBusWorksFine() throws Exception {
        // uncompressed Msg
        Message uncompressedMsg = checkDBusJson2MessageRunNormallyAndRetFirstMsg(false);
        // compressed Msg
        Message compressedMsg = checkDBusJson2MessageRunNormallyAndRetFirstMsg(true);

        assertTrue(uncompressedMsg.getData().array().length > compressedMsg.getData().array().length);
    }

    public Message checkDBusJson2MessageRunNormallyAndRetFirstMsg(boolean isDataCompressed) throws Exception {

        // TODO: 这里没有测试 tuple3 -> process 这个构建过程，但那个接口后面会给他废弃掉，暂时先不管他
        Convert2MessageProcess deserializerProcess = Convert2MessageProcess
                .builder()
                .addPatternAndRowType("testForwardPattern", preConstructRowType)
                .setDataCompress(isDataCompressed)
                .build();
        OneInputStreamOperatorTestHarness<Tuple2<String, String>, Message> harness =
                ProcessFunctionTestHarnesses.forProcessFunction(deserializerProcess);

        harness.processElement(new Tuple2<>(forwardPatternName, testJsonData), 10);
        List<Message> retMsgList = harness.extractOutputValues();
        assertEquals(1, retMsgList.size());

        Message msg = retMsgList.get(0);

        RowData rowData =
                new RowDataConverter(preConstructRowType, isDataCompressed).deserializeBytesToGenericRowData(msg.getData().array());
        // 确认Json -> Message转换没有问题
        assertEquals(msg.getSchemaName(), forwardPatternName);
        assertEquals(msg.getMsgType(), MessageType.ROW_DATA);

        assertEquals(rowData.getInt(0), col1.intValue());
        assertEquals(rowData.getString(1).toString(), col2);

        return msg;
    }
}