package dbus;

import com.tencent.andata.dct.api.serialize.KafkaAvro2BytesSerSchema;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import java.io.IOException;
import java.nio.ByteBuffer;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.VarCharType;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.Before;
import org.junit.Test;

import static dbus.utils.TestDataWith2Col.getPreConstructRowData;
import static dbus.utils.TestDataWith2Col.preConstructF1;
import static dbus.utils.TestDataWith2Col.preConstructF2;
import static dbus.utils.TestDataWith2Col.preConstructNewRowType;
import static dbus.utils.TestDataWith2Col.preConstructRowKind;
import static dbus.utils.TestDataWith2Col.preConstructRowType;
import static dbus.utils.TestDataWith2Col.schemaName;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;


public class DBusAvroTest {
    GenericRowData preConstructRowData = getPreConstructRowData();

    @Before
    public void construct() {

    }

    @Test
    public void testMessageRowDataDBusSerAndDesIntegrationWithOutDataCompress() throws IOException {
        final boolean isDataCompress = false;
        RowDataConverter serConverter = new RowDataConverter(preConstructRowType, isDataCompress);
        RowDataMessageConverter desConverter = new RowDataMessageConverter(
                preConstructRowType,
                isDataCompress,
                "testDstDB");
        assertSerAndDesHasSameVal(serConverter, desConverter);
    }

    @Test
    public void testMessageRowDataDBusSerAndDesIntegrationWithDataCompress() throws IOException {
        final boolean isDataCompress = true;
        RowDataConverter serConverter = new RowDataConverter(preConstructRowType, isDataCompress);
        RowDataMessageConverter desConverter = new RowDataMessageConverter(
                preConstructRowType,
                isDataCompress,
                "testDstDB");
        assertSerAndDesHasSameVal(serConverter, desConverter);
    }

    @Test
    public void testMessageRowDataDBusSchemaChange() throws IOException {
        final boolean isDataCompress = false;
        RowDataConverter serConverter = new RowDataConverter(preConstructRowType, isDataCompress);

        RowDataMessageConverter desConverter = new RowDataMessageConverter(
                preConstructNewRowType,
                isDataCompress,
                "testDstDB");

        ProducerRecord<byte[], byte[]> pRecord = packToProducerRecord(serConverter);
        RowData desRowData = desProducerRecord(pRecord, desConverter);
        assertEquals(desRowData.getArity(), preConstructNewRowType.getFieldCount());
        try {
            RowData.createFieldGetter(new VarCharType(), 2).getFieldOrNull(desRowData);
        } catch (Exception e) {
            // 20230707 BUG，如果Rowdata Schema变更，且已经写入到了MQ中（违规发布），那么会出现IndexOutOfBoundsException这个错误
            // 原因其实也很简单，只是因为 RowData反序列化的时候必须输入RowType，这样转出来的时候默认会多一个Field，导致取的时候有问题。
            // 这里暂时还没办法感知到Schema的变化，所以直接忽略有问题的Fields即可。并且由于是At least once，所以重启后会再发一次一样的数据
            // 不会有数据丢失的问题。但这里仍然需要加一下忽略逻辑，以解决报错
            assertEquals(e.getClass(), IndexOutOfBoundsException.class);
        }
    }

    ProducerRecord<byte[], byte[]> packToProducerRecord(RowDataConverter serConverter) throws IOException {
        Message msg = new Message();
        msg.setSchemaName(schemaName);
        msg.setMsgType(MessageType.ROW_DATA);
        msg.setProcTime(System.currentTimeMillis());
        msg.setData(ByteBuffer.wrap(serConverter.serializeRowDataToBytes(preConstructRowData)));


        KafkaAvro2BytesSerSchema<Message> serSchema = new KafkaAvro2BytesSerSchema<>(Message.class, "testKafkaTopic");
        return serSchema.serialize(msg, 0L);
    }

    RowData desProducerRecord(ProducerRecord<byte[], byte[]> pRecord, RowDataMessageConverter desConverter) throws IOException {
        AvroMessageDeserializationSchema desSchema = new AvroMessageDeserializationSchema();
        desSchema.addPatternAndConverter(schemaName, desConverter);
        return desSchema.deserialize(pRecord.value());
    }

    public void assertSerAndDesHasSameVal(RowDataConverter serConverter, RowDataMessageConverter desConverter) throws IOException {
        // 构造Message
        ProducerRecord<byte[], byte[]> pRecord = this.packToProducerRecord(serConverter);

        assertRowDataEqual(this.desProducerRecord(pRecord, desConverter));
    }

    public void assertRowDataEqual(RowData desRowData) throws IndexOutOfBoundsException {
        assertEquals(desRowData.getRowKind().toByteValue(), preConstructRowKind.toByteValue());
        assertEquals(desRowData.getInt(0), preConstructF1.intValue());
        assertEquals(desRowData.getString(1).toString(), preConstructF2.toString());
    }
}