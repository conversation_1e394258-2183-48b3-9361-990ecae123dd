package dispatcher;

import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.rainbow.base.config.KvGroup;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import org.junit.Test;
import org.junit.Assert;

public class TaskParamsTest {
    @Test
    public void testBuildTaskParamsFromJson() throws Exception {
        final String dbName = "work", dbTableName = "t217_ticket_scenes", dstDB = "andata_dev", dstTable = "t217_ticket_scenes";
        String jsonWithSinkType = "[{\"dbName\":\"%s\",\"dbTableName\":\"%s\","
                + "\"hiveName\":\"%s\",\"hiveTableName\":\"%s\","
                + "\"primaryKeys\":[\"id\"],\"dbType\":\"MYSQL\",\"sinkType\":\"ICEBERG\"}]";
        jsonWithSinkType = String.format(jsonWithSinkType, dbName, dbTableName, dstDB, dstTable);

        InputTaskParams[] inputParams = InputTaskParams.fromJson(jsonWithSinkType);
        List<TaskParams> taskParamsList = TaskParams.fromInputTaskParams(inputParams);

        TaskParams taskParams = taskParamsList.get(0);
        Assert.assertEquals(0, taskParams.jdbcConf.size());
        // 验证PK生成是否正确
        Assert.assertArrayEquals(taskParams.primaryKeys.toArray(), new String[]{"id"});
        // 默认为Upsert
        Assert.assertEquals(taskParams.writeType, WriteTypeEnum.UPSERT);
        // 验证生成的TableIdentifier对不对
        Assert.assertEquals(new TableIdentifier(DatabaseEnum.MYSQL, dbName, dbTableName), taskParams.srcTableID);
        Assert.assertEquals(new TableIdentifier(DatabaseEnum.ICEBERG, dstDB, dstTable), taskParams.dstTableID);

    }
}