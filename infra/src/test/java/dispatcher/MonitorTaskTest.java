package dispatcher;

import com.tencent.andata.dispatcher.cdc.deserializer.CDCDBRowDataDeserializeSchema;
import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.dispatcher.cdc.task.MonitorTask;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.List;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.TableSchema;
import org.junit.Test;
import org.junit.Assert;

public class MonitorTaskTest {
    @Test
    public void testBuildCDCDeserializerUsingSchemaMap() throws Exception {
        final String dbName = "work", dbTableName = "t217_ticket_scenes", dstDB = "andata_dev", dstTable = "t217_ticket_scenes";
        String jsonWithSinkType = "[{\"dbName\":\"%s\",\"dbTableName\":\"%s\","
                + "\"hiveName\":\"%s\",\"hiveTableName\":\"%s\","
                + "\"primaryKeys\":[\"id\"],\"dbType\":\"MYSQL\",\"sinkType\":\"ICEBERG\"}]";
        jsonWithSinkType = String.format(jsonWithSinkType, dbName, dbTableName, dstDB, dstTable);

        InputTaskParams[] inputParams = InputTaskParams.fromJson(jsonWithSinkType);
        List<TaskParams> taskParamsList = TaskParams.fromInputTaskParams(inputParams);


        Schema schema = new TableSchema(
                new String[]{"id", "test"},
                new TypeInformation[]{
                        TypeInformation.of(Integer.class),
                        TypeInformation.of(String.class)
                }).toSchema();
        MonitorTask mTask = new MonitorTask(taskParamsList.get(0));
        mTask.schemaMap.put(new TableIdentifier(DatabaseEnum.MYSQL, dbName, "", dbTableName), schema);
        CDCDBRowDataDeserializeSchema deserializer = mTask.buildCDCDeserializerUsingSchemaMap();
        Assert.assertEquals(1, deserializer.tableIdentifierHashMap.size());
    }
}