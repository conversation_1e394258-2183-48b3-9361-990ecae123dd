package demo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.junit.ClassRule;
import org.junit.Test;

import static org.junit.Assert.assertTrue;

// NOTE: ARM架构（如Mac M1）下，因为JNI没有arm64对应编译好的包，所以会出现问题。对于这个问题，建议把代码什么的都部署到DevCloud
// NOTE: 在IDE里开Shell来远程运行测试

public class IntegrationTest {
    // Set LOCAL_TEST = true & use mini cluster to active cluster & web ui
    static final boolean LOCAL_TEST = false;
//    @ClassRule
//    public static MiniClusterWithClientResource flinkCluster =
//            new MiniClusterWithClientResource(
//                    new MiniClusterResourceConfiguration.Builder()
//                            .setNumberSlotsPerTaskManager(10)
//                            .setNumberTaskManagers(4)
//                            .build()
//            );

    private static class CollectSink implements SinkFunction<String> {

        // must be static
        public static final List<String> values = Collections.synchronizedList(new ArrayList<>());

        @Override
        public void invoke(String value, SinkFunction.Context context) throws Exception {
            values.add(value);
        }
    }

    @Test
    public void testWebUICreatedAndCheckpointEnabled() throws Exception {
        Configuration config = new Configuration();
        config.setInteger(RestOptions.PORT, 8081);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        // 如果本地测试，也可以使用下面的代码
        // StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(config);
        env.setRestartStrategy(RestartStrategies.noRestart());
        DataStream<String> stream;
        MapFunction<String, String> mapFunction = new MapFunction<String, String>() {
            @Override
            public String map(String s) {
                return new StringBuffer(s).reverse().toString();
            }
        };
        if (LOCAL_TEST) {
            // 连接到本地 9999 端口，可以使用 nc -l 9999来搞一个web console来构造数据
            env
                    .socketTextStream("localhost", 9999, "\n")
                    .map(mapFunction)
                    .print();

            env.enableCheckpointing(5 * 1000);
            CheckpointConfig checkpointConfig = env.getCheckpointConfig();
            checkpointConfig.setMinPauseBetweenCheckpoints(5 * 1000L);
            checkpointConfig.setMaxConcurrentCheckpoints(2);
            checkpointConfig.setCheckpointTimeout(5 * 1000L);
            checkpointConfig.setTolerableCheckpointFailureNumber(10);
            env.execute("Test Demo");
        } else {
            env
                    .fromElements("rage", "your", "dream")
                    .map(mapFunction)
                    .addSink(new CollectSink());
            env.execute("Test Demo");
            assertTrue(CollectSink.values.containsAll(Arrays.asList("egar", "ruoy", "maerd")));
        }
    }
}