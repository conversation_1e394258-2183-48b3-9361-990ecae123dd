package demo;

import com.tencent.andata.conf.manager.InfraConfManager.DstTBLInfo;
import com.tencent.andata.utils.HTTPUtils;
import com.tencent.andata.utils.JSONUtils;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Iterator;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.runtime.plugable.SerializationDelegate;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.transformations.PartitionTransformation;
import org.apache.flink.streaming.runtime.partitioner.KeyGroupStreamPartitioner;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.Preconditions;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

public class CustomPartitionerTest {

    @Test
    public void testStream() throws Exception {
        HashMap<String, DstTBLInfo> retHM = new HashMap<>();
        HttpResponse response = HTTPUtils.getHttp("http://api.andata.polaris/andata-ft/flinkconf?Env=1");
        // 判断返回状态是否为200
        if (response.getStatusLine().getStatusCode() == 200) {
            // 请求体内容
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONUtils jsonUtils = new JSONUtils();
            ObjectNode resp = jsonUtils.getJSONObjectNodeByString(content);
            // 获取JSON中的Data对象，它是一个ObjectNode类型，包含多个键值对
            // 每个键是字段名，值是对应的表名
            ObjectNode resultData = (ObjectNode) resp.get("Data");
            Iterator<String> fieldNames = resultData.fieldNames();
            while (fieldNames.hasNext()) {
                String field = fieldNames.next();
                DstTBLInfo dstTBLInfo = new DstTBLInfo();
                dstTBLInfo.dstDatabase = "andata_rt";
                // 将Data对象中的每个值(表名)提取出来，存入DstTBLInfo对象
                dstTBLInfo.dstTableName = resultData.get(field).asText();
                retHM.put(field, dstTBLInfo);
            }
        } else {
            throw new Exception("Get odsKeyTableNameMap Error");
        }

        System.out.println(retHM);
    }

    @Test
    public void testKeyGroupStreamPartitionerToKeyedStream() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        String dataGenSource = "CREATE TABLE tTable (\n"
                + "    pk STRING,\n"
                + "    col1 INT,\n"
                + "    col2 INT\n"
                + ") WITH (\n"
                + "  'connector' = 'datagen',\n"
                + "  'rows-per-second' = '1'\n"
                + ")";
        tEnv.executeSql(dataGenSource);
        DataStream<Row> ds = tEnv
                .toDataStream(tEnv.sqlQuery("SELECT * FROM tTable"));
        KeySelector<Row, Integer> keySelector = new KeySelector<Row, Integer>() {
            @Override
            public Integer getKey(Row row) {
                return Integer.valueOf(0);
            }
        };
        KeyedStream<Row, Integer> keyedStream = ds.keyBy(keySelector);
        // 使用反射修改内部类
        Field transField = DataStream.class.getDeclaredField("transformation");
        transField.setAccessible(true);
        transField.set(
                keyedStream, new PartitionTransformation<>(
                        ds.getTransformation(),
                        new CustomKeyGroupStreamPartitioner<>(keySelector, 128))
        );
        keyedStream.process(new KeyedProcessFunction<Integer, Row, Object>() {
            @Override
            public void processElement(Row row, KeyedProcessFunction<Integer, Row, Object>.Context context,
                    Collector<Object> collector) throws Exception {
                System.out.printf("%s-%s%n", context.getCurrentKey(), row);
            }
        }).setParallelism(2).print();
        env.execute();
    }

    static class CustomKeyGroupStreamPartitioner<T, K> extends KeyGroupStreamPartitioner<T, K> {

        private final KeySelector<T, K> keySelector;
        private int maxParallelism;

        public CustomKeyGroupStreamPartitioner(KeySelector<T, K> keySelector, int maxParallelism) {
            super(keySelector, maxParallelism);
            this.keySelector = (KeySelector) Preconditions.checkNotNull(keySelector);
            this.maxParallelism = maxParallelism;
        }

        @Override
        public int selectChannel(SerializationDelegate<StreamRecord<T>> record) {
            Object key;
            try {
                key = this.keySelector.getKey((T) ((StreamRecord) record.getInstance()).getValue());
            } catch (Exception var4) {
                throw new RuntimeException("Could not extract key from " + ((StreamRecord) record.getInstance()).getValue(), var4);
            }
            // 写死，验证能否指定SubTask
            return (int) key;
//            return KeyGroupRangeAssignment.assignKeyToParallelOperator(key, this.maxParallelism, this.numberOfChannels);
        }
    }
}