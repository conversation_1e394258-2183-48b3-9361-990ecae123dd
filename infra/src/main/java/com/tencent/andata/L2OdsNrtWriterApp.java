package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.DBusConf.KafkaConf;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.writer.NRTWriter;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;


public class L2OdsNrtWriterApp {
    public static void main(String[] args) throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf(false);

        // Add NRTWriter Task
        NRTWriter.addTaskToEnv(env, updateInfraConf(infraConf, args));
        NRTWriter.setCKConf(env);
        env.execute("l2 ods nrt writer launcher");
    }

    // 通过参数更新InfraConf中的consumerGroup
    private static InfraConf updateInfraConf(InfraConf infraConf, String[] args) {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String consumerGroup = parameterTool.get("consumerGroup");
        KafkaConf kafkaConf = infraConf.l2OdsMQDistributeConf.dBusConf.dBusMQConf;
        if (consumerGroup != null) {
            DBusConf conf = new DBusConf();
            conf.dBusMQConf = new DBusConf.KafkaConf();
            conf.dBusMQConf.topic = kafkaConf.topic;
            conf.dBusMQConf.entryPoint = kafkaConf.entryPoint;
            conf.dBusMQConf.consumerGroup = consumerGroup;
            infraConf.l2OdsMQDistributeConf.setDBusConf(conf);
        }
        return infraConf;
    }
}