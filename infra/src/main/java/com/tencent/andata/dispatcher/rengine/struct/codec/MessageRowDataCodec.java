package com.tencent.andata.dispatcher.rengine.struct.codec;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.table.data.*;

import java.io.*;
import java.nio.ByteBuffer;
import java.sql.Timestamp;

public class MessageRowDataCodec {
    private FlinkLog logger = FlinkLog.getInstance();

    public final static int UNKONW_TYPE = 0;
    public final static int STRINGDATA_TYPE = 1;
    public final static int TIMESTAMPDATA_TYPE = 2;
    public final static int DECIMALDATA_TYPE = 3;
    public final static int ARRAYDATA_TYPE = 4;
    public final static int MAPDATA_TYPE = 5;
    public final static int ROWDATA_TYPE = 6;
    public final static int RAWVALUEDATA_TYPE = 7;

    // 基础类型
    public final static int STRING_TYPE = 100;
    public final static int INT_TYPE = 101;
    public final static int LONG_TYPE = 102;
    public final static int DOUBLE_TYPE = 103;
    public final static int BYTE_TYPE = 104;
    public final static int SHORT_TYPE = 105;
    public final static int CHAR_TYPE = 106;
    public final static int FLOAT_TYPE = 107;
    public final static int BOOL_TYPE = 108;


    public static byte[] serializeMessageRowData(MessageRowData rowData) throws IOException {

        int bufferSize = 4; // object length
        int length = rowData.getArity();

        byte[][] serializedObjects = new byte[length][];
        for (int i = 0; i < length; i++) {
            Object object = rowData.getField(i);
            byte[] bytes = null;
            // str
            if (object instanceof String) {
                bytes = ((String) object).getBytes();
                serializedObjects[i] = ByteBuffer.allocate(4 + 4 + bytes.length)
                        .putInt(STRING_TYPE)
                        .putInt(bytes.length)
                        .put(bytes).array();
                // str data
            } else if (object instanceof StringData) {
                bytes = ((StringData) object).toBytes();
                serializedObjects[i] = ByteBuffer.allocate(4 + 4 + bytes.length)
                        .putInt(STRINGDATA_TYPE)
                        .putInt(bytes.length)
                        .put(bytes).array();
                // int
            } else if (object instanceof Integer) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 4)
                        .putInt(INT_TYPE)
                        .putInt(((Integer) object)).array();
                // long
            } else if (object instanceof Long) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 8)
                        .putInt(LONG_TYPE)
                        .putLong(((Long) object)).array();
                // timestamp data
            } else if (object instanceof TimestampData) {
                long time = ((TimestampData) object).toTimestamp().getTime();
                serializedObjects[i] = ByteBuffer.allocate(4 + 8)
                        .putInt(TIMESTAMPDATA_TYPE)
                        .putLong(time).array();
                // boolean
            } else if (object instanceof Boolean) {
                int b = ((Boolean) object) ? 1 : 0;
                serializedObjects[i] = ByteBuffer.allocate(4 + 4)
                        .putInt(BOOL_TYPE)
                        .putInt(b).array();
                // byte 
            } else if (object instanceof Byte) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 1)
                        .putInt(BYTE_TYPE)
                        .put(((Byte) object)).array();
                // char
            } else if (object instanceof Character) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 2)
                        .putInt(CHAR_TYPE)
                        .putChar(((Character) object)).array();
                // float
            } else if (object instanceof Float) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 4)
                        .putInt(FLOAT_TYPE)
                        .putFloat(((Float) object)).array();
                // double
            } else if (object instanceof Double) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 8)
                        .putInt(DOUBLE_TYPE)
                        .putDouble(((Double) object)).array();
                // short
            } else if (object instanceof Short) {
                serializedObjects[i] = ByteBuffer.allocate(4 + 2)
                        .putInt(SHORT_TYPE)
                        .putShort(((Short) object)).array();
            }  else if (object instanceof DecimalData) {

                serializedObjects[i] = ByteBuffer.allocate(4)
                        .putInt(DECIMALDATA_TYPE).array();
            } else if (object instanceof ArrayData) {
                serializedObjects[i] = ByteBuffer.allocate(4)
                        .putInt(ARRAYDATA_TYPE).array();
            } else if (object instanceof MapData) {
                serializedObjects[i] = ByteBuffer.allocate(4)
                        .putInt(MAPDATA_TYPE).array();
            } else if (object instanceof RowData) {
                serializedObjects[i] = ByteBuffer.allocate(4)
                        .putInt(ROWDATA_TYPE).array();
            } else if (object instanceof RawValueData) {
                serializedObjects[i] = ByteBuffer.allocate(4)
                        .putInt(RAWVALUEDATA_TYPE).array();
            } else {
                bytes = serializeObject(object);
                serializedObjects[i] = ByteBuffer.allocate(4 + 4 + bytes.length)
                        .putInt(UNKONW_TYPE)
                        .putInt(bytes.length)
                        .put(bytes).array();
            }

            bufferSize += serializedObjects[i].length;
        }
        ByteBuffer byteBuffer = ByteBuffer.allocate(bufferSize);
        // length
        byteBuffer.putInt(length);
        for (int i = 0; i < length; i++) {
            byte[] bytes = serializedObjects[i];
            byteBuffer.put(bytes);
        }
        return byteBuffer.array();
    }


    public static MessageRowData deserializeMessageRowData(byte[] data) throws IOException, ClassNotFoundException {
        ByteBuffer byteBuffer = ByteBuffer.wrap(data);
        int length = byteBuffer.getInt();
        MessageRowData rowData = new MessageRowData(length);
        for (int i = 0; i < length; i++) {
            // type
            int type = byteBuffer.getInt();
            Object object = null;
            int bytesLength = 0;
            byte[] bytesData = null;
            // str data
            if (type == STRINGDATA_TYPE) {
                bytesLength = byteBuffer.getInt();
                bytesData = new byte[bytesLength];
                byteBuffer.get(bytesData);
                object = StringData.fromBytes(bytesData);
                // str
            } else if (type == STRING_TYPE) {
                bytesLength = byteBuffer.getInt();
                bytesData = new byte[bytesLength];
                byteBuffer.get(bytesData);
                object = new String(bytesData);
                // int 
            } else if (type == INT_TYPE) {
                object = byteBuffer.getInt();
                // long
            } else if (type == LONG_TYPE) {
                object = byteBuffer.getLong();
                // timestamp 
            } else if (type == TIMESTAMPDATA_TYPE) {
                object = TimestampData.fromTimestamp(new Timestamp(byteBuffer.getLong()));
                // short
            } else if (type == SHORT_TYPE) {
                object = byteBuffer.getShort();
                // bool
            } else if (type == BOOL_TYPE) {
                object = byteBuffer.getInt() == 1;
                // float
            } else if (type == FLOAT_TYPE) {
                object = byteBuffer.getFloat();
                // double
            } else if (type == DOUBLE_TYPE) {
                object = byteBuffer.getDouble();
                // byte
            } else if (type == BYTE_TYPE) {
                object = byteBuffer.get();
                // char
            } else if (type == CHAR_TYPE) {
                object = byteBuffer.getChar();
            } else if (type == DECIMALDATA_TYPE) {

            } else if (type == ARRAYDATA_TYPE) {

            } else if (type == MAPDATA_TYPE) {

            } else if (type == ROWDATA_TYPE) {

            } else if (type == RAWVALUEDATA_TYPE) {

            } else {
                bytesLength = byteBuffer.getInt();
                bytesData = new byte[bytesLength];
                byteBuffer.get(bytesData);
                object = deserializeObject(bytesData);
            }
            rowData.setField(i, object);
        }
        return rowData;
    }

    // 将对象序列化为字节数组
    private static byte[] serializeObject(Object object) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(object);
        oos.close();
        return baos.toByteArray();
    }

    // 将字节数组反序列化为对象
    private static Object deserializeObject(byte[] bytes) throws IOException, ClassNotFoundException {
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        ObjectInputStream ois = new ObjectInputStream(bais);
        Object object = ois.readObject();
        ois.close();
        return object;
    }
}
