package com.tencent.andata.dispatcher.rengine;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.dispatcher.rengine.etl.REngineListenerEtl;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.Properties;

public class REngineListener {
    public static final String JOIN_RULE_DB_RAINBOW_GROUP = "cdc.database.mysql.rengine_manager";

    public static void main(String[] args) throws Exception{
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        flinkEnv.env().getConfig().enableObjectReuse();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        // DB conf
        final DatabaseConf ruleDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(JOIN_RULE_DB_RAINBOW_GROUP)
                .build();
        // Runner
        REngineListenerEtl.builder()
                .rainbowUtils(rainbowUtils)
                .ruleDatabaseConf(ruleDbConf)
                .build()
                .run(flinkEnv);
        // Sink StarRocks，CK可以短一点
        flinkEnv.env().enableCheckpointing(10 * 1000, CheckpointingMode.EXACTLY_ONCE);
        flinkEnv.env().execute("REngine listener");

    }
}
