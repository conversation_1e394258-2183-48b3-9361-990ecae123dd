package com.tencent.andata.dispatcher.rengine.process;

import com.tencent.andata.dispatcher.rengine.lookup.builder.MultipleTableJoinLookupQueryBuilder;
import com.tencent.andata.dispatcher.rengine.lookup.query.AntoolMultipleTableInitQuery;
import com.tencent.andata.dispatcher.rengine.lookup.query.AntoolMultipleTableJoinLookupQuery;
import com.tencent.andata.dispatcher.rengine.struct.MessagePack;
import com.tencent.andata.dispatcher.rengine.struct.REngineMessage;
import com.tencent.andata.dispatcher.rengine.struct.builder.MessageDataBuilder;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.List;

public class REngineCdcDataQueryProcessFunction extends BroadcastProcessFunction<MessageRowData, Tuple2<String, String>, REngineMessage> {
    private static final FlinkLog logger = FlinkLog.getInstance();

    private final HashMap<String, AntoolMultipleTableJoinLookupQuery> queryMap;
    private final DatabaseConf ruleDatabaseConf;

    public REngineCdcDataQueryProcessFunction(DatabaseConf ruleDatabaseConf) {
        this.queryMap = new HashMap<>();
        this.ruleDatabaseConf = ruleDatabaseConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 初始化时从DB获取全量规则
        final AntoolMultipleTableInitQuery initQuery = new AntoolMultipleTableInitQuery(this.ruleDatabaseConf);
        initQuery.open();
        this.queryMap.putAll(
                // 这里是查全量规则，所以没有入参，暂时用空字符串代替
                initQuery.query("")
        );
        initQuery.close();
        // Query 初始化
        for (AntoolMultipleTableJoinLookupQuery query : this.queryMap.values()) {
            query.open();
        }
        logger.info(String.format(
                "初始化规则完成：%s",
                this.queryMap
        ));
    }

    @Override
    public void close() throws Exception {
        super.close();
        for (AntoolMultipleTableJoinLookupQuery query : this.queryMap.values()) {
            query.close();
        }
    }

    /**
     * 处理REngine CDC数据
     *
     * @param messageRowData  cdc数据
     * @param readOnlyContext context
     * @param collector       collector
     * @throws Exception ex
     */
    @Override
    public void processElement(MessageRowData messageRowData, BroadcastProcessFunction<MessageRowData, Tuple2<String, String>, REngineMessage>.ReadOnlyContext readOnlyContext, Collector<REngineMessage> collector) throws Exception {
        // 获取CDC数据的表名
        final String tableName = messageRowData.getAttr().getSrcTable().getTableName();
        // 判断该表是否需要join query
        if (this.queryMap.containsKey(tableName)) {
            logger.info(String.format(
                    "table: %s 接收到数据:%s，进行查询",
                    tableName,
                    messageRowData
            ));
            final AntoolMultipleTableJoinLookupQuery query = queryMap.get(tableName);
            // 调用query查询维表数据
            final List<MessageRowData> res = query.query(messageRowData);
            // 这里查询出来会有多条
            // TODO 现在是统一将查询结果直接flat分发，后续考虑对res进行特殊处理下发(Aggregate & Filter)
            // TODO 例如群消息需要根据群类型判断是否需要下发、DMD标签数据需要进行聚合
            for (MessageRowData data : res) {
                logger.info(String.format(
                        "表 %s 查询结果：%s",
                        tableName,
                        data
                ));
                collector.collect(
                        this.covertToRengineMessage(tableName, data)
                );
            }
        } else {
            logger.info(String.format(
                    "该表没有规则，直接下发:%s, data: %s",
                    tableName,
                    messageRowData
            ));
            // 该表没有需要query，则直接下发
            collector.collect(
                    this.covertToRengineMessage(tableName, messageRowData)
            );
        }
    }

    /**
     * 处理规则广播数据
     *
     * @param tuple2    tableName 和 query
     * @param context   context
     * @param collector collector
     * @throws Exception ex
     */
    @Override
    public void processBroadcastElement(Tuple2<String, String> tuple2, BroadcastProcessFunction<MessageRowData, Tuple2<String, String>, REngineMessage>.Context context, Collector<REngineMessage> collector) throws Exception {
        final AntoolMultipleTableJoinLookupQuery query;
        try {
            query = MultipleTableJoinLookupQueryBuilder.buildFromJson(tuple2.f0, tuple2.f1);
        } catch (Exception e) {
            logger.error(
                    String.format(
                            "CDC规则数据解析失败，跳过 :%s, err:%s",
                            tuple2.f1,
                            e
                    )
            );
            return;
        }
        // 更新状态数据
        this.queryMap.put(tuple2.f0, query);
        // 提前初始化query
        query.open();
        logger.info(String.format(
                "收到CDC规则，进行更新：%s",
                query
        ));
    }

    /**
     * 将query的数据转换成REngineMessage
     *
     * @param tableName 表名
     * @param rowData   数据
     * @return rEngine message
     */
    private REngineMessage covertToRengineMessage(String tableName, MessageRowData rowData) {
        // 创建 REngineMessage
        REngineMessage message = new REngineMessage();
        String schemaName = tableName;
        // 订阅表名
        message.setSubScribeName(tableName);
        message.setMessagePack(
                MessagePack.builder()
                        .addMessageData(
                                new MessageDataBuilder()
                                        .setDstTableName(schemaName)
                                        .addRowData(rowData)
                                        .build()
                        )
                        .build()
        );

        return message;
    }
}
