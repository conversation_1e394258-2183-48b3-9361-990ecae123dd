package com.tencent.andata.dispatcher.rengine.source.deserializer;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.deserialize.avro.BackwardCompatibleSpecificDatumReader;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import org.apache.avro.io.BinaryDecoder;
import org.apache.avro.io.DatumReader;
import org.apache.avro.io.DecoderFactory;
import org.apache.flink.table.types.logical.BigIntType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.VarCharType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;

public class REngineMessageDeserializer extends AvroMessageDeserializationSchema {
    private DatumReader<Message> datumReader;
    private BinaryDecoder decoder;
    private Message message;
    private RowDataMessageConverter rEngineMessageConverter;
    private static final RowType rEngineRowType = new RowType( // REngine固定Rowtype
            Arrays.asList(
                    new RowType.RowField("type", new VarCharType(false, 20)),
                    new RowType.RowField("change_type", new VarCharType(false, 20)),
                    new RowType.RowField("before_value", new VarCharType(true, Integer.MAX_VALUE)),
                    new RowType.RowField("after_value", new VarCharType(true, Integer.MAX_VALUE)),
                    new RowType.RowField("ts_ms", new BigIntType())
            ));

    public REngineMessageDeserializer() {
        this.rEngineMessageConverter = new RowDataMessageConverter(
                rEngineRowType,
                true,
                // TODO 这里下游不需要用到databaseName
                ""
        );
    }

    /**
     * 懒加载
     */
    public void lazyInitInstance() {
        if (datumReader == null) {
            datumReader = new BackwardCompatibleSpecificDatumReader<>(Message.class);
        }
    }


    @Override
    public MessageRowData deserialize(byte[] bytes) throws IOException {
        lazyInitInstance();
        decoder = new DecoderFactory().binaryDecoder(bytes, decoder);
        message = datumReader.read(message, decoder);

        MessageType messageType = message.getMsgType();
        if (messageType.equals(MessageType.ROW_DATA)) {
            return this.rEngineMessageConverter.convertMessageToMessageRowData(message);
        } else {
            throw new RuntimeException(String.format("Unknow message type. Val: %s", messageType));
        }
    }
}
