package com.tencent.andata.dispatcher.rengine.struct.builder;

import com.tencent.andata.dispatcher.rengine.struct.MessageData;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.Preconditions;

import java.util.ArrayList;
import java.util.List;

public class MessageDataBuilder {
    private List<MessageRowData> messageRowDatas;
    private String dstTableName;
    private String stateName;

    public MessageDataBuilder setDstTableName(String dstTableName) {
        this.dstTableName = dstTableName;
        return this;
    }

    public MessageDataBuilder setStateName(String stateName) {
        this.stateName = stateName;
        return this;
    }

    public MessageDataBuilder setRowDatas(List<MessageRowData> rowDatas) {
        this.messageRowDatas = rowDatas;
        return this;
    }

    public MessageDataBuilder addRowData(MessageRowData rowData) {
        if (this.messageRowDatas == null) {
            this.messageRowDatas = new ArrayList<>();
        }
        this.messageRowDatas.add(rowData);
        return this;
    }

    public MessageData build() {
        Preconditions.checkNotNull(dstTableName);
        Preconditions.checkNotNull(messageRowDatas);
        if (StringUtils.isEmpty(stateName)) {
            stateName = "";
        }
        return new MessageData(dstTableName, messageRowDatas, stateName);
    }
}