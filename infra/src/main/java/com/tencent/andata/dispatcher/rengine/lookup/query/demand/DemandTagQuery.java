package com.tencent.andata.dispatcher.rengine.lookup.query.demand;

import com.tencent.andata.dispatcher.rengine.lookup.interfaces.AntoolQuery;
import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询需求单标签
 */
public class DemandTagQuery extends AbstractJDBCLookupQuery<HashMap<String, Object>, List<HashMap<String, Object>>>
        implements AntoolQuery {
    public DemandTagQuery(DatabaseConf databaseConf) throws Exception {
        super(DatabaseEnum.MYSQL, databaseConf);
    }

    @Override
    protected List<HashMap<String, Object>> executeQuery(Connection connection, HashMap<String, Object> stringObjectHashMap) throws Exception {
        final Statement statement = connection.createStatement();
        String sqlTmp = "select t1.name from t_tag_configs t1 "
                + "left join t_tag_infos t2 "
                + "on t1.id = t2.tag_config_id "
                + "where t2.story_id = %s and t2.status = 1";
        int storyId = stringObjectHashMap.get("id") == null ? 0 : (int) stringObjectHashMap.get("id");
        // 查询标签
        String sql = String.format(
                sqlTmp,
                storyId
        );
        final ResultSet resultSet = statement.executeQuery(sql);
        List<String> tagList = new ArrayList<>();
        // 遍历结果
        while (resultSet.next()) {
            // 产品名称
            final String tagName = resultSet.getString(1);
            tagList.add(tagName);
        }
        return new ArrayList<HashMap<String, Object>>() {{
            add(new HashMap<String, Object>() {{
                put("story_tag", String.join(",", tagList));
            }});
        }};
    }

}
