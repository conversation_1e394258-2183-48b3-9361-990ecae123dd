package com.tencent.andata.dispatcher.rengine.struct;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

// 规则引擎统一的数据格式
public class REngineMessage {
    // 订阅信息，可能是 tableName 也可能是 ruleName
    // rebalancer 根据该字段判断数据来源
    // 具体来说  数据总线过来的时候是 tableName 然后经过 rebalancer 分发到 cep 算子
    // cep算子出来后该字段变为 ruleName， rebalancer 根据该字段分发到下游 mapper
    private String subScribeName = "";
    // 这个list存储的key通过计算会落在同一个kg里面
    // 比如rule1 订阅了字段 id , rule2 订阅了字段 uin
    // 数据(id:1,uin:2)根据 id-1 和 uin-2 做keyby会落在一个kg里
    // 那么这个list的value为 [id-1, uin-2]
    // 这里是一个list的原因是，避免多个key做keyby后落在同一个kg里面而造成不必要的数据复制
    // 具体做 keyby 的key不是这里面的任何一个
    public List<PartitionKeys> pksList = new ArrayList<>();
    // pksList的key做keyby后会落在的kg id
    // 使用这个值做keyby的key,下游通过修改keyby的分区器，通过这个kgid计算会落在哪个subtask上
    // 然后直接发往对应的subtask
    // keyGroupID 位于 0 到 maxParallelism之间
    private Integer keyGroupID;
    // data 数据
    // 一个 rule 会对应多个 state
    // 一个 state 可能会匹配上多个数据
    // 这里的数据是list的原因在于cep匹配后可能产生多个状态，每一个状态都可能含有多个数据
    private MessagePack messagePack;
    // 组件的版本号
    private String moduleVersion;
    // 元数据
    public HashMap<String, String> extraMap;

    public void setExtras(HashMap<String, String> extraMap) {
        if (extraMap == null) {
            return;
        }
        if (this.extraMap == null) {
            this.extraMap = new HashMap<>();
        }
        this.extraMap.putAll(extraMap);
    }

    public void setExtra(String key, String value) {
        if (extraMap == null) {
            extraMap = new HashMap<>();
        }
        extraMap.put(key, value);
    }

    public String getExtra(String key) {
        if (extraMap == null) {
            return null;
        }
        return extraMap.get(key);
    }

    public String getModuleVersion() {
        return moduleVersion;
    }

    public void setModuleVersion(String moduleVersion) {
        this.moduleVersion = moduleVersion;
    }

    public MessagePack getMessagePack() {
        return messagePack;
    }

    public void setMessagePack(MessagePack messagePack) {
        this.messagePack = messagePack;
    }

    public Integer getKeyGroupID() {
        return keyGroupID;
    }

    public void setKeyGroupID(Integer keyGroupID) {
        this.keyGroupID = keyGroupID;
    }


    public String getSubScribeName() {
        return subScribeName;
    }

    public void setSubScribeName(String subScribeName) {
        this.subScribeName = subScribeName;
    }


    public static class PartitionKeys {

        public static class DataKeyValue {
            public String partitionName;
            public String value;

            public DataKeyValue(String partitionName, String value) {
                this.partitionName = partitionName;
                this.value = value;
            }

            @Override
            public String toString() {
                return String.format("%s-%s", partitionName, value);
            }
        }

        public List<DataKeyValue> dkList = new ArrayList<>();

        public String getCombinedKeys() {
            return dkList.stream().map(DataKeyValue::toString).collect(Collectors.joining(":"));
        }
    }

    public byte[] encode() throws IOException {

        // 这个map长度不能超过 32767
        int extraLength = 2; // extraMap size
        short extraSize = 0;
        // extra length
        if (extraMap != null) {
            for (Map.Entry<String, String> entry : extraMap.entrySet()) {
                extraLength += 2 + entry.getKey().getBytes().length;
                extraLength += 4 + entry.getValue().getBytes().length;
                extraSize += 1;
            }
        }

        int packDataSize = messagePack.getDataSize();
        LinkedList<byte[]> packBytes = new LinkedList<>();
        int messageDataSize = 0;
        for (int i = 0; i < packDataSize; i++) {
            MessageData messageData = messagePack.getMessageData(i);
            byte[] encode = messageData.encode();
            packBytes.add(encode);
            messageDataSize += 4 + encode.length;
        }
        int allocateSize = extraLength +  // extra length
                4 + // subScribeName length
                subScribeName.getBytes(StandardCharsets.UTF_8).length + // subScribeName data
                4 + // packDataSize
                messageDataSize;  // packData

        ByteBuffer buffer = ByteBuffer.allocate(allocateSize);
        // extra Size
        buffer.putShort(extraSize);
        // encode extra
        if (extraMap != null) {
            for (Map.Entry<String, String> entry : extraMap.entrySet()) {
                byte[] keyBytes = entry.getKey().getBytes();
                byte[] valueBytes = entry.getValue().getBytes();
                buffer.putShort((short) keyBytes.length);
                buffer.put(keyBytes);
                buffer.putInt(valueBytes.length);
                buffer.put(valueBytes);
            }
        }
        // subScribeName length
        buffer.putInt(subScribeName.getBytes(StandardCharsets.UTF_8).length);
        // subScribeName data
        buffer.put(subScribeName.getBytes());
        // packDataSize
        buffer.putInt(packDataSize);
        // packData
        for (byte[] packByte : packBytes) {
            buffer.putInt(packByte.length);
            buffer.put(packByte);
        }
        return buffer.array();
    }

    public static REngineMessage decode(byte[] array) throws IOException, ClassNotFoundException {
        ByteBuffer byteBuffer = ByteBuffer.wrap(array);
        REngineMessage rEngineMessage = new REngineMessage();
        // extra decode
        short extraSize = byteBuffer.getShort();
        for (short i = 0; i < extraSize; i++) {
            short keyLength = byteBuffer.getShort();
            byte[] key = new byte[keyLength];
            byteBuffer.get(key);
            int valueLength = byteBuffer.getInt();
            byte[] value = new byte[valueLength];
            byteBuffer.get(value);
            rEngineMessage.setExtra(new String(key), new String(value));
        }
        // subScribeName length
        int subScribeNameLength = byteBuffer.getInt();
        // subScribeName
        byte[] bytes = new byte[subScribeNameLength];
        byteBuffer.get(bytes);
        rEngineMessage.setSubScribeName(new String(bytes));
        // messagePack size
        MessagePack.Builder builder = MessagePack.builder();
        int messagePackSize = byteBuffer.getInt();
        for (int i = 0; i < messagePackSize; i++) {
            int packLength = byteBuffer.getInt();
            byte[] packBytes = new byte[packLength];
            byteBuffer.get(packBytes);
            builder
                    .addMessageData(
                            MessageData.decode(packBytes)
                    );
        }
        rEngineMessage.setMessagePack(builder.build());
        return rEngineMessage;
    }

    @Override
    public String toString() {
        return "REngineMessage{" +
                "subScribeName='" + subScribeName + '\'' +
                ", pksList=" + pksList +
                ", keyGroupID=" + keyGroupID +
                ", messagePack=" + messagePack +
                ", moduleVersion='" + moduleVersion + '\'' +
                ", extraMap=" + extraMap +
                '}';
    }
}
