package com.tencent.andata.dispatcher.rengine.source.builder;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.InfraConfUtils;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import com.tencent.andata.dispatcher.rengine.source.deserializer.REngineMessageDeserializer;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import com.tencent.andata.writer.operator.source.builder.KafkaDBusSourceBuilder;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.HashMap;

public class REngineKafkaDbusBuilder {
    DBusConf dBusConf;
    StreamExecutionEnvironment env;

    public REngineKafkaDbusBuilder setDbus(DBusConf dBusConf) {
        this.dBusConf = dBusConf;
        this.dBusConf.payloadWrapped = true;
        return this;
    }

    public REngineKafkaDbusBuilder setFlinkEnv(StreamExecutionEnvironment env) {
        this.env = env;
        return this;
    }

    String getUid() {
        return String.format("rEngine-kafka-source-%s::%s", dBusConf, dBusConf.dBusMQConf.consumerGroup);
    }

    /**
     * build
     *
     * @return
     */
    public DataStream<MessageRowData> build() {
        AvroMessageDeserializationSchema deserializationSchema = new REngineMessageDeserializer();


        return env
                .fromSource(
                        new InfraConfUtils<MessageRowData>()
                                .buildKafkaConsumerFromDbusConf1_15(this.dBusConf.dBusMQConf, deserializationSchema)
                        ,
                        WatermarkStrategy.noWatermarks(),
                        getUid()
                )
                .uid(getUid())
                .name(getUid())
                .setParallelism(2);
    }

    public static KafkaDBusSourceBuilder getInstance() {
        return new KafkaDBusSourceBuilder();
    }
}
