package com.tencent.andata.dispatcher.rengine.struct;

import com.tencent.andata.dispatcher.rengine.struct.builder.MessageDataBuilder;
import com.tencent.andata.dispatcher.rengine.struct.codec.MessageRowDataCodec;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import com.tencent.andata.utils.struct.DatabaseEnum;


import java.io.*;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.util.List;

// 一个MessageData对应一个cep state匹配的数据
// 一个 state 匹配上的应该是同一张表的多条数据
public class MessageData {
    // 表名
    private String dstTableName = "";
    // 数据本身
    private List<MessageRowData> messageRowDatas;
    // cep 状态名
    private String stateName = "";

    public String getStateName() {
        return stateName;
    }

    public String getDstTableName() {
        return dstTableName;
    }

    public MessageRowData getMessageRowData(int i) {
        return messageRowDatas.get(i);
    }

    public int getRowSize() {
        return messageRowDatas.size();
    }

    public MessageData(String dstTableName, List<MessageRowData> messageRowDatas, String stateName) {
        this.dstTableName = dstTableName;
        this.messageRowDatas = messageRowDatas;
        this.stateName = stateName;
    }

    public byte[] encode() throws IOException {
        int rowSize = getRowSize();
        LinkedList<byte[]> datas = new LinkedList<>();

        int dataSize = 0;
        for (int i = 0; i < rowSize; i++) {
            MessageRowData rowData = messageRowDatas.get(i);
            byte[] bytes = MessageRowDataCodec.serializeMessageRowData(rowData);
            dataSize += 4 + bytes.length;
            datas.add(bytes);
        }

        int bufferSize = 4 + // stateName 长度
                stateName.getBytes(StandardCharsets.UTF_8).length + // stateName
                4 + // dstTableName长度
                dstTableName.getBytes(StandardCharsets.UTF_8).length + // dstTableName data长度
                4 + // 有多少个 rowData
                dataSize ; // data
        ByteBuffer buffer = ByteBuffer.allocate(bufferSize);
        // stateName长度
        buffer.putInt(stateName.getBytes(StandardCharsets.UTF_8).length);
        // stateName
        buffer.put(stateName.getBytes());
        // dstTableName长度
        buffer.putInt(dstTableName.getBytes(StandardCharsets.UTF_8).length);
        // dstTableName data长度
        buffer.put(dstTableName.getBytes());
        // 有多少个 rowData
        buffer.putInt(rowSize);
        // put 每一个 rowData
        for (byte[] data : datas) {
            // 每一个rowData的长度
            buffer.putInt(data.length);
            // 每一个rowData的数据
            buffer.put(data);
        }
        return buffer.array();
    }

    public static MessageData decode(byte[] array) throws IOException, ClassNotFoundException {
        ByteBuffer byteBuffer = ByteBuffer.wrap(array);

        MessageDataBuilder builder = new MessageDataBuilder();
        // 读 stateName 长度
        int stateNameSize = byteBuffer.getInt();
        // 读 stateName
        byte[] stateNameBytes = new byte[stateNameSize];
        byteBuffer.get(stateNameBytes);
        builder.setStateName(new String(stateNameBytes));
        // 读dstTableName长度
        int dstTableNameSize = byteBuffer.getInt();
        // 读dstTableName
        byte[] dstTableNameBytes = new byte[dstTableNameSize];
        byteBuffer.get(dstTableNameBytes);
        builder.setDstTableName(new String(dstTableNameBytes));
        // 读有多少个rowData
        int rowDataSize = byteBuffer.getInt();
        for (int i = 0; i < rowDataSize; i++) {
            // 读取每个rowData
            int rowDataLength = byteBuffer.getInt();
            byte[] bytes = new byte[rowDataLength];
            byteBuffer.get(bytes);
            MessageRowData rowData = MessageRowDataCodec.deserializeMessageRowData(bytes);
            MessageRowDataAttr messageRowDataAttr = new MessageRowDataAttr();
            messageRowDataAttr.setSrcTable(
                    new TableIdentifier(
                            // TODO 待优化，先设置成MYSQL
                            DatabaseEnum.MYSQL,
                            "rengine_source",
                            new String(dstTableNameBytes)
                    )
            );
            rowData.setAttr(
                    messageRowDataAttr
            );
            builder.addRowData(rowData);
        }
        return builder.build();
    }

    @Override
    public String toString() {
        return "MessageData{" +
                "dstTableName='" + dstTableName + '\'' +
                ", messageRowDatas=" + messageRowDatas +
                ", stateName='" + stateName + '\'' +
                '}';
    }
}