package com.tencent.andata.writer.deserialize.schema;

import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.writer.deserialize.avro.BackwardCompatibleSpecificDatumReader;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import java.io.IOException;
import java.util.HashMap;
import org.apache.avro.io.BinaryDecoder;
import org.apache.flink.api.common.serialization.AbstractDeserializationSchema;

import org.apache.avro.io.DatumReader;
import org.apache.avro.io.DecoderFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AvroMessageDeserializationSchema extends AbstractDeserializationSchema<MessageRowData> {
    private static final Logger log = LoggerFactory.getLogger(AvroMessageDeserializationSchema.class);
    // 本对象在序列化到TM的时候可能会有问题，采用Lazy Load模式解决问题可能会更好点
    private DatumReader<Message> datumReader;
    private BinaryDecoder decoder;
    private Message message;
    private HashMap<String, RowDataMessageConverter> patternConverterMap = new HashMap<>();
    private boolean ignore = false;

    public AvroMessageDeserializationSchema() {

    }

    public AvroMessageDeserializationSchema(boolean tableNotFoundIgnore) {
        this.ignore = tableNotFoundIgnore;
    }

    /**
     * 懒加载
     */
    public void lazyInitInstance() {
        if (datumReader == null) {
            datumReader = new BackwardCompatibleSpecificDatumReader<>(Message.class);
        }
    }


    public void addPatternAndConverter(String pattern, RowDataMessageConverter converter) {
        patternConverterMap.put(pattern, converter);
    }

    @Override
    public MessageRowData deserialize(byte[] bytes) throws IOException {
        lazyInitInstance();
        decoder = new DecoderFactory().binaryDecoder(bytes, decoder);
        message = datumReader.read(message, decoder);

        MessageType messageType = message.getMsgType();
        String pattern = message.getSchemaName().toString();
        if (messageType.equals(MessageType.ROW_DATA)) {
            RowDataMessageConverter rowDataMessageConverter = patternConverterMap.get(pattern);
            if (rowDataMessageConverter != null) {
                return rowDataMessageConverter.convertMessageToMessageRowData(message);
            }
        } else {
            throw new RuntimeException(String.format("Unknow message type. Val: %s", messageType));
        }
        // TODO: Abnormal should not be null. Get a new Instance to storage.
        if (ignore) {
            log.warn(String.format("%s schema cannot be found in schema map!", pattern));
            return null;
        }
        throw new RuntimeException(String.format("%s schema cannot be found in schema map!", pattern));
    }
}