package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.dct.api.APIChannelExtractor;
import com.tencent.andata.writer.NRTWriter;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

public class MultiAppLauncher {
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf(false);

        // Add NRTWriter Task
        NRTWriter.addTaskToEnv(env, infraConf);
        // Add API Extractor Task
        APIChannelExtractor.addTaskToEnv(env, infraConf);
        // Set checkpoint, using extractor conf
        APIChannelExtractor.setCKConf(env);

        env.execute("multi app (api extractor + nrt writer) launcher");
    }

}