package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.DBusConf.KafkaConf;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.writer.NRTWriter;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class L2OdsNrtWriterAppSplittable {
    private static final Logger LOG = LoggerFactory.getLogger(L2OdsNrtWriterAppSplittable.class);

    public static void main(String[] args) throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        // --- 动态拆分的核心参数 ---
        final int totalParts = parameterTool.getInt("totalParts");
        final int partIndex = parameterTool.getInt("partIndex");

        if (totalParts <= 0 || partIndex < 0 || partIndex >= totalParts) {
            throw new IllegalArgumentException(
                    "Parameters 'totalParts' and 'partIndex' are required and must be valid. " +
                    "E.g., --totalParts 3 --partIndex 0");
        }

        // --- 1. 获取带分片信息的配置 ---
        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConfWithPartition(totalParts, partIndex);

        LOG.info("已过滤表配置，当前分片 {}/{} 处理表数量: {}", 
                partIndex + 1, totalParts, infraConf.l2OdsMQDistributeConf.patternTableMap.size());

        // --- 2. 启动NRTWriter任务 ---
        NRTWriter.addTaskToEnv(env, updateInfraConf(infraConf, args, partIndex));
        NRTWriter.setCKConf(env);

        String jobName = String.format("l2 ods nrt writer launcher - PART %d of %d", partIndex + 1, totalParts);
        env.execute(jobName);
    }

    // 通过参数更新InfraConf中的consumerGroup
    private static InfraConf updateInfraConf(InfraConf infraConf, String[] args, int partIndex) {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String consumerGroup = parameterTool.get("consumerGroup");
        KafkaConf kafkaConf = infraConf.l2OdsMQDistributeConf.dBusConf.dBusMQConf;
        
        // 如果没有指定consumerGroup，则根据partIndex自动生成一个
        if (consumerGroup == null) {
            // 获取原始消费者组名称
            String originalConsumerGroup = kafkaConf.consumerGroup;
            // 根据分片索引生成新的消费者组名称
            consumerGroup = originalConsumerGroup + "-part-" + partIndex;
            LOG.info("未指定consumerGroup参数，自动生成消费者组: {}", consumerGroup);
        }
        
        // 设置消费者组
        DBusConf conf = new DBusConf();
        conf.dBusMQConf = new DBusConf.KafkaConf();
        conf.dBusMQConf.topic = kafkaConf.topic;
        conf.dBusMQConf.entryPoint = kafkaConf.entryPoint;
        conf.dBusMQConf.consumerGroup = consumerGroup;
        infraConf.l2OdsMQDistributeConf.setDBusConf(conf);
        
        LOG.info("设置Kafka消费者组: {}, 主题: {}", consumerGroup, kafkaConf.topic);
        
        return infraConf;
    }
} 