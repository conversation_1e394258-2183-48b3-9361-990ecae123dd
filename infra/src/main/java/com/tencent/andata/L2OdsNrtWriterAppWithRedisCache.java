package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManagerWithRedisCache;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.DBusConf.KafkaConf;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.writer.NRTWriter;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * L2 ODS NRT Writer应用 - Redis缓存版本
 * 使用Redis缓存的TableLoader，显著提升启动速度
 * 
 * <AUTHOR>
 */
public class L2OdsNrtWriterAppWithRedisCache {
    
    private static final Logger logger = LoggerFactory.getLogger(L2OdsNrtWriterAppWithRedisCache.class);

    public static void main(String[] args) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("启动L2 ODS NRT Writer (Redis缓存版本)...");

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 使用Redis缓存版本的InfraConfManager
        InfraConfManagerWithRedisCache confManager = new InfraConfManagerWithRedisCache(
                ExtractorFactory.fromProperties("env.properties")
        );

        try {
            // 获取配置
            InfraConf infraConf = confManager.getInfraConf(true);
            
            long confLoadTime = System.currentTimeMillis();
            logger.info("配置加载完成，耗时: {}ms", confLoadTime - startTime);

            // Add NRTWriter Task
            NRTWriter.addTaskToEnv(env, updateInfraConf(infraConf, args));
            NRTWriter.setCKConf(env);
            
            long setupTime = System.currentTimeMillis();
            logger.info("任务设置完成，总耗时: {}ms", setupTime - startTime);
            
            env.execute("l2 ods nrt writer launcher (Redis cached)");
        } finally {
            // 确保关闭Redis连接
            confManager.close();
        }
    }

    // 通过参数更新InfraConf中的consumerGroup
    private static InfraConf updateInfraConf(InfraConf infraConf, String[] args) {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String consumerGroup = parameterTool.get("consumerGroup");
        KafkaConf kafkaConf = infraConf.l2OdsMQDistributeConf.dBusConf.dBusMQConf;
        
        if (consumerGroup != null) {
            logger.info("设置自定义消费者组: {}", consumerGroup);
            DBusConf conf = new DBusConf();
            conf.dBusMQConf = new DBusConf.KafkaConf();
            conf.dBusMQConf.topic = kafkaConf.topic;
            conf.dBusMQConf.entryPoint = kafkaConf.entryPoint;
            conf.dBusMQConf.consumerGroup = consumerGroup;
            infraConf.l2OdsMQDistributeConf.setDBusConf(conf);
        } else {
            logger.info("使用默认消费者组: {}", kafkaConf.consumerGroup);
        }
        
        return infraConf;
    }
} 