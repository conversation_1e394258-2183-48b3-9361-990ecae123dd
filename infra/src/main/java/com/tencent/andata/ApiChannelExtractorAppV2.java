package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManagerWithApiSchema;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.dct.api.APIChannelExtractor;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 使用API Schema的新版ApiChannelExtractorApp
 * 支持API Schema和Hive Metastore的混合模式
 * 
 * <AUTHOR>
 */
public class ApiChannelExtractorAppV2 {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiChannelExtractorAppV2.class);
    
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        
        // 创建增强版的InfraConfManager
        InfraConf infraConf = new InfraConfManagerWithApiSchema(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf();

        // Add API Extractor Task
        APIChannelExtractor.addTaskToEnv(env, infraConf);
        // Set checkpoint, using extractor conf
        APIChannelExtractor.setCKConf(env);

        env.execute("Api extractor launcher v2 (with API Schema)");
    }

} 