package utils;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.flink.streaming.api.functions.source.ParallelSourceFunction;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.RowKind;
import utils.FlinkInternalDataFakers.FLinkInternalDataFaker;

public class FlinkFakeTypeSourceFunc implements ParallelSourceFunction<RowData> {

    private volatile boolean running = true;
    private final AtomicInteger eventsPerSecond;

    public FlinkFakeTypeSourceFunc(int initialRate) {
        this.eventsPerSecond = new AtomicInteger(initialRate);
    }

    public static final FLinkInternalDataFaker<?>[] typeFakerArr;
    public static final DataType rowDataType;
    public static final RowKind[] ROW_KINDS = RowKind.values();

    static {
        List<FLinkInternalDataFaker<?>> fakers = Arrays.asList(
                new FlinkInternalDataFakers.CharFaker("char10", 10),
                new FlinkInternalDataFakers.VarCharFaker("varchar10", 10),
                new FlinkInternalDataFakers.StringDataFaker("string"),
                new FlinkInternalDataFakers.BooleanFaker("boolean"),
                new FlinkInternalDataFakers.DecimalDataFaker("decimal103", 10, 3),
                new FlinkInternalDataFakers.TinyIntFaker("tinyint"),
                new FlinkInternalDataFakers.SmallIntFaker("smallint"),
                new FlinkInternalDataFakers.IntFaker("integer"),
                new FlinkInternalDataFakers.BigIntFaker("bigint"),
                new FlinkInternalDataFakers.FloatFaker("float"),
                new FlinkInternalDataFakers.DoubleFaker("double"),
                new FlinkInternalDataFakers.DateFaker("date"),
                new FlinkInternalDataFakers.TimeFaker("time0", 0),
                new FlinkInternalDataFakers.TimeFaker("time3", 3),
                new FlinkInternalDataFakers.TimestampFaker("timestamp0", 0),
                new FlinkInternalDataFakers.TimestampFaker("timestamp3", 3),
                new FlinkInternalDataFakers.TimestampFaker("timestamp6", 6),
                new FlinkInternalDataFakers.BinaryFaker("binary5", 5),
                new FlinkInternalDataFakers.VarBinaryFaker("varbinary5", 5),
                new FlinkInternalDataFakers.BytesFaker("bytes")
        );

        rowDataType = DataTypes.ROW(fakers.stream()
                .map(f -> DataTypes.FIELD(f.getName(), f.getProducedDataType()))
                .toArray(DataTypes.Field[]::new));

        typeFakerArr = fakers.toArray(new FLinkInternalDataFaker<?>[0]);
    }

    public DataType getRowDataType() {
        return rowDataType;
    }

    public void printFLinkInternalType() {
        System.out.println("------------------------------------------------------");
        for (int i = 0; i < typeFakerArr.length; i++) {
            String fieldName = typeFakerArr[i].getName();
            DataType dataType = typeFakerArr[i].getProducedDataType();
            System.out.printf(
                    "FieldName: %s --> TypeName: %s --> DefaultConversionClass: %s --> TypeConversionClass: %s%n"
                    , fieldName
                    , dataType.toString()
                    , dataType.getLogicalType().getDefaultConversion().getSimpleName()
                    , dataType.getConversionClass().getSimpleName());
        }
        System.out.println("------------------------------------------------------");
    }

    @Override
    public void run(SourceContext<RowData> ctx) throws Exception {
        Random random = new Random();
        while (running) {
            long startTime = System.currentTimeMillis();
            int rate = eventsPerSecond.get();
            for (int i = 0; i < rate; i++) {
                GenericRowData rowData = new GenericRowData(ROW_KINDS[random.nextInt(ROW_KINDS.length)],
                        typeFakerArr.length);
                for (int k = 0; k < typeFakerArr.length; k++) {
                    rowData.setField(k, typeFakerArr[k].get());
                }
                ctx.collect(rowData);
            }

            // 控制速率
            long elapsedTime = System.currentTimeMillis() - startTime;
            if (elapsedTime < 1000) {
                Thread.sleep(1000 - elapsedTime);
            }
        }
    }

    @Override
    public void cancel() {
        running = false;
    }

    /**
     * 动态调整数据生成速率。
     */
    public void setSourceRate(int newRate) {
        eventsPerSecond.set(newRate);
    }
}