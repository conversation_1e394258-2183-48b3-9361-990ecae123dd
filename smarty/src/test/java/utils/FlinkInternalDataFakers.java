package utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Locale;
import java.util.Random;
import java.util.function.Supplier;
import lombok.Data;
import net.datafaker.Faker;
import net.datafaker.Name;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.DataType;

/**
 * @Description : 注意：只能生成Flink Table的内部类型数据！！！
 */
public class FlinkInternalDataFakers {

    private static final Faker FAKER = new Faker(Locale.CHINA);
    private static final Random RANDOM = new Random();

    private FlinkInternalDataFakers() {
    }

    @Data
    public abstract static class FLinkInternalDataFaker<OUT> implements Supplier<OUT> {
        protected String name;

        public FLinkInternalDataFaker(String name) {
            this.name = name;
        }

        public abstract DataType getProducedDataType();
    }

    private static abstract class CharFakerBase extends FLinkInternalDataFaker<StringData> {
        protected final int len;

        public CharFakerBase(String name, int len) {
            super(name);
            this.len = len;
        }

        @Override
        public StringData get() {
            String fullName = FAKER.name().fullName();
            return StringData.fromString(fullName.substring(0, Math.min(len, fullName.length())));
        }
    }

    public static class CharFaker extends CharFakerBase {
        public CharFaker(String name, int len) {
            super(name, len);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.CHAR(len).bridgedTo(StringData.class);
        }
    }

    public static class VarCharFaker extends CharFakerBase {
        public VarCharFaker(String name, int len) {
            super(name, len);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.VARCHAR(len).bridgedTo(StringData.class);
        }
    }

    public static class StringDataFaker extends FLinkInternalDataFaker<StringData> {
        public StringDataFaker(String name) {
            super(name);
        }

        @Override
        public StringData get() {
            return StringData.fromString(FAKER.name().name() + "|" + FAKER.address().city() + "|" + FAKER.internet().emailAddress());
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.STRING().bridgedTo(StringData.class);
        }
    }

    public static class BooleanFaker extends FLinkInternalDataFaker<Boolean> {
        public BooleanFaker(String name) {
            super(name);
        }

        @Override
        public Boolean get() {
            return RANDOM.nextBoolean();
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.BOOLEAN();
        }
    }

    private static abstract class BinaryFakerBase extends FLinkInternalDataFaker<byte[]> {
        protected final int len;

        public BinaryFakerBase(String name, int len) {
            super(name);
            this.len = len;
        }

        @Override
        public byte[] get() {
            String fullName = FAKER.name().fullName();
            byte[] bytes = fullName.getBytes();
            byte[] output = new byte[len];
            System.arraycopy(bytes, 0, output, 0, Math.min(len, bytes.length));
            return output;
        }
    }

    public static class BinaryFaker extends BinaryFakerBase {
        public BinaryFaker(String name, int len) {
            super(name, len);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.BINARY(len);
        }
    }

    public static class VarBinaryFaker extends BinaryFakerBase {
        public VarBinaryFaker(String name, int len) {
            super(name, len);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.VARBINARY(len);
        }
    }

    public static class DecimalDataFaker extends FLinkInternalDataFaker<DecimalData> {
        private final int precision;
        private final int scale;

        public DecimalDataFaker(String name, int precision, int scale) {
            super(name);
            this.precision = precision;
            this.scale = scale;
        }

        @Override
        public DecimalData get() {
            BigDecimal bigDecimal = BigDecimal.valueOf(RANDOM.nextDouble()).setScale(scale, RoundingMode.HALF_UP);
            return DecimalData.fromBigDecimal(bigDecimal, precision, scale);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.DECIMAL(precision, scale).bridgedTo(DecimalData.class);
        }
    }

    public static class TinyIntFaker extends FLinkInternalDataFaker<Byte> {
        public TinyIntFaker(String name) {
            super(name);
        }

        @Override
        public Byte get() {
            return (byte) RANDOM.nextInt(128);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.TINYINT();
        }
    }

    public static class SmallIntFaker extends FLinkInternalDataFaker<Short> {
        public SmallIntFaker(String name) {
            super(name);
        }

        @Override
        public Short get() {
            return (short) RANDOM.nextInt(Short.MAX_VALUE);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.SMALLINT();
        }
    }

    public static class IntFaker extends FLinkInternalDataFaker<Integer> {
        public IntFaker(String name) {
            super(name);
        }

        @Override
        public Integer get() {
            return RANDOM.nextInt();
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.INT();
        }
    }

    public static class BigIntFaker extends FLinkInternalDataFaker<Long> {
        public BigIntFaker(String name) {
            super(name);
        }

        @Override
        public Long get() {
            return RANDOM.nextLong();
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.BIGINT();
        }
    }

    public static class FloatFaker extends FLinkInternalDataFaker<Float> {
        public FloatFaker(String name) {
            super(name);
        }

        @Override
        public Float get() {
            return RANDOM.nextFloat() * (float) Math.pow(10, RANDOM.nextInt(9));
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.FLOAT();
        }
    }

    public static class DoubleFaker extends FLinkInternalDataFaker<Double> {
        public DoubleFaker(String name) {
            super(name);
        }

        @Override
        public Double get() {
            return RANDOM.nextDouble() * Math.pow(10, RANDOM.nextInt(9));
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.DOUBLE();
        }
    }

    public static class DateFaker extends FLinkInternalDataFaker<Integer> {
        private static final int MAX_DATE = (int) LocalDate.of(2099, 12, 31).toEpochDay();

        public DateFaker(String name) {
            super(name);
        }

        @Override
        public Integer get() {
            return RANDOM.nextInt(MAX_DATE);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.DATE().bridgedTo(Integer.class);
        }
    }

    public static class TimeFaker extends FLinkInternalDataFaker<Integer> {
        private static final int SECOND_OF_DAY = 24 * 60 * 60;
        private final int scale;

        public TimeFaker(String name, int scale) {
            super(name);
            this.scale = scale;
        }

        @Override
        public Integer get() {
            int sec = RANDOM.nextInt(SECOND_OF_DAY);
            int milli = RANDOM.nextInt(1_000);
            return sec * 1_000 + milli;
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.TIME(scale).bridgedTo(Integer.class);
        }
    }

    public static class TimestampFaker extends FLinkInternalDataFaker<TimestampData> {
        private static final long MAX_SECONDS = LocalDateTime.of(LocalDate.of(2099, 12, 31), LocalTime.MAX)
                .toEpochSecond(ZoneOffset.of("+8"));
        private final int scale;

        public TimestampFaker(String name, int scale) {
            super(name);
            this.scale = scale;
        }

        @Override
        public TimestampData get() {
            long secs = Math.abs(RANDOM.nextLong()) % MAX_SECONDS;
            int nanos = RANDOM.nextInt((int) Math.pow(10, scale)) * (int) Math.pow(10, (9 - scale));
            LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(secs, nanos, ZoneOffset.UTC);
            return TimestampData.fromLocalDateTime(localDateTime);
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.TIMESTAMP(scale).bridgedTo(TimestampData.class);
        }
    }

    public static class BytesFaker extends FLinkInternalDataFaker<byte[]> {

        private final Faker faker = new Faker(Locale.CHINA);
        private final Name nameFaker = faker.name();

        public BytesFaker(String name) {
            super(name);
        }


        @Override
        public byte[] get() {
            return nameFaker.fullName().getBytes();
        }

        @Override
        public DataType getProducedDataType() {
            return DataTypes.BYTES();
        }
    }
}