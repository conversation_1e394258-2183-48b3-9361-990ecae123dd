package RateLimit;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.util.Collector;
public class RateLimiterTest {

    public static void main(String[] args) throws Exception {
        Configuration config = new Configuration();
        //指定 Flink Web UI 端口为9091
        config.setInteger("rest.port", 8081);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        // 如果本地测试，也可以使用下面的代码
        StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(config);
        env.setRestartStrategy(RestartStrategies.noRestart());

        // 初始化动态数据源，初始速率为 2000 条/秒
        DynamicTestSource source = new DynamicTestSource(20000);

        // 模拟速率调整的线程
        new Thread(() -> {
            try {
                Thread.sleep(10000); // 10 秒后降低速率
                source.setRate(500);
                System.out.println("Data rate adjusted to 500 events/second");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();

        // 模拟测试数据源
        DataStream<String> input = env.addSource(source).setParallelism(1);

        // 应用滑动窗口限流
        DataStream<String> output = input
                .keyBy(value -> "key")
                .process(new SlidingWindowRateLimiterV4(50, 3000, 1000, 10)) // 每 5 秒限流 1000 条
                .setParallelism(1);

        // 输出测试结果
        output.print().setParallelism(1);

        env.execute("Dynamic Sliding Window Rate Limiter Test");
    }

    /**
     * 模拟测试数据源，每秒生成指定数量的数据。
     */
    public static class DynamicTestSource implements SourceFunction<String> {

        private volatile boolean running = true;
        private final AtomicInteger eventsPerSecond;

        public DynamicTestSource(int initialRate) {
            this.eventsPerSecond = new AtomicInteger(initialRate);
        }

        @Override
        public void run(SourceContext<String> ctx) throws Exception {
            while (running) {
                long startTime = System.currentTimeMillis();
                int rate = eventsPerSecond.get();
                for (int i = 0; i < rate; i++) {
                    ctx.collect("event_" + i);
                }
                // 控制速率
                long elapsedTime = System.currentTimeMillis() - startTime;
                if (elapsedTime < 1000) {
                    Thread.sleep(1000 - elapsedTime);
                }
            }
        }

        @Override
        public void cancel() {
            running = false;
        }

        /**
         * 动态调整数据生成速率。
         */
        public void setRate(int newRate) {
            eventsPerSecond.set(newRate);
        }
    }

    public static class SlidingWindowRateLimiterV4 extends KeyedProcessFunction<String, String, String> {

        private final long rateLimit;
        private final long windowSize;
        private final long slideStep;
        private final int batchSize; // 每次定时器触发时输出的最大条数

        // 状态：计数窗口
        private transient ValueState<Long> countState;
        // 状态：窗口开始时间
        private transient ValueState<Long> windowStartState;
        // 状态：是否限流
        private transient ValueState<Boolean> isRateLimited;
        // 状态：缓存被限流的数据
        private transient ListState<String> bufferedEvents;

        public SlidingWindowRateLimiterV4(long rateLimit, long windowSize, long slideStep, int batchSize) {
            this.rateLimit = rateLimit;
            this.windowSize = windowSize;
            this.slideStep = slideStep;
            this.batchSize = batchSize;
        }

        @Override
        public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
            countState = getRuntimeContext().getState(new ValueStateDescriptor<>("count", Long.class));
            windowStartState = getRuntimeContext().getState(new ValueStateDescriptor<>("windowStart", Long.class));
            isRateLimited = getRuntimeContext().getState(new ValueStateDescriptor<>("isRateLimited", Boolean.class));
            bufferedEvents = getRuntimeContext().getListState(new ListStateDescriptor<>("bufferedEvents", String.class));
        }

        @Override
        public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            long currentTime = ctx.timerService().currentProcessingTime();

            Long count = countState.value();
            Long windowStart = windowStartState.value();
            Boolean rateLimited = isRateLimited.value();

            if (windowStart == null || currentTime - windowStart >= windowSize) {
                // 初始化窗口
                windowStartState.update(currentTime);
                countState.update(1L);
                // 注册下一个滑动窗口的定时器
                ctx.timerService().registerProcessingTimeTimer(currentTime + slideStep);
            } else {
                // 更新计数
                countState.update(count == null ? 1L : count + 1);
            }

            if (rateLimited != null && rateLimited) {
                // 当前处于限流状态，将数据缓存
                bufferedEvents.add(value);
                return;
            }

            if (count != null && count > rateLimit) {
                // 达到限流阈值，进入限流状态
                isRateLimited.update(true);
                bufferedEvents.add(value); // 当前数据也缓存
            } else {
                // 正常输出数据
                out.collect(value);
            }
        }

        @Override
        public void onTimer(long timestamp, OnTimerContext ctx, Collector<String> out) throws Exception {
            long currentTime = ctx.timerService().currentProcessingTime();

            // 检查并更新限流状态
            updateRateLimitStatus();

            // 输出缓存的数据并更新剩余事件
            List<String> remainingEvents = outputBufferedEvents(out);

            // 如果仍有缓存数据，注册下一次定时器
            if (!remainingEvents.isEmpty()) {
                ctx.timerService().registerProcessingTimeTimer(currentTime + slideStep);
            }

            // 清理过期状态
            clearExpiredState(currentTime);
        }

        private void updateRateLimitStatus() throws Exception {
            if (Boolean.TRUE.equals(isRateLimited.value())) {
                if (countState.value() == null || countState.value() <= rateLimit) {
                    isRateLimited.update(false); // 解除限流
                }
            }
        }

        private List<String> outputBufferedEvents(Collector<String> out) throws Exception {
            Iterable<String> buffered = bufferedEvents.get();
            List<String> remainingEvents = new ArrayList<>();
            int outputCount = 0;

            for (String event : buffered) {
                if (outputCount < batchSize) {
                    out.collect(event);
                    outputCount++;
                } else {
                    remainingEvents.add(event);
                }
            }

            bufferedEvents.update(remainingEvents);
            return remainingEvents;
        }

        private void clearExpiredState(long currentTime) throws Exception {
            if (countState.value() != null && currentTime - windowStartState.value() >= windowSize) {
                countState.clear();
            }
        }
    }

    public static class RateLimitProcess extends KeyedProcessFunction<String, String, String> {

        private final int count;
        private final long timeWindow;

        /**
         * 队列里面存储的是每一次通过时候的时间戳
         */
        private List<Long> list = new LinkedList<>();

        public RateLimitProcess(int count, long timeWindow) {
            this.count = count;
            this.timeWindow = timeWindow;
        }

        @Override
        public void processElement(String row, Context context, Collector<String> collector
        ) throws Exception {
            while (!isGo()) {
                Thread.sleep(100);
            }
            collector.collect(row);
        }

        /**
         * 滑动时间窗口限流算法
         * 在指定时间窗口，指定限制次数内，是否允许通过
         *
         * @return 是否允许通过
         */
        public boolean isGo() {
            // 获取当前时间
            long nowTime = System.currentTimeMillis();
            // 根据队列id，取出对应的限流队列，若没有则创建
            // 如果队列还没满，则允许通过，并添加当前时间戳到队列开始位置
            if (list.size() < count) {
                list.add(0, nowTime);
                return true;
            }

            // 队列已满（达到限制次数），则获取队列中最早添加的时间戳
            Long farTime = list.get(count - 1);
            // 用当前时间戳 减去 最早添加的时间戳
            if (nowTime - farTime <= timeWindow) {
                // 若结果小于等于timeWindow，则说明在timeWindow内，通过的次数大于count
                // 不允许通过
                return false;
            } else {
                // 若结果大于timeWindow，则说明在timeWindow内，通过的次数小于等于count
                // 允许通过，并删除最早添加的时间戳，将当前时间添加到队列开始位置
                list.remove(count - 1);
                list.add(0, nowTime);
                return true;
            }
        }
    }
}