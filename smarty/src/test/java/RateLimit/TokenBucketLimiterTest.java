package RateLimit;

import com.tencent.andata.utils.RowUtils;
import java.util.LinkedList;
import org.apache.flink.api.common.io.ratelimiting.GuavaFlinkConnectorRateLimiter;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import utils.FlinkFakeTypeSourceFunc;

public class TokenBucketLimiterTest {

    public static void main(String[] args) throws Exception {
        Configuration config = new Configuration();
        //指定 Flink Web UI 端口为9091
        config.setInteger("rest.port", 8081);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        // 如果本地测试，也可以使用下面的代码
        StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(config);
        env.setRestartStrategy(RestartStrategies.noRestart());

        FlinkFakeTypeSourceFunc fakeSourceFunc = new FlinkFakeTypeSourceFunc(100);
        fakeSourceFunc.printFLinkInternalType();

        // 模拟速率调整的线程
        new Thread(() -> {
            try {
                Thread.sleep(10000); // 10 秒后降低速率
                fakeSourceFunc.setSourceRate(500);
                System.out.println("Data rate adjusted to 100 events/second");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();

        DataType dataType = fakeSourceFunc.getRowDataType();
        InternalTypeInfo<RowData> typeInfo = InternalTypeInfo.of((RowType) dataType.getLogicalType());
        DataStreamSource<RowData> rowDataDs = env.addSource(fakeSourceFunc, "flinkTypeFakeSource", typeInfo)
                .setParallelism(3);

        RowUtils.TypedMapFunc<RowData, Row> mapFunc = RowUtils.getRowDataToRowMapFunc(dataType);
        SingleOutputStreamOperator<Row> rowDs = rowDataDs.map(mapFunc).returns(mapFunc.getProducedType());
        DataType producedDataType = mapFunc.getProducedDataType();
        System.out.println(producedDataType);

        // 使用 KeyedProcessFunction 实现滑动窗口限流
        DataStream<Row> limitedStream = rowDs
                .keyBy(in -> in.getField(0).toString()) // 根据某个键进行分组
                .process(new TokenBucketLimitingProcessFunction())
                .setParallelism(1);

        limitedStream.print("limitedStream: ").setParallelism(1);

        env.execute("Flink Sliding Window Rate Limiter with Blocking Example");
    }

    public static class TokenBucketLimitingProcessFunction extends KeyedProcessFunction<String, Row, Row> {

        private GuavaFlinkConnectorRateLimiter limiter;


        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            limiter = new GuavaFlinkConnectorRateLimiter();
            limiter.setRate(1);  //表示task限流2bytes/s
            limiter.open(getRuntimeContext());
        }

        @Override
        public void processElement(Row value, Context ctx, Collector<Row> out) {
            limiter.acquire(1);
            out.collect(value);
        }
    }

    public static class SlidingWindowRateLimitingProcessFunction extends
            KeyedProcessFunction<String, String, String> implements java.io.Serializable {

        private final long timeWindow;
        private final int maxRatePerWindow;
        // 用于阻塞的计数器
        // 状态变量
        private transient ValueState<Integer> requestCountState;
        private final LinkedList<Long> timestamps = new LinkedList<>();

        public SlidingWindowRateLimitingProcessFunction(int maxRatePerWindow, long timeWindow) {
            this.maxRatePerWindow = maxRatePerWindow;
            this.timeWindow = timeWindow;
        }

        @Override
        public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
            ValueStateDescriptor<Integer> descriptor = new ValueStateDescriptor<>(
                    "requestCount", // 状态名称
                    Integer.class, // 状态类型
                    0 // 默认值
            );
            requestCountState = getRuntimeContext().getState(descriptor);
        }

        @Override
        public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            long currentTime = System.currentTimeMillis();

            // 清理过期的时间戳
            while (!timestamps.isEmpty() && (currentTime - timestamps.getLast() > timeWindow)) {
                timestamps.removeLast();
            }

            // 获取当前请求计数
            int currentCount = requestCountState.value();

            // 检查当前窗口内的请求数量
            if (timestamps.size() < maxRatePerWindow) {
                // 注册定时器，设置在当前时间窗口结束时触发
                // ctx.timerService().registerProcessingTimeTimer(currentTime + 2000); // 2 秒后触发
//                ctx.timerService().registerProcessingTimeTimer(
//                        getWindowStartWithOffset(ctx.timerService().currentProcessingTime(), 0, 1) + 1);

                requestCountState.update(currentCount + 1); // 更新请求计数
                // 允许通过，记录当前时间戳
                timestamps.addFirst(currentTime);
                out.collect(value);
            } else {
                System.out.println("Rate limit reached, blocking thread...");
                // 释放后继续处理
                timestamps.addFirst(currentTime);
                out.collect(value);
            }
        }

        @Override
        public void onTimer(long timestamp, OnTimerContext ctx, Collector<String> out) throws Exception {
            super.onTimer(timestamp, ctx, out);
            // 清理过期的时间戳
            long currentTime = System.currentTimeMillis();
            while (!timestamps.isEmpty() && (currentTime - timestamps.getLast() > timeWindow)) {
                timestamps.removeLast();
            }

            // 重置请求计数
            requestCountState.clear(); // 清除状态，重置请求计数
        }

        // 释放阻塞的方法，可以在外部调用

        // 用于计算给定时间戳所从属的窗口的起点。
        private long getWindowStartWithOffset(long timestamp, long offset, long windowSize) {
            final long remainder = (timestamp - offset) % windowSize;
            // handle both positive and negative cases
            if (remainder < 0) {
                return timestamp - (remainder + windowSize);
            } else {
                return timestamp - remainder;
            }
        }
    }
}