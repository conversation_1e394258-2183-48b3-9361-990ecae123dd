package RateLimit;

import java.util.LinkedList;
import java.util.Queue;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

public class SlidingWindowRateLimiterTest {

    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 示例数据流
        DataStream<String> inputStream = env.fromElements("data1", "data2", "data3", "data4", "data5");

        // 设置速率限制
        int maxRatePerWindow = 2; // 每个时间窗口允许的最大处理数量
        long timeWindow = 1000; // 时间窗口大小（毫秒）

        // 使用 KeyedProcessFunction 实现滑动窗口限流
        DataStream<String> limitedStream = inputStream
                .keyBy(value -> value) // 根据某个键进行分组
                .process(new SlidingWindowRateLimitingProcessFunction(maxRatePerWindow, timeWindow));

        limitedStream.print();

        env.execute("Flink Sliding Window Rate Limiter with Retry Example");
    }

    public static class SlidingWindowRateLimitingProcessFunction extends KeyedProcessFunction<String, String, String> {
        private final int maxRatePerWindow;
        private final long timeWindow;
        private final LinkedList<Long> timestamps = new LinkedList<>();
        private final Queue<String> retryQueue = new LinkedList<>(); // 重试队列

        public SlidingWindowRateLimitingProcessFunction(int maxRatePerWindow, long timeWindow) {
            this.maxRatePerWindow = maxRatePerWindow;
            this.timeWindow = timeWindow;
        }

        @Override
        public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            long currentTime = System.currentTimeMillis();

            // 清理过期的时间戳
            while (!timestamps.isEmpty() && (currentTime - timestamps.getLast() > timeWindow)) {
                timestamps.removeLast();
            }

            // 检查当前窗口内的请求数量
            if (timestamps.size() < maxRatePerWindow) {
                // 允许通过，记录当前时间戳
                timestamps.addFirst(currentTime);
                out.collect(value);
            } else {
                // 达到限流，将数据放入重试队列
                retryQueue.add(value);
                // 尝试重试
                retryProcessing(ctx, out);
            }
        }

        private void retryProcessing(Context ctx, Collector<String> out) throws Exception {
            // 尝试重试
            while (!retryQueue.isEmpty()) {
                String retryValue = retryQueue.poll();
                long currentTime = System.currentTimeMillis();

                // 清理过期的时间戳
                while (!timestamps.isEmpty() && (currentTime - timestamps.getLast() > timeWindow)) {
                    timestamps.removeLast();
                }

                // 检查当前窗口内的请求数量
                if (timestamps.size() < maxRatePerWindow) {
                    timestamps.addFirst(currentTime);
                    out.collect(retryValue);
                } else {
                    // 如果仍然达到限流，放回重试队列
                    retryQueue.add(retryValue);
                    break; // 退出重试循环
                }
            }
        }
    }
}