package splice;

import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.utils.TimeUtil;
import io.vavr.control.Option;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

public class CallCenterCtxTest {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    public static void main(String[] args) throws JsonProcessingException {
        String operationNodes = "[{\"operation_id\":\"93412965\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T14:23:09+08:00\",\"operator\":\"84899\",\"operator_type\":2,\"inner_reply\":\"问题描述：<div>【问题描述】：购买咨询  "
                + "470-988</div><div>【处理方案】：用户对涨价不认可，表示不合理，告知价格已页面显示为准，用户不接受，要求投诉+打消费者协会投诉，要求核实是否符合正常涨价规律，产品变得都是用不到的东西；告知升级反馈专员，24小时内回复，要求尽快回电话 </div><div>【关键信息】：</div><div>【关联工单】：</div><div>【备注】：</div><div>[br]</div>[br]处理内容：<div>[img "
                + "src=https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754633644927_7919_image.png][img src=https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud"
                + ".com/callcenter_84899_1754633660829_1049_image.png][img src=https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754633746829_3932_image.png][img src=https://andon-ticket-1258344699.cos"
                + ".ap-guangzhou.myqcloud.com/callcenter_84899_1754633822336_7696_image.png][img src=https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754634187475_5373_image.png]</div>[br]\","
                + "\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":22,\"next_operator\":\"84899\",\"next_assign\":0,\"duration\":0,\"remark\":\"\","
                + "\"secret_content"
                +
                "\":\"eyJpdiI6InBCRFFqNVVzcVpRZ0wrWGUxaEtNY0E9PSIsIm1hYyI6IjJmMjUzN2U1MzAxN2NmMDNhOTFkNjFmOWZlZmJiMGQ4ODg2MWQ3YzFhNjg4ZGYxNzA1YjRjZmY1MTBhZmM4NWIiLCJ2YWx1ZSI6Ild4U0hUbVFCTEdEZU5tYSttUmNkWm5EV3Q4LzN4aE5VRHNhZnc5MlM1S2hzaXpCd1JCS3NvR3lHa1hDVnlmdDQifQ\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":1,\"status\":0,\"current_operator\":\"\",\"fact_assign\":0,\"post\":0,\"responsible\":\"\",\"next_responsible\":\"84899\",\"customer_fields\":\"{\\\"inner_url_black_desc_list\\\":null,\\\"inner_url_black_list\\\":null,\\\"inner_url_normal_list\\\":[\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754633746829_3932_image.png\\\",\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754633822336_7696_image.png\\\",\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754634187475_5373_image.png\\\",\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754633660829_1049_image.png\\\",\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/callcenter_84899_1754633644927_7919_image.png\\\"],\\\"operator_post\\\":2,\\\"priority\\\":2,\\\"priority_option\\\":{\\\"priority\\\":\\\"L4\\\"},\\\"update_source\\\":\\\"\\\"}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93413022\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T14:23:28+08:00\",\"operator\":\"84899\",\"operator_type\":2,\"inner_reply\":\"辛苦处理，综上\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":21,\"next_operator\":\"71009\",\"next_assign\":0,\"duration\":19,\"remark\":\"客户代表变更为 \\\"张席1（忆享云）\\\"<br/>\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":5,\"status\":22,\"current_operator\":\"84899\",\"fact_assign\":0,\"post\":2,\"responsible\":\"84899\",\"next_responsible\":\"71009\",\"customer_fields\":\"{\\\"priority\\\":2,\\\"operator_post\\\":2}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93413553\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T14:26:56+08:00\",\"operator\":\"6035\",\"operator_type\":2,\"inner_reply\":\"认领原因:1\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":22,\"next_operator\":\"6035\",\"next_assign\":0,\"duration\":208,\"remark\":\"客户代表变更为 \\\"丁兴罡（忆享云）\\\"<br/>\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":3,\"status\":22,\"current_operator\":\"71009\",\"fact_assign\":0,\"post\":2,\"responsible\":\"71009\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"priority\\\":2,\\\"visible_post\\\":2,\\\"operator_post\\\":2}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93421635\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T15:22:21+08:00\",\"operator\":\"46917\",\"operator_type\":2,\"inner_reply\":\"我的角色：服务监理[br]风险识别是否准确：是[br]处置方式：电话安抚[br]客户情况：不接受[br]升级情况：报备/升级口碑组[br]更多描述：<div>会议价格问题 已经解释原因 用户不认可 已升级协助</div>\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":22,\"next_operator\":\"6035\",\"next_assign\":0,\"duration\":3325,\"remark\":\"\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":21,\"status\":22,\"current_operator\":\"6035\",\"fact_assign\":0,\"post\":2,\"responsible\":\"6035\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"priority\\\":2,\\\"visible_post\\\":2,\\\"operator_post\\\":2}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93441164\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T17:36:47+08:00\",\"operator\":\"6035\",\"operator_type\":2,\"inner_reply\":\"【是否有风险】[br]否[br]【通话纪要】[br]回电；[img src=https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/ticket-detail_6035_15128653_1754645803959_761_image.png]无人接听[br][br][br]\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":22,\"next_operator\":\"\",\"next_assign\":0,\"duration\":8066,\"remark\":\"\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":45,\"status\":22,\"current_operator\":\"6035\",\"fact_assign\":0,\"post\":2,\"responsible\":\"6035\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"inner_url_black_desc_list\\\":null,\\\"inner_url_black_list\\\":null,\\\"inner_url_normal_list\\\":[\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/ticket-detail_6035_15128653_1754645803959_761_image.png\\\"],\\\"operator_post\\\":2,\\\"priority\\\":2}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93441189\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T17:36:58+08:00\",\"operator\":\"6035\",\"operator_type\":2,\"inner_reply\":\"已成功向客户发送短信，短信内容为：尊敬的腾讯会议用户，您好！关于您反馈的问题需要电话和您进行沟通，后续将再次和您取得联系，请您保持手机畅通！\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":22,\"next_operator\":\"6035\",\"next_assign\":0,\"duration\":11,\"remark\":\"\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":21,\"status\":22,\"current_operator\":\"6035\",\"fact_assign\":0,\"post\":2,\"responsible\":\"6035\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"priority\\\":2,\\\"operator_post\\\":2}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93451183\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T18:45:00+08:00\",\"operator\":\"6035\",\"operator_type\":2,\"inner_reply\":\"【是否有风险】[br]是[br]【通话纪要】[br]回电；婉拒用户诉求，价格以官网为准，用户提及向消费者协会反馈[img src=https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/ticket-detail_6035_15128653_1754649895472_6214_image.png]舆情已报备[br][br][br]通过企点外呼方式外呼客户, 通话录音: <a href='javascript:void(0);' data-calltype=qidian data-callid=7359532420598640640 data-ticket-id=15128653>查看具体通话内容</a>。\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":22,\"next_operator\":\"\",\"next_assign\":0,\"duration\":4082,\"remark\":\"\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":44,\"status\":22,\"current_operator\":\"6035\",\"fact_assign\":0,\"post\":2,\"responsible\":\"6035\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"inner_url_black_desc_list\\\":null,\\\"inner_url_black_list\\\":null,\\\"inner_url_normal_list\\\":[\\\"https://andon-ticket-1258344699.cos.ap-guangzhou.myqcloud.com/ticket-detail_6035_15128653_1754649895472_6214_image.png\\\"],\\\"operator_post\\\":2,\\\"priority\\\":2}\",\"request_source\":\"\",\"target_extern_status\":1,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93451196\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T18:45:09+08:00\",\"operator\":\"6035\",\"operator_type\":2,\"inner_reply\":\"综上，结单\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":3,\"next_operator\":\"\",\"next_assign\":0,\"duration\":9,\"remark\":\"问题分类从 \\\"\\\" 变更为 \\\"云产品咨询\\\" <br/>原因分类从 \\\"\\\" 变更为 \\\"其他咨询\\\" <br/>\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":1,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":16,\"status\":22,\"current_operator\":\"6035\",\"fact_assign\":0,\"post\":2,\"responsible\":\"6035\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"priority\\\":2,\\\"operator_post\\\":2}\",\"request_source\":\"\",\"target_extern_status\":2,\"data_type\":\"ticket_operation\"},{\"operation_id\":\"93451690\",\"ticket_id\":\"15128653\",\"operate_time\":\"2025-08-08T18:53:06+08:00\",\"operator\":\"v_pdxgding\",\"operator_type\":2,\"inner_reply\":\"我的角色：值班经理[br]风险识别是否准确：是[br]处置方式：电话安抚[br]客户情况：不接受[br]升级情况：无升级动作[br]更多描述：<div>对于腾讯会议涨价不接受，已经多次安抚，用户提及消协投诉，舆情已报备</div>\",\"extern_reply\":\"\",\"qcloud_comment_id\":\"0\",\"target_status\":3,\"next_operator\":\"6035\",\"next_assign\":0,\"duration\":477,\"remark\":\"\",\"secret_content\":\"\",\"is_undo\":0,\"company_id\":0,\"target_post\":2,\"cc_person\":\"\",\"operation_type\":21,\"status\":3,\"current_operator\":\"6035\",\"fact_assign\":0,\"post\":2,\"responsible\":\"6035\",\"next_responsible\":\"6035\",\"customer_fields\":\"{\\\"priority\\\":2,\\\"visible_post\\\":2,\\\"operator_post\\\":2,\\\"assign_company_id\\\":1}\",\"request_source\":\"\",\"target_extern_status\":2,\"data_type\":\"ticket_operation\"}]\n";

        System.out.println(processSimpleContext(MAPPER.readTree(operationNodes)));
    }

    private static void handleTicketScene(JsonNode contextNode) {
        contextNode.forEach(context ->
                ((ObjectNode) context).put("data_type", "ticket_operation"));
    }

    private static String processSimpleContext(JsonNode contextNode) throws JsonProcessingException {
        List<JsonNode> nodes = new ArrayList<>();
        if (contextNode.isArray()) {
            handleTicketScene(contextNode);
            addNodesToList(contextNode, nodes);
        }

        // 将CallCenter信息数据合并到节点列表中
        fetchCallCenterInfoForTicket()
                .peek(node -> mergeCallCenterData(node, nodes));

        // 对流水数据进行排序
        nodes.sort(Comparator.comparing(n -> TimeUtil.getEpochMilli(n.get("operate_time").asText())));
        return nodes.toString();
    }

    private static void addNodesToList(JsonNode contextNode, List<JsonNode> nodes) {
        contextNode.forEach(nodes::add);
    }

    private static void mergeCallCenterData(JsonNode jsonNode, List<JsonNode> nodes) {
        Option.of(jsonNode)
                .map(node -> Match(node).of(
                        Case($(JsonNode::isArray), (JsonNode n) -> StreamSupport.stream(n.spliterator(), false)),
                        Case($(JsonNode::isObject), (JsonNode n) -> Stream.of(n)),
                        Case($(), (JsonNode n) -> Stream.<JsonNode>empty())
                ))
                .getOrElse(() -> Stream.<JsonNode>empty())
                .filter(n -> n.has("operate_time"))
                .forEach(nodes::add);
    }

    private static Option<JsonNode> fetchCallCenterInfoForTicket() throws JsonProcessingException {
        String ticketCallingData =
                "[{\"ticket_id\":15128653,\"user\":\"6035\",\"call_id\":\"7359532420598640640\",\"create_time\":\"2025-08-08 18:34:22.0\",\"call_direction\":1,\"user_id\":\"丁兴罡（忆享云）\",\"data_type\":\"cc_operation\",\"content\":\"客户: "
                        + "喂。\\n客户: 喂。\\n坐席: 诶，您好先生，我这边是腾讯会议的工作人员，工号21014。\\n客户: 诶。\\n坐席: 诶您好，这边还有收到您这边来电反馈，关于那个腾讯会议的一个价格调整的一个问题是吧。\\n客户: 对，我有2个账号。\\n客户: 然后我现在要给这两个账号分别开始续费的时候，我就发现。\\n客户: 你们那个账，你们那个价格分别都涨了一倍多。\\n客户: "
                        + "然后我有两个账号，我这两个账号都得找一辈。\\n客户: "
                        + "我都得从400多直接直接蹦到1000块。\\n坐席: 嗯。\\n坐席: 嗯，这个确。\\n客户: 我觉得是一非常非常，我觉得是非常非常不合理的。\\n坐席: 这个确实非常抱歉给您带来不便，因为这个腾讯会议的话，这边的话，它确实在，呃，今年年初的时候，这边确实有对有进行一些价格的一些调整，这个确实非常抱歉给您带来不便了。\\n客户: 那这都不算是，这都不算是调整了。\\n客户: "
                        + "您能明白吗？我有两个账号，如果是有三四个，我们这种有两三个账号的公司的话。\\n客户: 就是你你们你们觉得稳定了客户之后，要给我们涨价，其实都是能理解的，你涨个100块钱，几十块钱，我们都能理解。\\n客户: 但你这样直接往上翻2倍的，这个是极其不合理的。\\n坐席: 这个确实。\\n客户: 让我用服挂了之后直接拿镰刀收割的意思吗？\\n坐席: "
                        + "这个确实非常抱歉，这个并非是这个意思啊，因为目前的话这边的话有做一些功能。\\n客户: "
                        + "你这。\\n客户: 不是，那你给我说一下有什么有什么方式可以，就是你们有没有任何解决方式吧，不然的话，我只能这么理解，就是把我们这些客户养熟了之后。\\n客户: 一把年头割了呗。\\n坐席: 这个并非是意思啊，因为这个确实啊，就是说这个在呃，今年年初的时候，这边的话，确实对价格一些做出了一些调整哈，包括。\\n客户: "
                        + "你们店在年初对价格做出了调整，这些我已经听了好几遍了，所以您不用再跟我说我可以选择什么套餐，然后你们在几月几号做了调整，这些我都知道，你就不用再说了，我让反馈，包括我投诉。\\n客户: 也不是想知道是你们在哪一天做出调整的。\\n客户: 你知道我在说什么意思？\\n坐席: 理解。\\n客户: 你也知道我，你们知道我诉求是什么，你就直接跟我说，您回给我这个电话能提供什么就行了。\\n坐席: "
                        + "嗯，当前这个价格确实只能以您这边官网页面显示为准，这个确实没办法给您恢复到您之前470的这个价格，的确非常抱歉。\\n坐席: 具体价格以您这边官网页面显示的为准。\\n客户: 所以您回个我电话是干嘛呢。\\n坐席: 这个确实非常抱歉给您带来不便，因为这个确实您这边之前来电反馈过后的话，我们这边也就是核实了这个情况过后，给您做的这个回电。\\n客户: 您这个不用核实。\\n客户: "
                        + "那还有啥有啥可核实的呢？我我提出了这个诉求，你的诉求是官网是什么就是什么。\\n客户: 你当时都能给我回电话了，还用着现在回吗？\\n坐席: 抱歉，给您带来不便哈。\\n客户: 那就再帮我反馈一下。\\n客户: 好吧，你这个这个三两。\\n客户: 翻两番两个，我两个账号翻4番，这个我就是不接受。\\n客户: 咱们这还没有投诉部门，咱们这只叫售后，没有投诉。\\n坐席: 那行。\\n坐席: "
                        + "您投诉的数据，我这边的话。\\n客户: "
                        + "就是咱们这儿不但。\\n客户: 对，我投诉的需求就是我需要咱们给出一个方案，就是我不能接受这个价格，一下全两个账号给我翻4番这个事儿。\\n坐席: 你说。\\n坐席: 就确实。\\n客户: 咱们是是要针对我们的诉求有一个解决方案的，你的解决方案不能是。\\n客户: 我们啥也做不了。\\n坐席: 这个确实非常抱歉。\\n客户: 或者就是我们，我们就是这么规定的。\\n客户: 那我不行。\\n客户: "
                        + "所以你给你给，所以你给我打电话，就是说。\\n客户: 嗯，就是说一句就是抱歉，就是如果这样的话就不叫解决方式。\\n坐席: 这个确实非常抱歉，因为这个目前的话，这边价格确实有做相关的调整，目前的话这边只能以您这边官网实际面显示的金额为准。\\n客户: 你刚才说过这句话了？\\n客户: 你记得吗？你输过了？\\n坐席: "
                        + "这个理解确实非常抱歉给您带来不便，但是目前的话，因为这边确实只能以官网这边实际显示金额为准，这个没办法再做调整。\\n客户: 您又说了一遍。\\n客户: 你听到了吗？你这是真的很有趣，你又说了一遍，一模一样的，你要是解决不了这个方式，那行，咱们投诉部门到底在哪，咱们有没有投诉部门。\\n坐席: 就是用户信记录。\\n客户: 咱们这么大的企业，没有个投诉部门，没有个投诉部门吗。\\n坐席: "
                        + "我这里就能受理您的投诉啊。\\n客户: "
                        + "嗯，对啊，你的投诉我投诉了，然后你只给我说了一下，一直在重复这个网络上一句话，这个我早就知道了呀。\\n坐席: 但是这个。\\n坐席: 嗯，没法在做，不再给您回复啊。\\n客户: 你你没有，就是除非是你给你给我一个什么解决方案，我接不接受，你什么都没说，你直接让我接受的意思吗。\\n坐席: 这个目前只能以您这边官网页面显示的金额为准。\\n客户: 嗯，对呀，你还是就这一句话呀。\\n坐席: "
                        + "这个确实非常抱歉，当前这边确实没办法去规则这个价格。\\n客户: 我没有让你维护到之前的价格。\\n坐席: 您的。\\n客户: 我说我是能接受涨幅的。\\n客户: 但是你。\\n客户: 但是咱们这个直接往上翻两翻的，我想知道一下是是什么原因。\\n坐席: 所以说价格先调，然后功能上的话也有做一些相关的调整啊。\\n客户: 没有你们这个功能我仔细研究过了，它其实没有任就是。\\n客户: "
                        + "除非有人用得着哈，有些人在公司就是有几千人上万人的，他们用得着。\\n客户: 但中国绝大部分是没有这么大的企业的，对吧。\\n客户: 我们为什么需要把80个人升到100？\\n客户: 我们为什么会需要100方开会呢？\\n客户: 可能我知道市场有人用得着啊，但是绝大部分肯定是用不着的。\\n客户: 腾讯用得着，但是我们这种。\\n客户: 就是人类普通的企业，它绝对是用不着的。\\n坐席: "
                        + "这个理解您的心情，您这边的话可以根据您的需求去进行购买就可以了，如果说有需要的话。\\n客户: 我们需要购买，如果我不需要购买的话，我就不需要给您再打这个电话了。\\n客户: 因为这两年我们一直都在用，从疫情的时候我们就在用，所以一直在涨价，我们就一直在买。\\n坐席: 这个。\\n坐席: 这个理解您。\\n客户: 并不是说你把你让我们把这个用习惯了，涨个价，然后跟我们说你还用用不用稳。\\n坐席: "
                        + "关于这个价格优。\\n客户: "
                        + "不是这样的，知道吗？\\n坐席: 这个理解您十分抱歉，给您带来不便。\\n客户: 那我不接，那我不接受，我需要你们再反馈一下。\\n坐席: 这个确实没办法给到您其他的一些方案哈。\\n客户: 还是您反馈也不能再反馈了。\\n坐席: 这个确实没办法给到您其他的一些方案哈。\\n客户: 我知道你这没办法，但是我现在没有办法接受这个事情，你帮我，咱们如果这有投诉部门的话。\\n客户: 麻烦您反馈一下。\\n坐席: "
                        + "我这边就能受理您的投诉哈。\\n客户: 嗯，对啊，所以你没有给出来任何方案，我可以我也可以不接受啊。\\n客户: 对吧，然后你不能给我的一个解决，对，我不想在这难为你。\\n坐席: 这个确实非常抱歉。\\n客户: 所以就是咱们这谁能给出来一个理由的，你找谁跟我说一下，其实就是这。\\n坐席: "
                        + "这个目前确实因为这个腾讯会议功能上也做一些调整，价格上的话，这边的话也有做相应调整，目前续改只能建议您这边以官网的一个实际金额显示为准。\\n客户: 是啊，因为你只跟我说。\\n客户: 一句话，我不相信咱们的客服就是只会说一句话的。\\n坐席: 目前的话没有其他方案可以为您提供的哈。\\n客户: "
                        + "对，你没有，但是我只需要你反馈一下，我不需要你帮我解决这个问题了，你帮我反馈一下，这是我现在的需求，我现在换需求了，我不需要你给我方式了，我需要你向我向上反馈一下。\\n坐席: 腾。\\n坐席: 腾讯。\\n坐席: 腾讯。\\n客户: 可以吗？\\n坐席: 腾讯会议测，目前无法满足到您的需求。\\n客户: 你可以代表腾讯会议说话是吗？\\n坐席: 对。\\n客户: 行，那我跟消费者协会反馈。\\n坐席: "
                        + "这个确实非常抱歉给您带来不便，您看还有其他需要协助到的吗？\\n客户: 用户已挂机。\",\"operate_time\":\"2025-08-08 18:34:27.0\"},{\"ticket_id\":15128653,\"user\":\"6035\",\"call_id\":\"7359517724478857216\",\"create_time\":\"2025-08-08 17:35:58.0\","
                        + "\"call_direction\":1,\"user_id\":\"丁兴罡（忆享云）\",\"data_type\":\"cc_operation\"},{\"ticket_id\":15128653,\"user\":\"84899\",\"call_id\":\"7359466434725994496\",\"create_time\":\"2025-08-08 14:23:09.0\","
                        + "\"call_direction\":2,\"user_id\":\"付永娟（忆享云）\",\"data_type\":\"cc_operation\",\"content\":\"客户: 工号。\\n客户: 21031。\\n客户: 为您服务。\\n坐席: 您好，请问有什么可以帮您？\\n客户: 哎，你好，我有两个账号，然后其中有一个账号刚刚过期了，我要续费，然后这个续费的价格翻了2倍多。\\n客户: "
                        + "我想问一下原因。\\n坐席:"
                        + " 嗯，我帮您查一下女士，您提供一下之前购买会员的账号，手机号说一下。\\n客户: 1860041。\\n坐席: 嗯。\\n客户: 0689。\\n坐席: 0689，诶女士您贵姓怎么称呼您呢。\\n客户: 嗯，我姓付，你就直接解解答一下我这个问题吧。我觉得这个太理。\\n客户: 每年都在涨，但现在这个涨，你们这个涨法。\\n坐席: 好的，我。\\n客户: 那是什么规定里的？\\n坐席: "
                        + "好的，我先查一下付女士，请稍等。\\n坐席: "
                        + "感谢您耐心等待女士，诶这边的话看到咱们之前的话也有购买这个包年的会员对吧。\\n客户: 我还有另外一个账号，我那个账号开的更早，是每个月在包月付钱。\\n坐席: 嗯。\\n坐席: 嗯，这块的话我给您说一下相关哈，首先的话就是说从嗯产品的话都是在不断的更新迭代，然后价格的话也有相应的一个变动。\\n客户: 不过。\\n客户: "
                        + "我我知道，我知道你们更新迭代不代表的就是我们原来的价格就要一直成倍成倍的往上翻，我们就没有别的选择，就必须非要跟着你们这个价格往上。\\n坐席: 我帮您看了。\\n客户: 更新迭代。\\n坐席: 嗯，您说。\\n客户: 对，更新迭代的意思，不是说我每次交钱都要比上一次更更贵。\\n客户: 然后把便宜的这种，把稍微便宜一点的会员全部取消掉。\\n客户: 你之前是从对之前。\\n坐席: 首先呢。\\n坐席: "
                        + "嗯。\\n客户: "
                        + "我这个我这三年每年这个费用都在往上涨，但你这次涨了一倍多。\\n客户: 这个是。\\n客户: 这个是很不合理的。\\n客户: 嗯，我说完了。\\n坐席: 嗯，好女士，我这边的话和您同步查了一下哈，我们目前的话，专业版的一个价格的话，如果是包年的话，那我们一年的价格是988元这样的女士。\\n客户: 我知道啊，我就是看到了才给您打电话的呀。\\n坐席: 理解您说的，那您目前的话有什么诉求呢女士。\\n客户: "
                        + "我的诉求不是说了吗？我我的我续费花的是400多，你再让我付一块1000块钱。\\n坐席: 嗯。\\n客户: 这个诉求啊。\\n客户: 我不然我就直接去了呀，我还打电话干嘛呢。\\n坐席: 嗯。\\n坐席: 理解您说的。首先如果说我们有这。\\n客户: 我们那个月费，我们的月费也在这一直涨，我们这个年费也在一直涨。\\n客户: 但这个你们这个年费涨我能理解，但是你涨的太多了。\\n客户: 然后跟我说就是因为。\\n客户: "
                        + "便宜的，有的稍微便宜一点的那个会员他没有了，直接取消了。\\n客户: 我是要续的。\\n客户: 然后就就是这个你们这个你们完全就是把我们合理接受的一个。\\n坐席: 理解你说的。\\n客户: 会员给取消掉了，这个我不接受。\\n坐席: "
                        + "看到咱们之前的话是这个个人会员版的一个产品，这个目这个产品的话，目前确实是下架了的女士，如果说我们后续有呃这个使用的一个需求的话，呃，建议您的话可以购买这个除了免费版本，就是这个专业版本交育还年的话是988，如果说是连续包月的话，是88元一个账号一个月，还有就是单个账号的话，一个月是98元这样的女士。\\n客户: 不是，你是完全。\\n客户: 您是完全没有在听我说什么吧？\\n坐席: "
                        + "我能听得明白您说的，然后。\\n客户: "
                        + "我知道那个是980，对呀，我知道啊。\\n客户: 我对你不用再跟我，你不用再跟我介绍你这些项目了，我都知道。\\n坐席: 嗯，然后。\\n坐席: 嗯。\\n客户: 你知道我在说什么，你就不要跟我绕关，你不要跟我绕圈子了。\\n坐席: 诶，就是说如果说您有需要的话，需要以页面价格显示为准的，这个是无法做调整的女士。\\n客户: 咱们投诉部门是什么？\\n坐席: 嗯，咱们这边的话就是。\\n客户: "
                        + "你请帮忙帮我转一下。\\n客户: "
                        + "还是你们没有投诉部门？\\n坐席: 嗯，咱们的话就是售后服务的，您您需要投诉什么呢？我可以给您做一个对应的反馈啊。\\n客户: 我我。\\n坐席: 价格这块对吧。\\n客户: 我觉得你们是你们是已经调整好一个话术了吗。\\n客户: 他不可能就是从你们涨价之后。\\n客户: 全中国就只有我一个人打过电话问过这个事情，对吧。\\n客户: "
                        + "你们自己觉得合理吗？我知道一个一个项目它是有涨幅的空间的，但它涨幅的空间不可能是这么大。\\n客户:"
                        + " 他在哪都是不可能的。\\n客户: 所以咱们连现在连投诉我们反馈这个事情的一个渠道都没有了。\\n坐席: 理解您说的。\\n客户: 还是需要我们直接联系跟消费者协会去跟他沟通一下。\\n客户: 我觉得这都是现在，现在你们在这个市场上，你们在做这个事情，我们都已经接受了。然后每个。\\n客户: 我那个月付，每年缴期加。\\n客户: 年付，每年涨一次价我都是可以接受的，就涨100块，每个月涨涨十几都是可以的。\\n客户: "
                        + "但是你们现在直接翻着跟头的往上找你。\\n客户: 你们自己觉得合理，用户觉得不合理啊。\\n客户: 而且我都我完全对比，我完全对比了你们这个，你们的这个在更新的项目是什么，你们更新的全部都是用不到的项目。\\n坐席: 理解您。\\n客户: 而且你告诉我，你们连投诉部门都没有。\\n客户: 就是我们只能接受你涨价，不能不能提意见。\\n坐席: 理解您说的，那您对这块有疑问的话，我再电话。\\n客户: "
                        + "您如果就只会，您如果就只用过，您就只在这个车上一直给我念你们自己的这个。\\n客户: 用户协调用户价钱的条文的话，那你如果跟我说，你们这没有投诉部门，我们反馈这个意见，这个价钱。\\n客户: 你们取完全取消了，这些是没有用的。\\n客户: 那我们就我没有必要再跟你说了，我去跟消费者协会投诉好吧。\\n坐席: 首先能理解您说的女士。\\n客户: 咱们是咱们是不是。\\n坐席: 嗯，你先说。\\n客户: "
                        + "不，我觉得你要是接下来还是给我念，那你们那种什么条文规定的话，你就不用再跟我说啥了，你就跟我说是不是就是这样。\\n客户: 我现在也在录着音，我现在就跟我现在就跟你确认一下，是不是事实就是我说的这样。\\n坐席: 首先能听得明白您说的，也理解您说的哈女士，其次您对这方面有疑义以及您要求投诉的话，我会帮您升级反馈到专员，24小时之内的话给到您回复，辛苦，您的话先等待一下。\\n客户: "
                        + "你好，大概多久会给我回电话？\\n坐席: "
                        + "这边的话是24小时之内回复，具体的一个回复时间的话，需要以专员回复的时间为准。\\n客户: 行，那麻烦你们尽快，你尽快反馈一下，让他给我回一个电话，或者就拿出来，咱们有没有符合任何在消费者市场。\\n客户: 符合的一个条文就是你是符合一个基本的涨价规律的。\\n客户: 我觉得香奈儿都没有像你这么涨价的，直接往上翻翻两翻翻。\\n客户: 好吧，谢谢。\\n坐席: "
                        + "理解您说的不客气，电话之后的话就帮您做升级的反馈，您看还有其他方面帮到您吗。\\n客户: 没有了。\\n客户: 没有了。\\n坐席: 诶，那这边的话就先不打扰到您付女士，也祝您生活愉快，也请对本次服务做出评价，再见女士。\\n客户: 用户已挂机。\",\"operate_time\":\"2025-08-08 14:13:04.0\"}]\n";

        return Option.of(MAPPER.readTree(ticketCallingData));
    }
}