package splice;


import static com.tencent.andata.utils.IterableUtils.getElementsWithContext;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.config.ChunkConfig;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.trigger.*;
import com.tencent.andata.smart.utils.SpliceUtils;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.control.Option;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;

public class SpliceUtilsTest {

    private SpliceUtils spliceUtils = new SpliceUtils();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static String REGEXPATTERN = "^\\d{2}-\\d{2} \\d{2}:\\d{2}";


    private List<JsonNode> item;

    private static List<JsonNode> getInputData() throws Exception {
        String filePath = "src/main/resources/operation.txt"; // 替换为你的文件路径
        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
        // 将json转为List<JsonNode>
        return objectMapper.readValue(ticketOperationStr, new TypeReference<List<JsonNode>>() {});

    }

    /**
     * 生成上下文
     */
    private String buildContext(List<JsonNode> itemList, Strategy strategy) {
        return io.vavr.collection.List
                .ofAll(getElementsWithContext(itemList))
                .map(item -> buildItemContext(item, strategy))
                .filter(Option::isDefined)
                .map(Option::get)
                .foldLeft(
                        new StringBuilder(),
                        (sb, itemStr) -> appendWithSizeLimit(sb, itemStr, strategy.chunk.maxSize)
                ).toString().trim();
    }

    /**
     * 在不超过最大大小的情况下追加字符串
     */
    private static StringBuilder appendWithSizeLimit(StringBuilder sb, String str, long maxSize) {
        return Option.when(
                !sb.toString().contains(str.replaceAll(REGEXPATTERN, "")) && sb.length() + str.length() <= maxSize,
                () -> sb.append(str)
        ).getOrElse(sb);
    }

    /**
     * 处理单个item的上下文
     */
    private Option<String> buildItemContext(ElementContext<JsonNode> item, Strategy strategy) {
        return Option.of(spliceUtils.execute(item, strategy));
    }

    private static String getInputData(String dataType) {
        return String.format("{\n"
                + "    \"operation_id\": 92215973,\n"
                + "    \"ticket_id\": 14992008,\n"
                + "    \"service_channel\": 2,\n"
                + "    \"operate_time\": \"2025-07-22T09:54:48\",\n"
                + "    \"operator\": \"71009\",\n"
                + "    \"operator_type\": 2,\n"
                + "    \"inner_reply\": \"综上，结单\",\n"
                + "    \"extern_reply\": \"\",\n"
                + "    \"qcloud_comment_id\": 0,\n"
                + "    \"target_status\": 3,\n"
                + "    \"next_operator\": \"\",\n"
                + "    \"next_assign\": 0,\n"
                + "    \"duration\": 8,\n"
                + "    \"remark\": \"问题分类从 \\\"\\\" 变更为 \\\"客户咨询类\\\" <br/>原因分类从 \\\"\\\" 变更为 \\\"其他咨询\\\" <br/>\",\n"
                + "    \"secret_content\": \"\",\n"
                + "    \"is_undo\": 0,\n"
                + "    \"company_id\": 1,\n"
                + "    \"target_post\": 2,\n"
                + "    \"cc_person\": \"\",\n"
                + "    \"operation_type\": 16,\n"
                + "    \"status\": 22,\n"
                + "    \"current_operator\": \"71009\",\n"
                + "    \"fact_assign\": 0,\n"
                + "    \"post\": 2,\n"
                + "    \"responsible\": \"71009\",\n"
                + "    \"next_responsible\": \"71009\",\n"
                + "    \"customer_fields\": \"{\\\"priority\\\":2,\\\"operator_post\\\":2}\",\n"
                + "    \"request_source\": \"\",\n"
                + "    \"target_extern_status\": 2,\n"
                + "    \"data_type\": \"%s\"\n"
                + "}", dataType);
    }

    private static Strategy buildTestStrategy(String chunkType, String dataType) throws Exception {
        Strategy strategy = Strategy.builder()
                .id(1)
                .sceneIdentify("14992008")
                .name("CallCenter结单质检")
                .scene(Scene.Ticket)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'ticket_operation'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(ChunkConfig.CallCenterReply)
                .analyzes(Collections.singletonList(AnalyzeConfig.Professional_Skills).toArray(new Analyze[0]))
                .build();

        strategy.trigger.setTriggerTimestamp(1754633447489L);

        strategy.trigger.data = new JsonNode[]{objectMapper.readTree(getInputData(dataType))};

        return strategy;
    }

    @Before
    public void setUp() throws Exception {
        item = getInputData();
    }

    @Test
    public void testExecute() throws Exception {
        Strategy strategy = buildTestStrategy("Conversation", "ticket_operation");
        // assert !buildContext(item, strategy).trim().isEmpty();

        String contenxt = buildContext(item, strategy);
        System.out.println(contenxt);
    }
}