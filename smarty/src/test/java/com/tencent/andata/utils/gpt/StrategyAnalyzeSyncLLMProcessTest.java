package com.tencent.andata.utils.gpt;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.etl.process.StrategyAnalyzeSyncLLMProcess;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import com.tencent.andata.utils.HttpClientUtils;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * StrategyAnalyzeSyncLLMProcess的单元测试类
 * 使用Mockito模拟HTTP请求和响应
 * <p>
 * 注意事项:
 * 1. 我们测试的是包装HTTP客户端的服务类，所以这里使用Mockito模拟HTTP客户端是合适的
 * 2. 对于更完整的集成测试，可以考虑使用WireMock来模拟完整的HTTP服务
 * 3. 测试用例覆盖了正常流程以及各种异常情况（无效对话，返回无效JSON，空响应等）
 * 4. 为了测试非公开的内部类，使用反射注入模拟的HTTP客户端
 * <p>
 * 测试最佳实践:
 * - 每个测试方法专注于测试单一行为/场景
 * - 使用明确的测试方法命名表达测试意图
 * - 测试边界条件和异常路径
 * - 使用合适的断言验证结果
 */
public class StrategyAnalyzeSyncLLMProcessTest {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private StrategyAnalyzeSyncLLMProcess syncProcessorLLM;
    private HttpClientUtils mockHttpClient;
    private Collector<Strategy> mockCollector;


    /**
     * 创建测试用的Strategy对象
     * 包含模拟的对话内容和基本设置
     */
    private static Strategy buildTestStrategy() {
        Strategy strategy = Strategy.builder()
                .id(2)
                .sceneIdentify("@TGS#2O47H4IPN")
                .name("WebIM结单质检")
                .scene(Scene.WebIM)
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(Chunk.builder()
                        .type(ChunkType.Conversation)
                        .maxSize(32 * 1024)
                        .delimiter("\n")
                        .conversationSpliceType(SpliceType.WebIMQualityInspection)
                        .conversation("10-18 17:30 坐席 一线 江秦鹏（金慧） （在线-腾讯会议购买咨询）回复：您好，我是腾讯会议官网的人工售前经理，目前腾讯会议分为多个版本，不同的版本对应不同的场景需求，请问您想咨询解决什么问题呢？\n" +
                                "10-18 17:41 坐席 一线 江秦鹏（金慧） （在线-腾讯会议购买咨询）回复：您好，请问还在吗？\n" +
                                "10-18 18:12 坐席 一线 江秦鹏（金慧） （在线-腾讯会议购买咨询）回复：您好，我看您一直没有回复，您还有其他问题要咨询吗？\n" +
                                "10-18 18:13 坐席 一线 江秦鹏（金慧） （在线-腾讯会议购买咨询）触发动作：待客户确认结单\n" +
                                "10-21 18:13 超过3天系统自动结单")
                        .build())
                .analyzes(Arrays.asList(
//                                AnalyzeConfig.Professional_Skills,
//                                AnalyzeConfig.Service_Awareness,
//                                AnalyzeConfig.Communication_Skills,
//                                AnalyzeConfig.WebIM_Service_Timeliness,
                                AnalyzeConfig.Service_Specifications)
                        .toArray(new Analyze[0]))
                .build();

        strategy.trigger.setTriggerTimestamp(System.currentTimeMillis());

        return strategy;
    }

    @Before
    public void setUp() throws Exception {
        // 创建处理器实例
        syncProcessorLLM = StrategyAnalyzeSyncLLMProcess
                .builder()
                .modelUrl("http://30.46.122.172:8080/gpt-api/api")
                .modelToken("2b663fe95b604593b74697950cac6c2e")
                .build();

        // 创建模拟的Collector
        mockCollector = mock(Collector.class);

        // 初始化处理器
        syncProcessorLLM.open(new Configuration());

        // 创建模拟的HTTP客户端 - 使用mock而不是实例
        mockHttpClient = mock(HttpClientUtils.class);

        // 注入模拟的HTTP客户端
        injectMockHttpClient(syncProcessorLLM, mockHttpClient);
    }

    /**
     * 测试处理有效对话的情况
     * 验证正常情况下LLM请求处理流程
     */
    @Test
    public void testProcessValidConversation() throws Exception {
        // 准备测试数据
        Strategy strategy = buildTestStrategy();

        // 模拟HTTP响应
        CompletableFuture<String> successResponse = CompletableFuture.completedFuture(
                "{\"requestId\":\"" + UUID.randomUUID() + "\",\"data\":{\"answer\":{\"坐席\":\"测试坐席\",\"分析结果\":\"通过\"}}}"
        );

        // 设置模拟HTTP客户端的行为
        String testUrl = "http://30.46.122.172:8080/gpt-api/api";
        when(mockHttpClient.post(Mockito.eq(testUrl), any(Map.class), any(Map.class)))
                .thenReturn(String.valueOf(successResponse));

        // 为JSON修复服务设置模拟响应
        CompletableFuture<String> mockJsonRepairResponse = CompletableFuture.completedFuture(
                "{\"code\":0,\"message\":\"success\",\"data\":{\"converted_data\":\"{\\\"坐席\\\":\\\"测试坐席\\\",\\\"分析结果\\\":\\\"通过\\\"}\"}}\n"
        );
        when(mockHttpClient.post(Mockito.eq("http://11.145.81.86:8080/gpt-api/json_repair"), any(Map.class), any(Map.class)))
                .thenReturn(String.valueOf(mockJsonRepairResponse));

        // 调用要测试的方法
        ProcessFunction.Context mockContext = mock(ProcessFunction.Context.class);
        syncProcessorLLM.processElement(strategy, mockContext, mockCollector);

        // 验证结果
        assertNotNull(strategy.analyzes[0].res);
        System.out.println(new ObjectMapper().writeValueAsString(strategy));
        Mockito.verify(mockCollector).collect(strategy);
    }

    /**
     * 测试处理无效对话的情况
     * 验证当对话内容无效时，不应处理任何分析任务
     */
    @Test
    public void testProcessInvalidConversation() {
        // 准备测试数据 - 无效对话
        Strategy strategy = buildTestStrategy();
        strategy.chunk.conversation = "null"; // 无效对话内容

        // 调用要测试的方法
        ProcessFunction.Context mockContext = mock(ProcessFunction.Context.class);
        syncProcessorLLM.processElement(strategy, mockContext, mockCollector);

        // 验证结果 - 不应被处理
        Mockito.verify(mockCollector, Mockito.never()).collect(any());
    }


    /**
     * 测试LLM请求失败时的重试机制
     * 验证系统是否能够在失败后进行重试并最终成功
     */
    @Test
    public void testRetryMechanism() throws Exception {
        // 准备测试数据
        Strategy strategy = buildTestStrategy();
        strategy.analyzes = new Analyze[]{AnalyzeConfig.Professional_Skills};

        // 模拟首次请求失败，第二次请求成功的场景
        CompletableFuture<String> failedResponse = new CompletableFuture<>();
        failedResponse.completeExceptionally(new RuntimeException("LLM服务暂时不可用"));

        CompletableFuture<String> successResponse = CompletableFuture.completedFuture(
                "{\"requestId\":\"" + UUID.randomUUID() + "\",\"data\":{\"answer\":{\"坐席\":\"测试坐席\",\"分析结果\":\"通过\"}}}"
        );

        // 设置模拟HTTP客户端的行为，第一次调用失败，之后调用成功
        // 使用实际的URL字符串而不是any(String.class)，因为URL会被用来创建URI对象
        String testUrl = "http://test.example.com/api";
        when(mockHttpClient.post(Mockito.eq(testUrl), any(Map.class), any(Map.class)))
                .thenReturn(String.valueOf(failedResponse))
                .thenReturn(String.valueOf(successResponse));

        // 使用反射修改modelUrl为测试URL
        Field modelUrlField = StrategyAnalyzeSyncLLMProcess.class.getDeclaredField("modelUrl");
        modelUrlField.setAccessible(true);
        modelUrlField.set(syncProcessorLLM, testUrl);

        // 调用要测试的方法
        ProcessFunction.Context mockContext = mock(ProcessFunction.Context.class);
        syncProcessorLLM.processElement(strategy, mockContext, mockCollector);

        // 验证结果
        // 验证HTTP客户端被调用了至少2次（首次失败+重试）
        Mockito.verify(mockHttpClient, times(2)).post(Mockito.eq(testUrl), any(Map.class), any(Map.class));
        // 验证最终结果被成功处理
        assertNotNull(strategy.analyzes[0].res);
        Mockito.verify(mockCollector).collect(strategy);
    }

    /**
     * 测试处理多个分析任务的情况
     * 验证系统能够正确处理多个分析任务
     */
    @Test
    public void testProcessMultipleAnalyzes() throws Exception {
        // 准备测试数据
        Strategy strategy = buildTestStrategy();

        // 创建两个不同的分析任务
        Analyze analyze1 = AnalyzeConfig.Professional_Skills;
        Analyze analyze2 = AnalyzeConfig.Service_Awareness;

        strategy.analyzes = new Analyze[]{analyze1, analyze2};

        // 模拟两个不同的HTTP响应
        CompletableFuture<String> response1 = CompletableFuture.completedFuture(
                "{\"requestId\":\"" + UUID.randomUUID() + "\",\"data\":{\"answer\":{\"服务意识\":\"良好\",\"分析结果\":\"通过\"}}}"
        );

        CompletableFuture<String> response2 = CompletableFuture.completedFuture(
                "{\"requestId\":\"" + UUID.randomUUID() + "\",\"data\":{\"answer\":{\"沟通技巧\":\"出色\",\"分析结果\":\"通过\"}}}"
        );

        // 设置模拟HTTP客户端的行为
        String testUrl = "http://test.example.com/api";
        when(mockHttpClient.post(Mockito.eq(testUrl), any(Map.class), any(Map.class)))
                .thenReturn(String.valueOf(response1))
                .thenReturn(String.valueOf(response2));

        // 使用反射修改modelUrl为测试URL
        Field modelUrlField = StrategyAnalyzeSyncLLMProcess.class.getDeclaredField("modelUrl");
        modelUrlField.setAccessible(true);
        modelUrlField.set(syncProcessorLLM, testUrl);

        // 调用要测试的方法
        ProcessFunction.Context mockContext = mock(ProcessFunction.Context.class);
        syncProcessorLLM.processElement(strategy, mockContext, mockCollector);

        // 验证结果
        assertNotNull(strategy.analyzes[0].res);
        assertNotNull(strategy.analyzes[1].res);
        Mockito.verify(mockCollector).collect(strategy);
    }

    /**
     * 通过反射注入模拟的HTTP客户端
     * 由于LLMClient是StrategyAnalyzeSyncLLMProcess的私有内部类，
     * 需要使用反射机制访问并注入模拟对象
     */
    private void injectMockHttpClient(StrategyAnalyzeSyncLLMProcess processor, HttpClientUtils mockClient) throws Exception {
        // 获取LLMClient类
        Class<?> llmClientClass = Class.forName("com.tencent.andata.smart.etl.process.StrategyAnalyzeSyncLLMProcess$LLMClient");

        // 获取llmClient字段
        Field llmClientField = StrategyAnalyzeSyncLLMProcess.class.getDeclaredField("llmClient");
        llmClientField.setAccessible(true);

        // 获取LLMClient实例
        Object llmClient = llmClientField.get(processor);

        // 获取httpClient字段
        Field httpClientField = llmClientClass.getDeclaredField("httpClient");
        httpClientField.setAccessible(true);

        // 设置模拟的HTTP客户端
        httpClientField.set(llmClient, mockClient);
    }
}