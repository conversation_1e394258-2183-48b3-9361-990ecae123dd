package com.tencent.andata.utils.gpt;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.tencent.andata.utils.AsyncHttpClientUtils;
import com.tencent.andata.utils.HttpClientUtils;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import org.junit.Before;
import org.junit.Test;

public class CommonGptApiV2Test {
    private CommonGptApiV2 gptApi;
    private CommonGptApiV2 snycGptApi;
    private transient AsyncHttpClientUtils asyncHttpClientUtils;
    private transient HttpClientUtils httpClientUtils;


    private final String promptTmp = "\"请分析客户与客服的对话，基于客户发言内容进行风险等级的判断，风险等级分别为D/C/B/A/S，等级说明如下，\\n\" +\n"
            + "                    \"C：包括两种情况，满足其中一项或以上则命中C风险：\\n\" +\n"
            + "                    \"    C-1 客户表示“尽快解决”、“马上解决”、“请及时处理”、“能加快吗”、“速度”、“快点”等，且没有表示问题长时间仍未解决如“从XX时候开始就在处理问题了，怎么现在还没没弄好”。\\n\" +\n"
            + "                    \"    C-2 客户存在负面情绪，但没有出现辱骂和自残、自杀威胁的情况。\\n\" +\n"
            + "                    \"B：包括两种情况，满足其中一项或以上则命中B风险：\\n\" +\n"
            + "                    \"    B-1 客户明确要求赔偿或退费。\\n\" +\n"
            + "                    \"    B-2 客户明确提到内部投诉，包括直接联系腾讯云高层领导投诉。\\n\" +\n"
            + "                    \"A：包括七种情况，满足其中一项或以上则命中A风险：\\n\" +\n"
            + "                    \"    A-1 客户明确说单台服务器或机器出现故障。\\n\" +\n"
            + "                    \"    A-2 客户明确说数据库出现异常或数据丢失。\\n\" +\n"
            + "                    \"    A-3 客户明确提到不再使用腾讯云产品，或改用其他云厂商（如华为云、阿里云、亚马逊AWS、微软Azure等）产品，或其他云厂商产品比腾讯云产品更有优势。\\n\" +\n"
            + "                    \"    A-4 客户表示问题长时间仍未解决，如“从XX时候开始就在处理问题了，怎么现在还没没弄好”。\\n\" +\n"
            + "                    \"    A-5 客户有辱骂行为。\\n\" +\n"
            + "                    \"    A-6 客户发言有舆情监管的风险如：消费者协会，工信部，12345，12315，国家市场监督总局, 社交媒体曝光等。\\n\" +\n"
            + "                    \"    A-7 客户有诉讼倾向，或以诉讼威胁客服，但实际未提起诉讼。\\n\" +\n"
            + "                    \"S：包括三种情况，满足其中一项或以上则命中S风险：\\n\" +\n"
            + "                    \"    S-1 客户明确说批量机器出现故障。\\n\" +\n"
            + "                    \"    S-2 客户以自残、自杀威胁客服。\\n\" +\n"
            + "                    \"    S-3 客户明确说已经提起诉讼。\\n\" +\n"
            + "                    \"D：无风险或S、A、B、C下的风险情形都不满足；\\n\" +\n"
            + "                    \"注意：请严格根据以上标准判断风险，需要详细判别每一个风险情形是否存在，详细输出所有命中风险情况的编号，不要漏识别。风险等级S>A>B>C>D，如果判断后出现多个风险等级，请选择最高等级。\\n\" +\n"
            + "                    \"对话：\\n\" +\n"
            + "                    \"%s\\n\" +\n"
            + "                    \"以json格式输出“reason”与“risk_level”例如：\\n\" +\n"
            + "                    \"{\\\"reason\\\":\\\"xxx\\\",\\\"risk_level\\\":\\\"x\\\"}\\n\" +\n"
            + "                    \"只输出一个以上格式的json，不要输出其他内容。请详细、一步步思考，将思考过程放到“reason”里，注意不要过度解读。\"";

    String conversation = "客户回复：腾讯云认证退款原因：培训课程延期，考试先申请退费。退款订单号：20241108198072666282221\n"
            + "客服回复：您好，您反馈的问题我们已经收到，我们正在为您进一步核实中，请稍候，预计15-90分钟给您反馈进展，谢谢您的耐心等待\n"
            + "客服回复：您好，资源id：20241108198072666282241，截止当前可退现金：388.7元，资源id：20241108198072666282231，截止当前可退现金：119.6元，实际金额以退还时为准，退款会给您原路返回您的支付腾讯云账户，购买资源时所使用的代金券不支持退还，请知悉。退还前腾讯云需要您核实并确认数据已备份，业务已迁移到其他实例。您回复 【核实并确认】后，我们会为您执行退还操作。【注意】1、退还后实例将会在控制台下线，资源到期后会销毁且不可找回。请慎重确认并回复。2、实例在没有正式退还前，是正常计费的，为了避免多扣费用，请您尽快确认并回复，请知悉。\n"
            + "客户回复：确��无误。\n"
            + "客服回复：您好，很抱歉，在您采购腾讯云产品时使用微信、QQ或网银方式进行在线订单付款，对应的金额是先充值至腾讯云账户余额中，再进行的订单付款。进行资源退回时退款金额也是先退回至腾讯云账户余额中，若您需退回至支付账户中，是需要您发起提现的，还请您了解。充值不满360天的金额在申请提现时是原路退回至支付账户中，还请您了解。提现请您参考：https://cloud.tencent.com/document/product/555/7435\n"
            + "客服回复：尊敬的客户：您好，由于您长时间未反馈信息，我们暂时将您的问题修改为待您确认状态，如此问题重现您可以继续留言。如您不再回复，7天后工单会自动关闭。\n"
            + "客户回复：要求款项原路退回至支付的个人银行账户，不能退回到公司账户。\n"
            + "客服回复：您好，退款是退还到账户余额，退款后，您可以进行原路提现的。\n"
            + "客服回复：您好，十分不好意思，耽误您的宝贵时间了，经核实您的资源id：20241108198072666282241，截止当前可退现金：388.7元，资源id：20241108198072666282231，截止当前可退现金：119.6元，实际金额以退还时为准，退��会给您原路返回您的支付腾讯云账户，购买资源时所使用的代金券不支持退还，请知悉。退还前腾讯云需要您核实并确认数据已备份，业务已迁移到其他实例。您回复 【核实并确认】后，我们会为您执行退还操作。【注意】1、退还后实例将会在控制台下线，资源到期后会销毁且不可找回。请慎重确认并回复。2、实例在没有正式退还前，是正常计费的，为了避免多扣费用，请您尽快确认并回复，请知悉。\n"
            + "客服回复：您好，确实很抱歉，退款是退还到账户余额，退款后，您可以进行原路提现的：https://cloud.tencent.com/document/product/555/7435\n"
            + "客户回复：不同意退到公司账户。要求款项原路退回至支付的个人银行账户，不能退回到公司账户。\n"
            + "客服回复：您好，抱歉给您带来不便，支付购买是在平台上购买使用的，即腾讯云产品，退款金额是返回到腾讯云账号余额中，退款成功后您可在费用中心操作提现：https://console.cloud.tencent.com/expense/overview腾讯云账户提现文档介绍：https://cloud.tencent.com/document/product/555/7435如果问题已经解决，您可以点击【结单】关闭此工单，并对此次服务做出评价。如果���题尚未解决，您可以随时在这个工单补充咨询， 我会竭尽全力帮助您的。感谢您对腾讯云的支持与信任，祝您生活愉快，事事顺心~\n"
            + "客户回复：不同意退到公司账户。要求款项原路退回至支付的个人银行账户，不能退回到公司账户。\n"
            + "客户回复：不同意退到公司账户。要求款项原路退回至支付的个人银行账户，不能退回到公司账户。\n"
            + "客户回复：不同意退到公司账户。要求款项原路退回至支付的个人银行账户，不能退回到公司账户。\n"
            + "客服回复：您好，非常抱歉，这边已经反馈专员核实，还没有结果回馈，核实处理预计明日，这边将在有初步结果时，在此工单中回复您，请您耐心等待。\n"
            + "客服回复：您好，非常抱歉让您久等了，支付购买是在平台上购买使用的，即腾讯云产品，退款金额是返回到腾讯云账号余额中，退款成功后您可在费用中心操作提现，原路提现发起后，资金会原路退回到对应充值订单的付款账号。https://console.cloud.tencent.com/expense/overview腾讯云账户提现文档介绍：https://cloud.tencent.com/document/product/555/7435\n"
            + "客户回复：不同意退到公司账户。要求款项原路退回至支付的个人银行账户，不能退回到公司账户。\n"
            + "客户回复：不同意退到公司账户。强烈要求款项原路退回至支付的个人银行账户，绝对不同意退回到公司账户，如果还直接退回到公司账户，由此造成的任何后果，将全部由腾讯公司承担，我保留向相关部门投诉的权利。";

    @Before
    public void setUp() {
        asyncHttpClientUtils = new AsyncHttpClientUtils(6000000, 6000000);
        httpClientUtils = new HttpClientUtils(6000000, 6000000);

        gptApi = new CommonGptApiV2("2b663fe95b604593b74697950cac6c2e", "http://30.46.122.172:8080/gpt-api/api",
                "hunyuan-turbo_riskcontrol");

        snycGptApi = new CommonGptApiV2("2b663fe95b604593b74697950cac6c2e", "http://30.46.122.172:8080/gpt-api/api",
                "hunyuan-turbo_riskcontrol");

        gptApi.setMessages(new CopyOnWriteArrayList<>(Collections.singletonList(new ConcurrentHashMap<String, Object>() {{
            put("role", "system");
            put("content", "\n***请务必注意***\n你的输出请使用 JSON 格式，不要有多余的文字\n***请务必注意***");
        }})));

        snycGptApi.setMessages(new CopyOnWriteArrayList<>(Collections.singletonList(new ConcurrentHashMap<String, Object>() {{
            put("role", "system");
            put("content", "\n***请务必注意***\n你的输出请使用 JSON 格式，不要有多余的文字\n***请务必注意***");
        }})));

        gptApi.setExtraParams(new ConcurrentHashMap<String, String>() {{
            put("version", "latest");
        }});
        snycGptApi.setExtraParams(new ConcurrentHashMap<String, String>() {{
            put("version", "latest");
        }});

        gptApi.messages.add(new ConcurrentHashMap<String, Object>() {{
            put("role", "user");
            put("content", String.format(promptTmp, conversation));
        }});

        snycGptApi.messages.add(new ConcurrentHashMap<String, Object>() {{
            put("role", "user");
            put("content", String.format(promptTmp, conversation));
        }});
    }

    @Test
    public void testParseResponseSuccess2() throws Exception {
        gptApi.buildRequest(String.format(promptTmp, conversation));
        snycGptApi.buildRequest(String.format(promptTmp, conversation));

        String ansower = gptApi.requestAndParse(asyncHttpClientUtils).get();
        String snycAns = String.valueOf(snycGptApi.requestAndParseSync(httpClientUtils));

        System.out.println(ansower);
        System.out.println(snycAns);
    }
}