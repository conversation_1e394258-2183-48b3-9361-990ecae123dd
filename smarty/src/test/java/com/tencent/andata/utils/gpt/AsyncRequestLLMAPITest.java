package com.tencent.andata.utils.gpt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.etl.process.StrategyAnalyzeLLMProcess;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

public class AsyncRequestLLMAPITest {

    private static ObjectMapper objectMapper = new ObjectMapper();
    private StrategyAnalyzeLLMProcess asyncProcessorLLM;
    private ResultFuture<Strategy> resultFuture;

    @Before
    public void setUp() throws Exception {
        asyncProcessorLLM = StrategyAnalyzeLLMProcess
                .builder()
                .modelUrl("http://30.46.122.172:8080/gpt-api/api")
                .modelToken("2b663fe95b604593b74697950cac6c2e")
                .build();

        asyncProcessorLLM.open(new Configuration());

        resultFuture = mock(ResultFuture.class);
    }

    @Test
    public void testAsyncInvoke() throws Exception {
        // 创建输入数据
        Strategy strategy = buildTestStrategy();

        strategy.analyzes = Arrays.asList(
                AnalyzeConfig.Professional_Skills,
                AnalyzeConfig.Service_Awareness,
                AnalyzeConfig.Communication_Skills,
                //AnalyzeConfig.WebIM_Service_Timeliness,
                AnalyzeConfig.Service_Specifications).toArray(new Analyze[4]);

        // 调用 asyncInvoke 方法
        asyncProcessorLLM.asyncInvoke(strategy, resultFuture);

        // 捕获 ResultFuture 的参数
        ArgumentCaptor<Collection<Strategy>> captor = ArgumentCaptor.forClass(Collection.class);
        verify(resultFuture, timeout(6000000)).complete(captor.capture());

        // 验证结果
        Collection<Strategy> result = captor.getValue();
        assertEquals(1, result.size()); // 确保有一个结果
        Strategy output = result.iterator().next();
        System.out.println(output);
    }


    private static Strategy buildTestStrategy() throws Exception {
        Strategy strategy = Strategy.builder()
                .id(7)
                .sceneIdentify("13912606")
                .name("")
                .scene(Scene.Ticket)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'retry_strategy' && reflection_type_classify == 'L3'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(Chunk.builder()
                        .type(ChunkType.Conversation)
                        .maxSize(32 * 1024)
                        .delimiter("\n")
                        .conversationSpliceType(SpliceType.TicketQualityInspection)
                        .build())
                .analyzes(
                        new ArrayList<Analyze>() {{
                            add(AnalyzeConfig.TDSQL_Agent_Ticket_Priority_L2);
                        }}.toArray(new Analyze[0])
                ).build();

        strategy.trigger.setTriggerTimestamp(1732683735406L);

        strategy.trigger.data = new JsonNode[]{objectMapper.readTree(getInputData())};
        strategy.chunk.conversation = "04-07 16:22 客户回复：你好\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）回复：您好\n" +
                "04-07 16:23 客户回复：我们有一个很重要的会议\n" +
                "04-07 16:23 客户回复：我现在进去了，结束不了，改不了时间，您有没有办法\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）回复：非常抱歉！根据您描述这边需要为您转接会话。可能需要排队请您耐心等待哦！\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）触发动作：转单，将会话转移给 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，很高兴为您服务，辛苦稍等，我看下您的问题\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：结束不了是指什么呢，您截图下我看下\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：结束不了提示您什么\n" +
                "04-07 16:35 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，还在线吗\n" +
                "04-07 16:44 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，由于长时间没有收到您的回复，我们将关闭本次会话，感谢您的咨询。为了给您提供更好的服务，我们诚挚地邀请您点击“评价反馈”或在稍后的弹窗中对本次服务做出评价，我们非常期待和重视您的满意度，9-10分代表着您的满意。祝您生活愉快、一切顺利！\n"
                +
                "04-07 16:44 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）触发动作：待客户确认结单\n" +
                "04-08 16:45 超过3天系统自动结单";

        return strategy;
    }

    private static String getInputData() {
        return String.format("{\n"
                + "    \"operation_id\": 78701486,\n"
                + "    \"ticket_id\": 13520914,\n"
                + "    \"group_id\": \"wrRiX7DwAASNzFodguTjDuMyZv277KdQ\",\n"
                + "    \"start_time\": 1733577235000,\n"
                + "    \"end_time\": 1733579533000,\n"
                + "    \"create_time\": \"2024-12-07 21:13:55\",\n"
                + "    \"operate_time\": \"2024-12-07 21:51:48\",\n"
                + "    \"operator\": \"p_pymyan\",\n"
                + "    \"operator_type\": 2,\n"
                + "    \"inner_reply\": \"待客户实施或验证：已有解决方案，待验证[br][br]方案截图：[br][img src=https://ticket-10039692.cossh.myqcloud.com/1732698568142_9912.png][br]数据盘资源 不足\",\n"
                + "    \"extern_reply\": \"\",\n"
                + "    \"qcloud_comment_id\": 0,\n"
                + "    \"target_status\": 29,\n"
                + "    \"next_operator\": \"p_pymyan\",\n"
                + "    \"next_assign\": 0,\n"
                + "    \"duration\": 1522,\n"
                + "    \"remark\": \"\",\n"
                + "    \"secret_content\": \"\",\n"
                + "    \"is_undo\": 0,\n"
                + "    \"company_id\": 0,\n"
                + "    \"target_post\": 12,\n"
                + "    \"cc_person\": \"\",\n"
                + "    \"operation_type\": 62,\n"
                + "    \"status\": 22,\n"
                + "    \"current_operator\": \"p_pymyan\",\n"
                + "    \"fact_assign\": 3218,\n"
                + "    \"post\": 12,\n"
                + "    \"responsible\": \"v_nanxpan\",\n"
                + "    \"next_responsible\": \"v_nanxpan\",\n"
                + "    \"customer_fields\": \"{\\\"priority\\\":2,\\\"validation_scene\\\":\\\"to_be_validation\\\"}\",\n"
                + "    \"request_source\": \"\",\n"
                + "    \"target_extern_status\": 1,\n"
                + "    \"data_type\": \"%s\",\n"
                + "    \"service_channel\": 27,\n"
                + "    \"service_scene\": 257696,\n"
                + "    \"ticket_title\": \"【中信建投-TDSQL -李敬财】tdsql 创建实例失败\",\n"
                + "    \"priority\": 2,\n"
                + "    \"service_scene_level1_name\": \"云产品一部/国产数据库产品中心\",\n"
                + "    \"service_scene_level2_name\": \"腾讯云TDSQL\",\n"
                + "    \"service_scene_level3_name\": \"咨询\",\n"
                + "    \"service_scene_level4_name\": \"TDSQL原理相关\",\n"
                + "    \"risk_type\": \"\",\n"
                +
                "    \"context\": \"事件标题:【中信建投-TDSQL -李敬财】tdsql 创建实例失败描述及影响:【中信建投-TDSQL -李敬财】tdsql 创建实例失败\\n1、【问题描述】tdsql 创建实例失败\\n2、【补充信息】暂无\\n3、【触发条件】暂无\\n4、【版本信息】TDSQL-MySQL-10.3.16"
                +
                ".2_ABC_ARM\\n5、【业务影响】暂无\\n6、【环境信息】暂无\\n7、【是否重启过什么服务】暂无\\n8、【问题进展】暂无\\n\",\n"
                + "    \"problem_summary\": \"tdsql 创建实例失败\",\n"
                + "    \"reflection_type_classify\": \"L3\",\n"
                + "    \"is_consult\": \"否\",\n"
                + "    \"is_obvious\": \"否\",\n"
                + "    \"reason\": \"虽然问题描述中没有明确提到生产故障、业务受损或客户情绪化，但由于tdsql创建实例失败可能会影响到业务运行，因此将其归类为L3。\",\n"
                + "    \"try_to_recovered\": \"否\",\n"
                + "    \"preLlmRes\": {\n"
                + "        \"problem_summary\": \"tdsql 创建实例失败\",\n"
                + "        \"reflection_type_classify\": \"L3\",\n"
                + "        \"is_consult\": \"否\",\n"
                + "        \"is_obvious\": \"否\",\n"
                + "        \"reason\": \"虽然问题描述中没有明确提到生产故障、业务受损或客户情绪化，但由于tdsql创建实例失败可能会影响到业务运行，因此将其归类为L3。\",\n"
                + "        \"try_to_recovered\": \"否\"\n"
                + "    }\n"
                + "}", "ticket_operation");
    }
}