package com.tencent.andata.strategy;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.config.ChunkConfig;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import com.tencent.andata.smart.etl.process.StrategyConditionProcess;
import java.util.Arrays;
import java.util.Collections;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.PrintSinkFunction;

/**
 * 用于测试StrategyConditionProcess的功能。
 * 输入数据为工单操作JSON，策略条件为data_type == 'ticket_operation' && operation_type == 16 && service_channel == 27。
 * 期望输出命中的Strategy对象。
 */
public class StrategyConditionProcessTest {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        // 1. 构造Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

        // 2. 构造测试输入数据
        String inputJson = "{\n" +
                "    \"operation_id\": 87142734,\n" +
                "    \"ticket_id\": 14416954,\n" +
                "    \"operate_time\": \"2025-05-06T10:16:28\",\n" +
                "    \"operator\": \"100026386359\",\n" +
                "    \"operator_type\": 1,\n" +
                "    \"inner_reply\": \"客户结单[br]评价星级：\\\"五星 \\\"[br]问题是否解决：\\\"已解决\\\"[br]\",\n" +
                "    \"extern_reply\": null,\n" +
                "    \"qcloud_comment_id\": 0,\n" +
                "    \"target_status\": 3,\n" +
                "    \"next_operator\": \"\",\n" +
                "    \"next_assign\": 0,\n" +
                "    \"duration\": 54080,\n" +
                "    \"remark\": null,\n" +
                "    \"secret_content\": \"\",\n" +
                "    \"is_undo\": 0,\n" +
                "    \"company_id\": 0,\n" +
                "    \"target_post\": 2,\n" +
                "    \"cc_person\": \"\",\n" +
                "    \"operation_type\": 16,\n" +
                "    \"status\": 3,\n" +
                "    \"current_operator\": \"\",\n" +
                "    \"fact_assign\": 101,\n" +
                "    \"post\": 2,\n" +
                "    \"responsible\": \"v_pgzyzhang\",\n" +
                "    \"next_responsible\": \"v_pgzyzhang\",\n" +
                "    \"customer_fields\": \"{\\\"operator_post\\\":2}\",\n" +
                "    \"request_source\": \"\",\n" +
                "    \"target_extern_status\": 2,\n" +
                "    \"group_id\": \"wrRiX7DwAAnPCk1_q-8c-nMyhR8Clc2Q\",\n" +
                "    \"start_time\": 1746433457000,\n" +
                "    \"end_time\": 1746497788000,\n" +
                "    \"service_channel\": 27,\n" +
                "    \"data_type\": \"ticket_operation\"\n" +
                "}";

        // 3. 构造策略，条件能命中上述数据
        Strategy strategy = Strategy.builder()
                .id(7)
                .sceneIdentify("14416954")
                .name("工单结单质检测试")
                .scene(Scene.Ticket)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'ticket_operation' && operation_type == 16 && regexMatch(service_channel, '^(3|27)$')")
                                .build()
                )
                .chunk(ChunkConfig.TicketExternalReplyChunk)
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .analyzes(Arrays.asList(
                                AnalyzeConfig.Professional_Skills,
                                AnalyzeConfig.Service_Awareness,
                                AnalyzeConfig.Communication_Skills,
                                AnalyzeConfig.Ticket_Service_Timeliness,
                                AnalyzeConfig.Service_Specifications)
                        .toArray(new Analyze[0])
                )
                .build();

        // 4. 构造输入流
        DataStream<String> input = env.fromElements(inputJson);

        // 5. 应用StrategyConditionProcess
        DataStream<Strategy> result = input.process(new StrategyConditionProcess(Collections.singletonList(strategy)));

        // 6. 打印输出
        result.addSink(new PrintSinkFunction<Strategy>() {
            @Override
            public void invoke(Strategy value, Context context) throws JsonProcessingException {
                System.out.println("=== 命中策略输出 ===");
                System.out.println(new ObjectMapper().writeValueAsString(value));
                System.out.println("==================");
            }
        });

        // 7. 执行任务
        env.execute("StrategyConditionProcess 单元测试");
    }
}