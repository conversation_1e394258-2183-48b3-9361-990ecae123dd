package com.tencent.andata.strategy;

import static com.tencent.andata.smart.utils.AviatorMatchUtils.convertJsonToMap;

import com.googlecode.aviator.AviatorEvaluator;
import com.tencent.andata.smart.utils.aviatorFuns.AddAttributeFunction;
import com.tencent.andata.smart.utils.aviatorFuns.GetCurrentTimeStampFunction;
import com.tencent.andata.smart.utils.aviatorFuns.GetNestedValueFunction;
import com.tencent.andata.smart.utils.aviatorFuns.PutNestedFunction;
import com.tencent.andata.smart.utils.aviatorFuns.RegexMatchFunction;
import com.tencent.andata.smart.utils.aviatorFuns.ReturnOriginValueFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;


public class AviatorExpressionDemo {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) {
        String a = "{\n"
                + "    \"analyzes\": [\n"
                + "        {\n"
                + "            \"res\": null,\n"
                + "            \"modelExtraParams\": null,\n"
                + "            \"name\": \"Professional_Skills\",\n"
                + "            \"messages\": null,\n"
                + "            \"model\": \"quality_check_sft\",\n"
                + "            \"type\": \"Quality_Inspection\",\n"
                + "            \"status\": 1,\n"
                +
                "            \"prompt\": \"你是一位云计算售后服务质量检查员。请分析每个客户与坐席的对话，按以下步骤执行：\\n1、总结问题与解决方案：\\n+ 详细描述对话中客户提出的每个问题。\\n+ 详细描述坐席对此提出的具体解决方案。\\n注意：客户的问题和坐席提供的解决方案要一一对应输出。\\n\\n2、专业能力评估：\\n+ 坐席是否能够正确识别和理解客户提出的问题？\\n+ " +
                "当客户对问题的描述不够清晰时，坐席是否主动向客户进一步澄清和确认？\\n+ 如果坐席无法解决客户的问题，坐席是否将客户的问题转交或升级到其他坐席进行处理？\\n+ 坐席是否遗漏了客户的问题？\\n注意：从以上4个方面分别评估不同坐席的专业能力。\\n\\n3、综合以上信息，输出不同坐席最终的质检和结果（通过或不通过），如果质检结果为“不通过”，请给出具体原因。\\n\\n注意事项：\\n+ " +
                "如果由于客户不回复坐席的问题，导致坐席无法解答客户的问题，则该坐席质的检结果为：通过。\\n+ 按不同坐席（包括一线和1.5线）分别输出以上3部分内容。\\n+ 各坐席的姓名可能不同，请针对个体进行质检。\\n+ 保持回答简洁明了，只输出必要的质检内容.\\n\\n对话：\\n%s\",\n"
                + "            \"desc\": \"专业技能\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"res\": null,\n"
                + "            \"modelExtraParams\": null,\n"
                + "            \"name\": \"Service_Awareness\",\n"
                + "            \"messages\": null,\n"
                + "            \"model\": \"quality_check_sft\",\n"
                + "            \"type\": \"Quality_Inspection\",\n"
                + "            \"status\": 2,\n"
                +
                "            \"prompt\": \"你是一位云计算售后服务质量检查员，分析客户与坐席的对话并从以下几个方面判断坐席质检是否通过，\\n+ 坐席是否始终使用专业和礼貌的语言与客户沟通，是否保持耐心的服务态度；\\n+ 坐席是否表现出积极和认真的态度，是否主动帮助客户解决问题；\\n+ 如果客户吐槽、有负面情绪、提出投诉、有流失倾向时，坐席是否有及时安抚或致歉；\\n+ 如果客户要求电话沟通，坐席是否拒绝；\\n+ " +
                "坐席是否表达不通顺或有错别字；\\n\\n注意：按不同坐席分别输出分析过程与质检结果（通过或者不通过），不要输出其它多余内容，回答要简洁，坐席包括一线与1.5线，并且姓名可能不一样，需要针对不同的坐席进行质检，如果有质检不通过的需要输出坐席违反本要求的具体时间和原因。\\n对话\\n%s\",\n"
                + "            \"desc\": \"服务意识\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"res\": null,\n"
                + "            \"modelExtraParams\": null,\n"
                + "            \"name\": \"Communication_Skills\",\n"
                + "            \"messages\": null,\n"
                + "            \"model\": \"quality_check_sft\",\n"
                + "            \"type\": \"Quality_Inspection\",\n"
                + "            \"status\": 3,\n"
                +
                "            \"prompt\": \"你是一位云计算售后服务质量检查员，分析���户与坐席的对话并从以下两个方面判断坐席质检是否通过，\\n+ 坐席是否使用清晰、准确的语言回答客户问题，确保信息传达明确且容易理解。\\n+ " +
                "坐席在解答客户的问题时，坐席是否连续3次使用完全相同的话术解答客户的问题，如果话术是以下三种情况则除外：1、坐席询问客户问题是否已经解决；2、坐席邀请客户对本次服务进行评价；3、坐席回复客户问题正在核实/处理需要客户等待。\\n\\n注意：输出以上2个质检项的结果，按不同坐席分别输出分析过程与质检结果（通过或者不通过），不要输出其它多余内容，回答要简洁，坐席可能包括一线与1.5线，并且姓名可能不一样。\\n对话：\\n%s\",\n"
                + "            \"desc\": \"沟通技巧\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"res\": null,\n"
                + "            \"modelExtraParams\": null,\n"
                + "            \"name\": \"Service_Timeliness\",\n"
                + "            \"messages\": null,\n"
                + "            \"model\": \"quality_check_sft\",\n"
                + "            \"type\": \"Quality_Inspection\",\n"
                + "            \"status\": 4,\n"
                +
                "            \"prompt\": \"你是一位云计算售后服务质量检查员，分析客户与坐席的对话并从以下7个方面判断坐席质检是否通过，\\n+ 坐席是否在10分钟内响应客户的第1条回复；\\n+ 如果客户的回复中包含了问题或者要求，依次输出客户的回复时间和坐席的响应时间，逐一判断坐席是否在10分钟内响应客户；\\n+ 如果坐席提供了预期答复时间，坐席是否在预期时间前向客户同步问题的处理进展或者结论；\\n+ " +
                "如果坐席要时间处理客户的问题，并且没有向客户提供预期答复时间，坐席是否在30分钟内向客户同步问题的处理进展或者结论；\\n+ 客户触发动作：催单后，坐席是否在10分钟内响应客户；\\n+ 客户触发动作：投诉后，坐席是否在10分钟内响应客户；\\n+ 如果客户的问题处理需要跨自然日，坐席是否每天至少一次向客户同步问题的处理进展；\\n\\n注意：只需要输出分析过程与质检结果，不要输出其它多余内容，回答要简洁，坐席包括一线与1" +
                ".5线，并且姓名可能不一样，需要针对不同的坐席进行质检。最后输出一个结论“质检通过”或“质检未通过”。\\n对话：\\n%s\",\n"
                + "            \"desc\": \"服务时效\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"res\": null,\n"
                + "            \"modelExtraParams\": null,\n"
                + "            \"name\": \"Service_Specifications\",\n"
                + "            \"messages\": null,\n"
                + "            \"model\": \"quality_check_sft\",\n"
                + "            \"type\": \"Quality_Inspection\",\n"
                + "            \"status\": 5,\n"
                +
                "            \"prompt\": \"你是一位云计算售后服务质量检查员，分析客户与坐席的对话并从以下3个方��判断坐席质检是否通过。\\n+ 如果坐席有触发动作“待客户确认结单”，请判断坐席是否在触发前询问客户问题是否解决或是否其他的问题，例如回复中包含“若您的问题已经解决”、“如果您当前的问题没有解决或者仍有其他问题”；\\n+ " +
                "如果坐席有触发动作“待客户确认结单”，请判断坐席是否在触发前邀请客户对本次服务进行评价；\\n+ 如果坐席有触发动作“转单”，是否向客户进行告知或说明，例如回复了：“需要将您的问题转接到负责您的问题的工程师进行处理，您的问题需要其他工程师进一步处理，您反馈的问题已为您反馈专员核实确认中”则质检通过。如果坐席没有触发动作“转单”，则不进行此质检项的检查。\\n\\n" +
                "注意：只需要输出分析过程与质检结果，不要输出其它多余内容，回答要简洁，坐席包括一线与1.5线，并且姓名可能不一样，需要针对不同的坐席进行质检。\\n对话：\\n%s\",\n"
                + "            \"desc\": \"服务规范\"\n"
                + "        }\n"
                + "    ],\n"
                + "    \"chunk\": {\n"
                + "        \"operations\": null,\n"
                + "        \"delimiter\": \"\\n\",\n"
                + "        \"maxSize\": 28672,\n"
                + "        \"type\": \"xxxx\",\n"
                + "        \"value\": null,\n"
                + "        \"conversation\": null,\n"
                + "            \"status\": 6,\n"
                + "        \"conversationSpliceType\": \"TicketQualityInspection\"\n"
                + "    },\n"
                + "    \"sceneIdentify\": \"13520914\",\n"
                + "    \"name\": \"TDSQL工单优先级L3二次校验\",\n"
                + "    \"id\": 7,\n"
                + "    \"condition\": {\n"
                + "        \"expression\": \"data_type == 'retry_strategy' && reflection_type_classify == 'L3'\"\n"
                + "    },\n"
                + "    \"scene\": \"Ticket\",\n"
                + "    \"trigger\": {\n"
                + "        \"needPersist\": true,\n"
                + "        \"data\": [\n"
                + "            {\n"
                + "                \"msg_id\": \"14687404323626165942_1735270295232_external\",\n"
                + "                \"channel\": \"wework-extend\",\n"
                + "                \"msg_type\": \"text\",\n"
                + "                \"content\": {\n"
                + "                    \"text\": {\n"
                + "                        \"content\": \"我确认下\"\n"
                + "                    }\n"
                + "                },\n"
                + "                \"msg_time\": 1735270291034,\n"
                + "                \"group_id\": \"wrRiX7DwAAM3FfpG7JKokvdHSuNPTnlQ\",\n"
                + "                \"sender_id\": \"woRiX7DwAAjF4iOQ_SmsrRbqNYBxD8Mg\",\n"
                + "                \"sender_name\": \"赵志\",\n"
                + "                \"table\": \"ods_private_cloud_wxgroup_msg_data\",\n"
                + "                \"sender_type\": \"客服\",\n"
                + "                \"uin\": 2126950878,\n"
                + "                \"company\": \"北京畅游时空软件技术有限公司\",\n"
                + "                \"nickname\": \"赵志\",\n"
                + "                \"corp_name\": \"腾讯\",\n"
                + "                \"corp_full_name\": \"腾讯计算机系统有限公司\",\n"
                + "                \"rtx\": \"p_pzhizhao\",\n"
                + "                \"alarm_group_id\": \"\",\n"
                + "                \"sales_supportor\": \"adamascai\",\n"
                + "                \"is_big_customer\": 1,\n"
                + "                \"status\": 6,\n"
                + "                \"data_type\": \"group\",\n"
                + "                \"display_content\": \"我确认下\",\n"
                + "                \"risk_type\": \"\"\n"
                + "            }\n"
                + "        ],\n"
                + "        \"type\": \"Immediately\",\n"
                + "        \"triggerTimestamp\": 1732683735406\n"
                + "    }\n"
                + "}";

        JsonNode env;
        try {
            env = objectMapper.readTree(a);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 注册新函数
        AviatorEvaluator.addFunction(new PutNestedFunction());
        AviatorEvaluator.addFunction(new RegexMatchFunction());
        AviatorEvaluator.addFunction(new AddAttributeFunction());
        AviatorEvaluator.addFunction(new GetNestedValueFunction());
        AviatorEvaluator.addFunction(new ReturnOriginValueFunction());
        AviatorEvaluator.addFunction(new GetCurrentTimeStampFunction());

        // 获取嵌套值的示例
        String nestedField = "getNested('trigger.data[0].group_id')";
        Object nestedValue = AviatorEvaluator.execute(nestedField, convertJsonToMap(env));
        System.out.println("获取嵌套对象属性： " + nestedValue);

        // 设置嵌套值的示例
        String setNestedField = "putNested('trigger.data[0].start_time', getNested('trigger.data[0].msg_time') - 86400000)";
        JsonNode res = (JsonNode) AviatorEvaluator.execute(setNestedField, convertJsonToMap(env));
        System.out.println("设置嵌套对象属性后： " + res);

        // 添加简单属性的示例
        String addField = "put('newField', 'newFieldValue')";
        res = (JsonNode) AviatorEvaluator.execute(addField, convertJsonToMap(env));
        System.out.println("添加新属性后： " + res);

        // 使用正则表达式匹配
        String expression = "regexMatch(getNested('trigger.data[0].risk_type'), '催单风险|舆情监管|已诉讼|一般催单|业务疑似影响|业务影响|负面情绪|紧急催单|复现|赔偿问题|情绪激动|威胁切量|自残行为')";
        System.out.println("正则匹配结果：" + AviatorEvaluator.execute(expression, convertJsonToMap(env)));

        // 使用aviator内置函数
        String expression2 = "include(seq.list(6, 12), getNested('trigger.data[0].status'))";
        System.out.println("正则匹配结果：" + AviatorEvaluator.execute(expression2, convertJsonToMap(env)));
    }
}