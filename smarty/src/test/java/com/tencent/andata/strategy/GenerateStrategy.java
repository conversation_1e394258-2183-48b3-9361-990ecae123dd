package com.tencent.andata.strategy;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import java.util.ArrayList;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class GenerateStrategy {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        Strategy strategy = buildTestStrategy("Composite", "ticket_operation");
        System.out.println(objectMapper.writeValueAsString(strategy));
    }


    private static String getInputData(String dataType) {
        return String.format("{\n"
                + "    \"operation_id\": 78701486,\n"
                + "    \"ticket_id\": 13520914,\n"
                + "    \"group_id\": \"wrRiX7DwAASNzFodguTjDuMyZv277KdQ\",\n"
                + "    \"start_time\": 1733577235000,\n"
                + "    \"end_time\": 1733579533000,\n"
                + "    \"create_time\": \"2024-12-07 21:13:55\",\n"
                + "    \"operate_time\": \"2024-12-07 21:51:48\",\n"
                + "    \"operator\": \"p_pymyan\",\n"
                + "    \"operator_type\": 2,\n"
                + "    \"inner_reply\": \"待客户实施或验证：已有解决方案，待验证[br][br]方案截图：[br][img src=https://ticket-10039692.cossh.myqcloud.com/1732698568142_9912.png][br]数据盘资源 不足\",\n"
                + "    \"extern_reply\": \"\",\n"
                + "    \"qcloud_comment_id\": 0,\n"
                + "    \"target_status\": 29,\n"
                + "    \"next_operator\": \"p_pymyan\",\n"
                + "    \"next_assign\": 0,\n"
                + "    \"duration\": 1522,\n"
                + "    \"remark\": \"\",\n"
                + "    \"secret_content\": \"\",\n"
                + "    \"is_undo\": 0,\n"
                + "    \"company_id\": 0,\n"
                + "    \"target_post\": 12,\n"
                + "    \"cc_person\": \"\",\n"
                + "    \"operation_type\": 62,\n"
                + "    \"status\": 22,\n"
                + "    \"current_operator\": \"p_pymyan\",\n"
                + "    \"fact_assign\": 3218,\n"
                + "    \"post\": 12,\n"
                + "    \"responsible\": \"v_nanxpan\",\n"
                + "    \"next_responsible\": \"v_nanxpan\",\n"
                + "    \"customer_fields\": \"{\\\"priority\\\":2,\\\"validation_scene\\\":\\\"to_be_validation\\\"}\",\n"
                + "    \"request_source\": \"\",\n"
                + "    \"target_extern_status\": 1,\n"
                + "    \"data_type\": \"%s\",\n"
                + "    \"service_channel\": 27,\n"
                + "    \"service_scene\": 257696,\n"
                + "    \"ticket_title\": \"【中信建投-TDSQL -李敬财】tdsql 创建实例失败\",\n"
                + "    \"priority\": 2,\n"
                + "    \"service_scene_level1_name\": \"云产品一部/国产数据库产品中心\",\n"
                + "    \"service_scene_level2_name\": \"腾讯云TDSQL\",\n"
                + "    \"service_scene_level3_name\": \"咨询\",\n"
                + "    \"service_scene_level4_name\": \"TDSQL原理相关\",\n"
                + "    \"risk_type\": \"\",\n"
                + "    \"context\": \"事件标题:【中信建投-TDSQL -李敬财】tdsql 创建实例失败描述及影响:【中信建投-TDSQL -李敬财】tdsql 创建实例失败\\n1、【问题描述】tdsql 创建实例失败\\n2、【补充信息】暂无\\n3、【触发条件】暂无\\n4、【版本信息】TDSQL-MySQL-10.3.16"
                + ".2_ABC_ARM\\n5、【业务影响】暂无\\n6、【环境信息】暂无\\n7、【是否重启过什么服务】暂无\\n8、【问题进展】暂无\\n\",\n"
                + "    \"problem_summary\": \"tdsql 创建实例失败\",\n"
                + "    \"reflection_type_classify\": \"L3\",\n"
                + "    \"is_consult\": \"否\",\n"
                + "    \"is_obvious\": \"否\",\n"
                + "    \"reason\": \"虽然问题描述中没有明确提到生产故障、业务受损或客户情绪化，但由于tdsql创建实例失败可能会影响到业务运行，因此将其归类为L3。\",\n"
                + "    \"try_to_recovered\": \"否\",\n"
                + "    \"preLlmRes\": {\n"
                + "        \"problem_summary\": \"tdsql 创建实例失败\",\n"
                + "        \"reflection_type_classify\": \"L3\",\n"
                + "        \"is_consult\": \"否\",\n"
                + "        \"is_obvious\": \"否\",\n"
                + "        \"reason\": \"虽然问题描述中没有明确提到生产故障、业务受损或客户情绪化，但由于tdsql创建实例失败可能会影响到业务运行，因此将其归类为L3。\",\n"
                + "        \"try_to_recovered\": \"否\"\n"
                + "    }\n"
                + "}", dataType);
    }

    private static Strategy buildTestStrategy(String chunkType, String dataType) throws Exception {
        Strategy strategy = Strategy.builder()
                .id(7)
                .sceneIdentify("13520914")
                .name("TDSQL工单优先级L3二次校验")
                .scene(Scene.Ticket)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'retry_strategy' && reflection_type_classify == 'L3'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(Chunk.builder()
                        .type(ChunkType.valueOf(chunkType))
                        .maxSize(28 * 1024)
                        .delimiter("\n")
                        .conversationSpliceType(SpliceType.TicketQualityInspection)
                        .build())
                .analyzes(
                        new ArrayList<Analyze>() {{
                            add(AnalyzeConfig.Professional_Skills);
                            add(AnalyzeConfig.Service_Awareness);
                            add(AnalyzeConfig.Communication_Skills);
                            add(AnalyzeConfig.Ticket_Service_Timeliness);
                            add(AnalyzeConfig.Service_Specifications);
                        }}.toArray(new Analyze[0])
                ).build();

        strategy.trigger.setTriggerTimestamp(1732683735406L);

        strategy.trigger.data = new JsonNode[]{objectMapper.readTree(getInputData(dataType))};

        return strategy;
    }
}