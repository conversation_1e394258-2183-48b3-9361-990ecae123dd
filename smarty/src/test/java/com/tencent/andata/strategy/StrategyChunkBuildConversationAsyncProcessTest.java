package com.tencent.andata.strategy;


import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.config.ChunkConfig;
import com.tencent.andata.smart.etl.process.StrategyChunkBuildConversationAsyncProcess;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import java.util.Arrays;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.PrintSinkFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

public class StrategyChunkBuildConversationAsyncProcessTest {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        Properties properties = PropertyUtils.loadProperties("env.properties");

        String chunkType = parameterTool.get("chunkType");
        String dataType = parameterTool.get("dataType");
        // 构造测试数据
        DataStream<Strategy> inputStream = env.addSource(new TestStrategySource(chunkType, dataType));

        // 获取上下文
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        String zkQuorum = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM");
        String zkNodeParent = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT");

        // 创建异步处理器
        StrategyChunkBuildConversationAsyncProcess asyncProcessor =
                new StrategyChunkBuildConversationAsyncProcess(zkQuorum, zkNodeParent);

        // 应用异步处理
        DataStream<Strategy> resultStream = AsyncDataStream
                .unorderedWait(inputStream, asyncProcessor, 30, TimeUnit.MINUTES, 3);

        // 添加打印sink
        resultStream.addSink(new PrintSinkFunction<Strategy>() {
            @Override
            public void invoke(Strategy strategy, Context context) {
                System.out.println("==== Processing Result ====");
                System.out.println("Strategy ID: " + strategy.id);
                System.out.println("Strategy Name: " + strategy.name);
                System.out.println("Conversation: " + strategy.chunk.conversation);
                System.out.println("========================");
            }
        });

        // 执行任务
        env.execute("Test StrategyChunkBuildConversationAsyncProcess");
    }

    private static class TestStrategySource implements SourceFunction<Strategy> {

        private volatile boolean isRunning = true;

        private String chunkType;
        private String dataType;
        private long counter = 0;

        private TestStrategySource(String chunkType, String dataType) {
            this.chunkType = chunkType;
            this.dataType = dataType;
        }


        private static String getInputData(String dataType) {
            return String.format("{\n" +
                    "    \"action_type\": \"insert\",\n" +
                    "    \"record_update_time\": \"2025-04-18 09:30:38\",\n" +
                    "    \"requestid\": \"5a8fcfa0-ee3c-492c-a987-9e3ea2b6ccc0\",\n" +
                    "    \"rpc_name\": \"FinishUserConversation\",\n" +
                    "    \"table\": \"ods_im_online_customer_service_backend_data\",\n" +
                    "    \"value_of_primary_key\": \"1744939838511407886|@TGS#247SBMPQN\",\n" +
                    "    \"conversation_id\": \"@TGS#247SBMPQN\",\n" +
                    "    \"owner_uin\": \"100005100285\",\n" +
                    "    \"uin\": \"100005100285\",\n" +
                    "    \"uid\": \"\",\n" +
                    "    \"source\": \"MC\",\n" +
                    "    \"status\": 12,\n" +
                    "    \"category_id\": 246,\n" +
                    "    \"first_should_assign\": 3652,\n" +
                    "    \"should_assign\": 3652,\n" +
                    "    \"fact_assign\": 3652,\n" +
                    "    \"service_scene\": 21036,\n" +
                    "    \"current_staff\": \"83726\",\n" +
                    "    \"staffs\": \"83726\",\n" +
                    "    \"appraise\": \"\",\n" +
                    "    \"service_rate\": 0,\n" +
                    "    \"unsatisfy_reason\": 0,\n" +
                    "    \"conversation_service_rate\": 0,\n" +
                    "    \"conversation_unsatisfy_reason\": \"\",\n" +
                    "    \"product_rate\": 0,\n" +
                    "    \"product_unsatisfy_reason\": \"\",\n" +
                    "    \"recommend_score\": -1,\n" +
                    "    \"appraise_time\": 0,\n" +
                    "    \"create_time\": 1744874446,\n" +
                    "    \"customer_updated_time\": 1744939838,\n" +
                    "    \"staff_updated_time\": 1744888261,\n" +
                    "    \"ticket_ids\": \"\",\n" +
                    "    \"conversation_ticket_ids\": \"14306926\",\n" +
                    "    \"title\": \"我这个短信还是会被运营商拦截\",\n" +
                    "    \"all_level_category\": \"{\\\"first_level\\\":{\\\"id\\\":29,\\\"name\\\":\\\"视频与通信服务\\\",\\\"name_en\\\":\\\"Video and communication service\\\",\\\"service_scene\\\":0},\\\"second_level\\\":{\\\"id\\\":243,"
                    +
                    "\\\"name\\\":\\\"短信\\\",\\\"name_en\\\":\\\"Short Message Service\\\",\\\"service_scene\\\":0},\\\"third_level\\\":{\\\"id\\\":246,\\\"name\\\":\\\"短信签名审批\\\",\\\"name_en\\\":\\\"Signature approval\\\"," +
                    "\\\"service_scene\\\":14490}}\",\n" +
                    "    \"customer_first_updated_time\": 1744874512,\n" +
                    "    \"staff_first_updated_time\": 1744874451,\n" +
                    "    \"is_alarm\": false,\n" +
                    "    \"solve_status\": 0,\n" +
                    "    \"customer_name\": \"河北昌明信息科技有限公司\",\n" +
                    "    \"is_clean\": 1,\n" +
                    "    \"customer_type\": 1,\n" +
                    "    \"finish_time\": 1744939838,\n" +
                    "    \"staff_title\": \"您好，非常荣幸为您服务，这边先看一下您的问题，有结果第一时间反馈给您，请您稍等。\",\n" +
                    "    \"is_transferred\": false,\n" +
                    "    \"chat_type\": 0,\n" +
                    "    \"company_id\": 336,\n" +
                    "    \"is_ticket_created\": true,\n" +
                    "    \"set_wait_status_time\": 0,\n" +
                    "    \"apply_finish_time\": 1744888284,\n" +
                    "    \"awaiting_supplement_time\": 0,\n" +
                    "    \"creator\": \"100005100285\",\n" +
                    "    \"creator_type\": 1,\n" +
                    "    \"finisher\": \"100005100285\",\n" +
                    "    \"finish_type\": 1,\n" +
                    "    \"post\": 2,\n" +
                    "    \"parent\": \"\",\n" +
                    "    \"contact\": \"\",\n" +
                    "    \"marked_by_staffs\": \"\",\n" +
                    "    \"is_ola_alarm\": false,\n" +
                    "    \"has_complaint\": false,\n" +
                    "    \"sem_category_id\": 0,\n" +
                    "    \"service_scene_level4_name\": \"运营商原因发送失败\",\n" +
                    "    \"service_scene_level2_name\": \"短信\",\n" +
                    "    \"service_scene_level1_name\": \"云产品五部/通信产品中心\",\n" +
                    "    \"service_scene_level3_name\": \"短信收发问题\",\n" +
                    "    \"data_type\": \"%s\",\n" +
                    "    \"display_content\": \"\",\n" +
                    "    \"risk_type\": \"\"\n" +
                    "}", dataType);
        }

        private static Strategy buildTestStrategy(String chunkType, String dataType) throws Exception {
            Strategy strategy = Strategy.builder()
                    .id(7)
                    .sceneIdentify("@TGS#247SBMPQN")
                    .name("WebIM结单质检")
                    .scene(Scene.WebIM)
                    .condition(
                            Condition.builder()
                                    .expression(
                                            "data_type == 'webim'")
                                    .build()
                    )
                    .trigger(Trigger.builder()
                            .type(TriggerType.Immediately)
                            .needPersist(true)
                            .build())
                    .chunk(ChunkConfig.WebIMReply)
                    .analyzes(Arrays.asList(
                                    AnalyzeConfig.Professional_Skills,
                                    AnalyzeConfig.Service_Awareness,
                                    AnalyzeConfig.Communication_Skills,
                                    AnalyzeConfig.Ticket_Service_Timeliness,
                                    AnalyzeConfig.Service_Specifications)
                            .toArray(new Analyze[0])
                    ).build();

            strategy.trigger.setTriggerTimestamp(1744939838709L);

            strategy.trigger.data = new JsonNode[]{objectMapper.readTree(getInputData(dataType))};

            return strategy;
        }

        @Override
        public void run(SourceContext<Strategy> ctx) throws Exception {
            while (isRunning) {
                Strategy strategy = buildTestStrategy(chunkType, dataType);
                strategy.id = (int) counter++;  // 使每个Strategy唯一
                ctx.collect(strategy);

                // 每10秒发送一条数据
                Thread.sleep(100000);
            }
        }

        @Override
        public void cancel() {
            isRunning = false;
        }
    }
}