package com.tencent.andata.quality.operators;

import com.tencent.andata.smart.access.operators.WebImMsgDataProcess;
import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import com.tencent.andata.utils.HBaseSinkFunction.OperationType;
import com.tencent.andata.utils.JSONUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction.Context;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import static com.tencent.andata.smart.access.operators.WebImMsgDataProcess.extractWebImMsgContent;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class WebImMsgDataProcessTest {

    private static final OutputTag<JsonNode> WEBIM_OUTPUT_TAG = new OutputTag<JsonNode>("webim-side-output") {};
    private WebImMsgDataProcess webImMsgDataProcess;
    private static ObjectMapper objectMapper;
    private Collector<HbaseRow> collector;
    private JSONUtils jsonUtils;
    private Context context;
    private String msgData;

    @Before
    public void setUp() {
        webImMsgDataProcess = new WebImMsgDataProcess(WEBIM_OUTPUT_TAG);
        objectMapper = new ObjectMapper();
        jsonUtils = new JSONUtils();
        context = mock(Context.class);
        collector = mock(Collector.class);
        msgData = "{\"action_type\":\"insert\",\"msgdata\":\"{\\\"MsgType\\\":4,\\\"Rich\\\":[{\\\"MsgType\\\":1,\\\"Content\\\":\\\"2222\\\"}]}\",\"msgseq\":\"13\",\"record_update_time\":\"2025-03-06 14:21:31\"," +
                "\"requestid\":\"86872b8a-e184-4ee7-a75f-d85bfc890651\",\"rpc_name\":\"SendUserMsg\",\"table\":\"ods_im_online_customer_service_backend_data\",\"value_of_primary_key\":\"1741242091913302474|@TGS#22Q225HQS\"," +
                "\"conversation_id\":\"@TGS#22Q225HQS\",\"owner_uin\":\"121073510\",\"uin\":\"121073510\",\"uid\":\"\",\"source\":\"MC\",\"status\":3,\"category_id\":28,\"first_should_assign\":4185,\"should_assign\":4185," +
                "\"fact_assign\":4185,\"service_scene\":33088,\"current_staff\":\"2129\",\"staffs\":\"2129\",\"appraise\":\"\",\"service_rate\":0,\"unsatisfy_reason\":0,\"conversation_service_rate\":0," +
                "\"conversation_unsatisfy_reason\":\"\",\"product_rate\":0,\"product_unsatisfy_reason\":\"\",\"recommend_score\":-1,\"appraise_time\":0,\"create_time\":1741145091,\"customer_updated_time\":1741242091," +
                "\"staff_updated_time\":1741242082,\"ticket_ids\":\"\",\"conversation_ticket_ids\":\"11212820\",\"title\":\"11111222\",\"all_level_category\":\"{\\\"first_level\\\":{\\\"id\\\":1,\\\"name\\\":\\\"咨询\\\"," +
                "\\\"name_en\\\":\\\"General  problems\\\",\\\"service_scene\\\":0},\\\"second_level\\\":{\\\"id\\\":20,\\\"name\\\":\\\"ICP备案\\\",\\\"name_en\\\":\\\"ICP Filing\\\",\\\"service_scene\\\":0}," +
                "\\\"third_level\\\":{\\\"id\\\":28,\\\"name\\\":\\\"备案规则咨询\\\",\\\"name_en\\\":\\\"Filing rule consultation\\\",\\\"service_scene\\\":33088}}\",\"customer_first_updated_time\":1741242052," +
                "\"staff_first_updated_time\":1741145189,\"is_alarm\":false,\"solve_status\":0,\"customer_name\":\"\",\"is_clean\":1,\"customer_type\":0,\"finish_time\":0,\"staff_title\":\"111111\",\"is_transferred\":false," +
                "\"chat_type\":0,\"company_id\":0,\"is_ticket_created\":true,\"set_wait_status_time\":0,\"apply_finish_time\":0,\"awaiting_supplement_time\":0,\"creator\":\"2129\",\"creator_type\":2,\"finisher\":\"\",\"finish_type\":0," +
                "\"post\":2,\"parent\":\"@TGS#2IDZ4VGQJ\",\"contact\":\"\",\"marked_by_staffs\":\"\",\"is_ola_alarm\":false,\"has_complaint\":false,\"sem_category_id\":0,\"service_scene_level4_name\":\"null\"," +
                "\"service_scene_level2_name\":\"ICP备案\",\"service_scene_level1_name\":\"云产品一部/轻量云产品中心\",\"service_scene_level3_name\":\"网站-备案规则咨询\",\"data_type\":\"webim\",\"display_content\":\"2222\",\"risk_type\":\"无风险,无风险,无风险,无风险\"}";
    }

    @Test
    public void testProcessElement_NormalCase() throws Exception {
        // Prepare test data
        JsonNode value = jsonUtils.toSnakeCase(jsonUtils.flatten(objectMapper.readTree(msgData)));

        // Execute
        webImMsgDataProcess.processElement(value, context, collector);

        // Verify HbaseRow output
        ArgumentCaptor<HbaseRow> hbaseRowCaptor = ArgumentCaptor.forClass(HbaseRow.class);
        verify(collector).collect(hbaseRowCaptor.capture());
        HbaseRow capturedRow = hbaseRowCaptor.getValue();
        // System.out.println(capturedRow.getData());

        assertEquals("@TGS#2ELFMHIQG-1741241364810678730", capturedRow.getRowKey());

        // Verify side output
        verify(context).output(eq(WEBIM_OUTPUT_TAG), any(JsonNode.class));
    }

    @Test
    public void testProcessElement_WrongTable() throws Exception {
        // Prepare test data with wrong table
        JsonNode value = jsonUtils.toSnakeCase(jsonUtils.flatten(objectMapper.readTree(msgData)));

        // Execute
        webImMsgDataProcess.processElement(value, context, collector);

        System.out.println("extractValue: " + extractWebImMsgContent(value.get("msgdata")));

        // Verify no outputs
        //verify(collector, any()).collect(any());
        verify(context).output(eq(WEBIM_OUTPUT_TAG), any(JsonNode.class));
        //verify(context, any()).output(any(), any());
    }

    @Test
    public void testProcessElement_EmptyMsgData() throws Exception {
        // Prepare test data with empty msgdata
        String msgData = "{\n" +
                "    \"table\": \"ods_im_online_customer_service_backend_data\",\n" +
                "    \"value_of_primary_key\": \"1740799270060783148|@TGS#2B4O2FGQT\",\n" +
                "    \"conversation_id\": \"@TGS#2B4O2FGQT\",\n" +
                "    \"msgdata\": null\n" +
                "}";
        JsonNode value = jsonUtils.toSnakeCase(jsonUtils.flatten(objectMapper.readTree(msgData)));

        // Execute
        webImMsgDataProcess.processElement(value, context, collector);

        // Verify output
        ArgumentCaptor<HbaseRow> hbaseRowCaptor = ArgumentCaptor.forClass(HbaseRow.class);
        verify(collector).collect(hbaseRowCaptor.capture());
        HbaseRow capturedRow = hbaseRowCaptor.getValue();

        // Verify rowKey
        assertEquals("@TGS#2B4O2FGQT-1740799270060783148", capturedRow.getRowKey());

        // Verify operation type
        assertEquals(OperationType.INSERT, capturedRow.getType());

        // Verify column family
        assertEquals("cf", capturedRow.getFamily());

        // Verify side output
        verify(context).output(eq(WEBIM_OUTPUT_TAG), any(JsonNode.class));
    }

    @Test
    public void testExtractWebImMsgContent() throws JsonProcessingException {
        // Test text message
        String textMsg = "{\"Rich\":[{\"MsgType\":1,\"Content\":\"Hello\"}]}";
        assertEquals("Hello", extractWebImMsgContent(objectMapper.createObjectNode().put("msgdata", textMsg).get("msgdata")));

        // Test image message
        String imageMsg = "{\"Rich\":[{\"MsgType\":2,\"Content\":\"image.jpg\"}]}";
        assertEquals("[图片]", extractWebImMsgContent(objectMapper.createObjectNode().put("msgdata", imageMsg).get("msgdata")));

        // Test unknown message type
        String unknownMsg = "{\"Rich\":[{\"MsgType\":99,\"Content\":\"unknown\"}]}";
        assertEquals("", extractWebImMsgContent(objectMapper.createObjectNode().put("msgdata", unknownMsg).get("msgdata")));
    }
}