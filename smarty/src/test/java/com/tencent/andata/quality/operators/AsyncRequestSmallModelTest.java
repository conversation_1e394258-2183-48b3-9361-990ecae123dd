package com.tencent.andata.quality.operators;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.tencent.andata.smart.access.operators.AsyncRequestSmallModelV1;
import java.util.Collection;
import java.util.regex.Pattern;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

public class AsyncRequestSmallModelTest {

    private AsyncRequestSmallModelV1 asyncRequestSmallModel;
    private ResultFuture<JsonNode> resultFuture;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        asyncRequestSmallModel = new AsyncRequestSmallModelV1();
        asyncRequestSmallModel.open(new Configuration());
        resultFuture = mock(ResultFuture.class);
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testAsyncInvoke() throws Exception {
        // 创建输入数据
        JsonNode input = objectMapper.createObjectNode()
                .put("data_type", "group")
                .put("sender_type", "客户")
                .put("display_content", "據我了解，你這個是產品贈送的，我根本用不到。你如果真想補償，就不要弄這些用不到的東西給我們\n");

        // 调用 asyncInvoke 方法
        asyncRequestSmallModel.asyncInvoke(input, resultFuture);

        // 捕获 ResultFuture 的参数
        ArgumentCaptor<Collection<JsonNode>> captor = ArgumentCaptor.forClass(Collection.class);
        verify(resultFuture, timeout(5000)).complete(captor.capture());

        // 验证结果
        Collection<JsonNode> result = captor.getValue();
        assertEquals(1, result.size()); // 确保有一个结果
        JsonNode output = result.iterator().next();
        System.out.println(output);

        String riskType = output.get("risk_type").asText();
        assertTrue(Pattern.compile("赔偿问题").matcher(riskType).find()); // 替换为期望的风险类型
    }
}