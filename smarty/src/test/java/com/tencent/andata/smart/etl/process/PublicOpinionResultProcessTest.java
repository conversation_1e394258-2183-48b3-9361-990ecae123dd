package com.tencent.andata.smart.etl.process;

import static org.mockito.ArgumentMatchers.any;

import com.tencent.andata.smart.etl.domain.RiskResult;
import com.tencent.andata.smart.etl.handler.WebIMSceneHandler;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * PublicOpinionResultProcess类的测试
 * 针对buildRiskResult方法进行测试
 */
public class PublicOpinionResultProcessTest {

    private ObjectMapper objectMapper;
    private CustomerRepository customerRepository;
    private TicketRepository ticketRepository;
    private DutyRepository dutyRepository;
    private WebIMSceneHandler handler;
    private PublicOpinionResultProcess process;

    @Before
    public void setup() {
        objectMapper = new ObjectMapper();
        // 创建模拟的依赖对象
        customerRepository = Mockito.mock(CustomerRepository.class);
        ticketRepository = Mockito.mock(TicketRepository.class);
        dutyRepository = Mockito.mock(DutyRepository.class);

        // 创建WebIMSceneHandler实例
        handler = Mockito.mock(WebIMSceneHandler.class);

        // 创建PublicOpinionResultProcess实例
        process = new PublicOpinionResultProcess(
                customerRepository,
                ticketRepository,
                dutyRepository,
                objectMapper
        );
    }

    @Test
    public void testBuildRiskResult() throws Exception {
        // 解析Strategy JSON
        String strategyJson =
                "{\n" +
                        "    \"id\": 4,\n" +
                        "    \"name\": \"WebIM工单风控识别\",\n" +
                        "    \"scene\": \"WebIM\",\n" +
                        "    \"condition\": {\n" +
                        "        \"expression\": \"data_type == 'webim' && !include(seq.list(6, 12), status) && regexMatch(source, '^(PRESALE|MC|MP|MA|QDSAAS)$') && (rpc_name == 'SendUserMsg' || rpc_name == 'SendChatCallbackMsg' || " +
                        "rpc_name == 'CreateConversation') && regexMatch(risk_type, '内部投诉|舆情监管|已诉讼|一般催单|业务疑似影响|业务影响|负面情绪|紧急催单|复现|赔偿问题|情绪激动|威胁切量|自残行为|数据丢失|计费问题|微信业务受影响|证书问题')\"\n"
                        +
                        "    },\n" +
                        "    \"trigger\": {\n" +
                        "        \"type\": \"Immediately\",\n" +
                        "        \"needPersist\": true,\n" +
                        "        \"data\": [\n" +
                        "            {\n" +
                        "                \"action_type\": \"insert\",\n" +
                        "                \"msgdata\": \"{\\\"MsgType\\\":4,\\\"Rich\\\":[{\\\"MsgType\\\":1,\\\"Content\\\":\\\"有个紧急短信 想麻烦加快审核下\\\"}]}\",\n" +
                        "                \"msgseq\": \"6\",\n" +
                        "                \"record_update_time\": \"2025-05-21 16:18:45\",\n" +
                        "                \"requestid\": \"c02e53e5-5a2c-46aa-b6f1-315efef5aa07\",\n" +
                        "                \"rpc_name\": \"SendUserMsg\",\n" +
                        "                \"table\": \"ods_im_online_customer_service_backend_data\",\n" +
                        "                \"value_of_primary_key\": \"1747815525891527685|@TGS#2KWZZT2QO\",\n" +
                        "                \"conversation_id\": \"@TGS#2KWZZT2QO\",\n" +
                        "                \"owner_uin\": 100038741197,\n" +
                        "                \"uin\": 100038741197,\n" +
                        "                \"uid\": \"\",\n" +
                        "                \"source\": \"MC\",\n" +
                        "                \"status\": 3,\n" +
                        "                \"category_id\": 247,\n" +
                        "                \"first_should_assign\": 3652,\n" +
                        "                \"should_assign\": 3652,\n" +
                        "                \"fact_assign\": 3652,\n" +
                        "                \"service_scene\": 11823,\n" +
                        "                \"current_staff\": \"83728\",\n" +
                        "                \"staffs\": \"83728\",\n" +
                        "                \"appraise\": \"\",\n" +
                        "                \"service_rate\": 0,\n" +
                        "                \"unsatisfy_reason\": 0,\n" +
                        "                \"conversation_service_rate\": 0,\n" +
                        "                \"conversation_unsatisfy_reason\": \"\",\n" +
                        "                \"product_rate\": 0,\n" +
                        "                \"product_unsatisfy_reason\": \"\",\n" +
                        "                \"recommend_score\": -1,\n" +
                        "                \"appraise_time\": 0,\n" +
                        "                \"create_time\": \"2025-05-21 16:18:21\",\n" +
                        "                \"customer_updated_time\": 1747815525,\n" +
                        "                \"staff_updated_time\": 1747815506,\n" +
                        "                \"ticket_ids\": \"\",\n" +
                        "                \"conversation_ticket_ids\": \"14537485\",\n" +
                        "                \"title\": \"\",\n" +
                        "                \"all_level_category\": \"\",\n" +
                        "                \"customer_first_updated_time\": 1747815525,\n" +
                        "                \"staff_first_updated_time\": 1747815506,\n" +
                        "                \"is_alarm\": false,\n" +
                        "                \"solve_status\": 0,\n" +
                        "                \"customer_name\": \"开封大学\",\n" +
                        "                \"is_clean\": 0,\n" +
                        "                \"customer_type\": 2,\n" +
                        "                \"finish_time\": 0,\n" +
                        "                \"staff_title\": \"\",\n" +
                        "                \"is_transferred\": false,\n" +
                        "                \"chat_type\": 0,\n" +
                        "                \"company_id\": 336,\n" +
                        "                \"is_ticket_created\": true,\n" +
                        "                \"set_wait_status_time\": 0,\n" +
                        "                \"apply_finish_time\": 0,\n" +
                        "                \"awaiting_supplement_time\": 0,\n" +
                        "                \"creator\": \"100038741197\",\n" +
                        "                \"creator_type\": 1,\n" +
                        "                \"finisher\": \"\",\n" +
                        "                \"finish_type\": 0,\n" +
                        "                \"post\": 2,\n" +
                        "                \"parent\": \"\",\n" +
                        "                \"contact\": \"\",\n" +
                        "                \"marked_by_staffs\": \"\",\n" +
                        "                \"is_ola_alarm\": false,\n" +
                        "                \"has_complaint\": false,\n" +
                        "                \"sem_category_id\": 0,\n" +
                        "                \"ticket_id\": 14537485,\n" +
                        "                \"service_scene_level4_name\": \"\",\n" +
                        "                \"service_scene_level3_name\": \"\",\n" +
                        "                \"service_scene_level2_name\": \"\",\n" +
                        "                \"service_scene_level1_name\": \"\",\n" +
                        "                \"customerName\": \"开封大学\",\n" +
                        "                \"fact_assign_duty_name\": \"在线-云通信队列\",\n" +
                        "                \"current_operator\": \"何婷（金道）\",\n" +
                        "                \"responsible\": \"何婷（金道）\",\n" +
                        "                \"url\": \"https://andon.cloud.tencent.com/ticket/nologin/redirect?id=14537485&sign=c923e70c46bdf9d0780a473b5dbfe255\",\n" +
                        "                \"start_time\": 1747815501000,\n" +
                        "                \"service_channel\": 39,\n" +
                        "                \"ticket_status\": 22,\n" +
                        "                \"question\": \"提单信息：\\n请输入问题描述 ：着急发短信，请加速审核\\n\",\n" +
                        "                \"priority\": 2,\n" +
                        "                \"group_id\": \"\",\n" +
                        "                \"end_time\": 1747815525000,\n" +
                        "                \"name\": \"\",\n" +
                        "                \"data_type\": \"webim\",\n" +
                        "                \"display_content\": \"有个紧急短信 想麻烦加快审核下\",\n" +
                        "                \"risk_type\": \"无风险,无风险,无风险,一般催单\"\n" +
                        "            }\n" +
                        "        ],\n" +
                        "        \"triggerTimestamp\": 1747815526129\n" +
                        "    },\n" +
                        "    \"chunk\": {\n" +
                        "        \"type\": \"Conversation\",\n" +
                        "        \"value\": null,\n" +
                        "        \"maxSize\": 28672,\n" +
                        "        \"delimiter\": \"\\n\",\n" +
                        "        \"conversationSpliceType\": \"WebIMPublicOpinion\",\n" +
                        "        \"conversation\": \"坐席: 猜您想了解：      短信送达失败提示运营商关键字拦截      短信新手指引      短信显示发送中，用户无法收到短信      短信审核周期      短信送达失败提示运营商内部错误      腾讯云短信发送成功，用户未收到短信      短信服务无法使用      短信签名审核状态说明      快速了解短信签名审核的内容规范      "
                        +
                        "国内短信签名实名问题处理建议您好!欢迎使用腾讯云。有什么售后问题可以帮您？O(∩_∩)O有关短信的问题，您也可以通过联系腾讯云短信小助手 的人工服务了解情况并获得专业支持。客户: 人工客服坐席: 如需人工服务，请点击联系腾讯云技术顾问\\n客户回复：请输入问题描述 ：着急发短信，请加速审核\",\n"
                        +
                        "        \"operations\": null,\n" +
                        "        \"aviatorExpressions\": null\n" +
                        "    },\n" +
                        "    \"analyzes\": [\n" +
                        "        {\n" +
                        "            \"type\": \"Risk_Analyze\",\n" +
                        "            \"name\": \"Public_Opinion_Risk\",\n" +
                        "            \"desc\": \"风险总结分析\",\n" +
                        "            \"prompt\": \"请分析客户与客服的对话，基于客户发言内容进行风险等级的判断，风险等级分别为D/C/B/A/S，等级说明如下，\\nC：包括两种情况，满足其中一项或以上则命中C风险：\\n    C-1 客户表示“尽快解决”、“马上解决”、“请及时处理”、“能加快吗”、“速度”、“快点”等，且没有表示客服长时间处理仍未解决问题如“从XX时候开始就在处理问题了，怎么现在还没没弄好”。\\n"
                        +
                        "    C-2 客户存在负面情绪，但没有出现辱骂和自残、自杀威胁的情况。\\nB：包括两种情况，满足其中一项或以上则命中B风险：\\n    B-1 客户明确要求赔偿或退费。\\n    B-2 客户明确提到内部投诉，包括直接联系腾讯云高层领导投诉。\\nA：包括七种情况，满足其中一项或以上则命中A风险：\\n    A-1 客户明确说单台服务器出现故障，但不包括服务器登录不上。\\n    A-2 "
                        +
                        "客户明确说数据库出现异常或数据丢失。\\n    A-3 客户明确提到不再使用腾讯云产品，或改用其他云厂商（如华为云、阿里云、亚马逊AWS、微软Azure等）产品，或其他云厂商产品比腾讯云产品更有优势。\\n    A-4 客户表示客服长时间处理仍未解决问题，如“从XX时候开始就在处理问题了，怎么现在还没没弄好”。\\n    A-5 客户有辱骂行为。\\n    A-6 "
                        +
                        "客户发言有舆情监管的风险如：消费者协会，工信部，12345，12315，国家市场监督总局, 社交媒体曝光等。\\n    A-7 客户有诉讼倾向，或以诉讼威胁客服，但实际未提起诉讼。\\nS：包括四种情况，满足其中一项或以上则命中S风险：\\n    S-1 客户明确说批量机器出现故障。\\n    S-2 客户明确说明线上、生产、现网的业务受到影响。\\n    S-3 客户以自残、自杀威胁客服。\\n    S-4 "
                        +
                        "客户明确说已经提起诉讼。\\nD：无风险或S、A、B、C下的风险情形都不满足；\\n注意：请严格根据以上标准判断风险，需要详细判别每一个风险情形是否存在，详细输出所有命中风险的原文和风险情形编号，不要漏识别。风险等级S>A>B>C>D，风险不可累加，如果判断后出现多个风险等级，请选择最高等级。\\n对话：\\n```\\n%s\\n```\\n以json格式输出“reason”与“risk_level”例如：\\n"
                        +
                        "{\\\"reason\\\":\\\"xxx\\\",\\\"risk_level\\\":\\\"x\\\"}\\n只输出一个以上格式的json，不要输出其他内容。请详细、一步步思考，将思考过程放到“reason”里，注意不要过度解读。\",\n" +
                        "            \"res\": \"{\\\"reason\\\":\\\"客户表示“着急发短信，请加速审核”，命中C-1风险情形，客户要求加快处理速度。\\\",\\\"risk_level\\\":\\\"C\\\"}\",\n" +
                        "            \"model\": \"risk_sft\",\n" +
                        "            \"resType\": \"Json\",\n" +
                        "            \"modelExtraParams\": null,\n" +
                        "            \"messages\": null\n" +
                        "        }\n" +
                        "    ],\n" +
                        "    \"sceneIdentify\": \"@TGS#2KWZZT2QO\"\n" +
                        "}";
        Strategy strategy = objectMapper.readValue(strategyJson, Strategy.class);

        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        PublicOpinionResultProcess process = PublicOpinionResultProcess.create(rainbowUtils);

        WebIMSceneHandler handler1 = new WebIMSceneHandler(dutyRepository, ticketRepository, customerRepository);

        Option<RiskResult> riskResult = process.buildRiskResult(strategy, handler1);
        System.out.println(riskResult.get());
    }
}