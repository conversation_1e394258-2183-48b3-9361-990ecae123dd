package com.tencent.andata.smart.etl.process;

import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.streaming.api.operators.KeyedProcessOperator;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.TestHarnessUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StrategyDelayProcess 单元测试
 */
public class StrategyDelayProcessTest {

    private KeyedOneInputStreamOperatorTestHarness<String, Strategy, Strategy> testHarness;
    private StrategyDelayProcess delayProcess;

    @BeforeEach
    public void setup() throws Exception {
        delayProcess = new StrategyDelayProcess();
        testHarness = new KeyedOneInputStreamOperatorTestHarness<>(
                new KeyedProcessOperator<>(delayProcess),
                strategy -> strategy.sceneIdentify != null ? strategy.sceneIdentify : "default",
                String.class
        );
        testHarness.open();
    }

    @Test
    public void testNonCallCenterQualityInspection_ShouldForwardImmediately() throws Exception {
        // 创建非CallCenterQualityInspection类型的策略
        Strategy strategy = createTestStrategy("test-scene", SpliceType.TicketQualityInspection);
        
        // 处理策略
        testHarness.processElement(strategy, 1000L);
        
        // 验证立即输出
        ConcurrentLinkedQueue<Object> output = testHarness.getOutput();
        assertEquals(1, output.size());
        
        // 验证输出的策略是原策略
        Strategy outputStrategy = (Strategy) TestHarnessUtil.getRawElementsFromOutput(output).get(0);
        assertEquals(strategy.id, outputStrategy.id);
        assertEquals(strategy.sceneIdentify, outputStrategy.sceneIdentify);
    }

    @Test
    public void testCallCenterQualityInspection_ShouldDelayOutput() throws Exception {
        // 创建CallCenterQualityInspection类型的策略
        Strategy strategy = createTestStrategy("call-center-scene", SpliceType.CallCenterQualityInspection);
        
        // 处理策略
        testHarness.processElement(strategy, 1000L);
        
        // 验证没有立即输出
        ConcurrentLinkedQueue<Object> output = testHarness.getOutput();
        assertEquals(0, output.size());
        
        // 模拟时间推进到2小时后
        long delayTime = 1000L + TimeUnit.HOURS.toMillis(2);
        testHarness.setProcessingTime(delayTime);
        
        // 验证延迟后有输出
        output = testHarness.getOutput();
        assertEquals(1, output.size());
        
        // 验证输出的策略是原策略
        Strategy outputStrategy = (Strategy) TestHarnessUtil.getRawElementsFromOutput(output).get(0);
        assertEquals(strategy.id, outputStrategy.id);
        assertEquals(strategy.sceneIdentify, outputStrategy.sceneIdentify);
    }

    @Test
    public void testNullStrategy_ShouldForwardImmediately() throws Exception {
        // 处理null策略
        testHarness.processElement(null, 1000L);
        
        // 验证立即输出
        ConcurrentLinkedQueue<Object> output = testHarness.getOutput();
        assertEquals(1, output.size());
        
        // 验证输出的是null
        Strategy outputStrategy = (Strategy) TestHarnessUtil.getRawElementsFromOutput(output).get(0);
        assertNull(outputStrategy);
    }

    @Test
    public void testStrategyWithNullChunk_ShouldForwardImmediately() throws Exception {
        // 创建chunk为null的策略
        Strategy strategy = Strategy.builder()
                .id(1)
                .sceneIdentify("test-scene")
                .chunk(null)
                .build();
        
        // 处理策略
        testHarness.processElement(strategy, 1000L);
        
        // 验证立即输出
        ConcurrentLinkedQueue<Object> output = testHarness.getOutput();
        assertEquals(1, output.size());
        
        // 验证输出的策略是原策略
        Strategy outputStrategy = (Strategy) TestHarnessUtil.getRawElementsFromOutput(output).get(0);
        assertEquals(strategy.id, outputStrategy.id);
    }

    @Test
    public void testMultipleStrategies_MixedTypes() throws Exception {
        // 创建不同类型的策略
        Strategy immediateStrategy = createTestStrategy("immediate-scene", SpliceType.TicketQualityInspection);
        Strategy delayedStrategy = createTestStrategy("delayed-scene", SpliceType.CallCenterQualityInspection);
        
        // 处理策略
        testHarness.processElement(immediateStrategy, 1000L);
        testHarness.processElement(delayedStrategy, 2000L);
        
        // 验证只有immediate策略立即输出
        ConcurrentLinkedQueue<Object> output = testHarness.getOutput();
        assertEquals(1, output.size());
        
        Strategy outputStrategy = (Strategy) TestHarnessUtil.getRawElementsFromOutput(output).get(0);
        assertEquals(immediateStrategy.id, outputStrategy.id);
        
        // 模拟时间推进到2小时后
        long delayTime = 2000L + TimeUnit.HOURS.toMillis(2);
        testHarness.setProcessingTime(delayTime);
        
        // 验证延迟策略也输出了
        output = testHarness.getOutput();
        assertEquals(2, output.size());
        
        Strategy delayedOutputStrategy = (Strategy) TestHarnessUtil.getRawElementsFromOutput(output).get(1);
        assertEquals(delayedStrategy.id, delayedOutputStrategy.id);
    }

    /**
     * 创建测试用的Strategy对象
     */
    private Strategy createTestStrategy(String sceneIdentify, SpliceType spliceType) {
        Chunk chunk = Chunk.builder()
                .conversationSpliceType(spliceType)
                .build();
        
        return Strategy.builder()
                .id(1)
                .sceneIdentify(sceneIdentify)
                .chunk(chunk)
                .build();
    }
}
