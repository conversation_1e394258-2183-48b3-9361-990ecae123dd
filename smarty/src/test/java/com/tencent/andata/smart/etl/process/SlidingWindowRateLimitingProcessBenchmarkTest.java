package com.tencent.andata.smart.etl.process;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.tencent.andata.smart.strategy.model.Strategy;
import java.util.ArrayList;
import java.util.List;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * SlidingWindowRateLimitingProcess 性能基准测试
 * 验证优化前后的性能差异和实际QPM效果
 */
public class SlidingWindowRateLimitingProcessBenchmarkTest {

    private KeyedOneInputStreamOperatorTestHarness<String, Strategy, Strategy> testHarness;
    private SlidingWindowRateLimitingProcess rateLimitingProcess;

    @BeforeEach
    void setUp() throws Exception {
        // QPM=30的标准配置，增加batchSize提高输出效率
        rateLimitingProcess = new SlidingWindowRateLimitingProcess(5, 10000, 2000, 3);

        testHarness = ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                rateLimitingProcess,
                strategy -> strategy.sceneIdentify,
                TypeInformation.of(String.class)
        );

        testHarness.open();
    }

    @Test
    @DisplayName("QPM=30基准测试 - 1分钟场景")
    void testQPM30Benchmark() throws Exception {
        long startTime = 0L;
        testHarness.setProcessingTime(startTime);

        // 快速发送50个事件来测试限流效果
        for (int i = 1; i <= 50; i++) {
            testHarness.setProcessingTime(startTime + i * 100); // 每100ms一个事件
            Strategy strategy = createTestStrategy("benchmark-event-" + i);
            testHarness.processElement(new StreamRecord<>(strategy, startTime + i * 100));
        }
        int eventCount = 50;

        // 等待所有定时器处理完成
        for (int i = 1; i <= 60; i++) {
            testHarness.setProcessingTime(startTime + 5000 + i * 2000);
        }

        int outputCount = testHarness.getOutput().size();

        // 验证QPM限制效果
        System.out.printf("输入事件: %d, 输出事件: %d, QPM限制: 30%n", eventCount, outputCount);

        // 验证所有事件最终都被处理
        assertEquals(eventCount, outputCount, "所有事件最终都应该被输出");
    }

    @Test
    @DisplayName("高并发场景测试 - 突发流量")
    void testBurstTrafficHandling() throws Exception {
        long startTime = 0L;
        testHarness.setProcessingTime(startTime);

        // 模拟突发流量：在500ms内发送30个事件
        for (int i = 1; i <= 30; i++) {
            testHarness.setProcessingTime(startTime + i * 15); // 每15ms一个事件
            Strategy strategy = createTestStrategy("burst-event-" + i);
            testHarness.processElement(new StreamRecord<>(strategy, startTime + i * 15));
        }

        // 推进时间处理缓存事件
        for (int i = 1; i <= 30; i++) {
            testHarness.setProcessingTime(startTime + 500 + i * 2000);
        }

        int outputCount = testHarness.getOutput().size();

        System.out.printf("突发流量测试 - 输入: 30, 输出: %d%n", outputCount);

        // 验证所有事件最终都被处理
        assertEquals(30, outputCount, "所有事件最终都应该被输出");
    }

    @Test
    @DisplayName("长时间运行稳定性测试")
    void testLongRunningStability() throws Exception {
        long startTime = 0L;
        testHarness.setProcessingTime(startTime);

        int totalEvents = 200; // 测试200个事件

        // 模拟快速发送200个事件（在10秒内）
        for (int i = 0; i < totalEvents; i++) {
            testHarness.setProcessingTime(startTime + i * 50); // 每50ms一个事件
            Strategy strategy = createTestStrategy(String.format("long-run-%d", i));
            testHarness.processElement(new StreamRecord<>(strategy, startTime + i * 50));
        }

        // 额外处理时间
        for (int i = 1; i <= 60; i++) {
            testHarness.setProcessingTime(startTime + 10000 + i * 2000);
        }

        int outputCount = testHarness.getOutput().size();

        System.out.printf("长时间测试 - 输入: %d, 输出: %d%n", totalEvents, outputCount);

        // 验证所有事件最终都被处理
        assertEquals(totalEvents, outputCount, "所有事件最终都应该被输出");
    }

    @Test
    @DisplayName("内存使用效率测试")
    void testMemoryEfficiency() throws Exception {
        long startTime = 0L;
        testHarness.setProcessingTime(startTime);

        // 发送大量事件测试内存使用
        int largeEventCount = 1000;

        for (int i = 1; i <= largeEventCount; i++) {
            testHarness.setProcessingTime(startTime + i * 5); // 每5ms一个事件
            Strategy strategy = createTestStrategy("memory-test-" + i);
            testHarness.processElement(new StreamRecord<>(strategy, startTime + i * 5));
        }

        // 处理所有缓存事件
        for (int i = 0; i < 200; i++) { // 足够的定时器触发次数
            testHarness.setProcessingTime(startTime + largeEventCount * 5 + i * 2000);
        }

        int outputCount = testHarness.getOutput().size();

        System.out.printf("内存效率测试 - 输入: %d, 输出: %d%n", largeEventCount, outputCount);

        // 验证所有事件最终都被处理（内存没有泄漏）
        assertEquals(largeEventCount, outputCount, "所有事件都应该被处理，不应该有内存泄漏");
    }

    /**
     * 生成真实的事件时间分布
     * 模拟实际业务中的不均匀流量模式
     */
    private List<Long> generateRealisticEventTimes(long startTime, long duration, int eventCount) {
        List<Long> times = new ArrayList<>();

        // 模拟不均匀分布：前半段时间事件较多，后半段较少
        for (int i = 0; i < eventCount; i++) {
            double progress = (double) i / eventCount;
            // 使用指数分布模拟真实流量
            double timeRatio = Math.pow(progress, 0.7); // 前期密集，后期稀疏
            long eventTime = startTime + (long) (timeRatio * duration);
            times.add(eventTime);
        }

        return times;
    }

    /**
     * 创建测试用的Strategy对象
     */
    private Strategy createTestStrategy(String id) {
        Strategy strategy = new Strategy();
        strategy.sceneIdentify = id;
        // 可以添加更多字段来模拟真实数据大小
        return strategy;
    }
}