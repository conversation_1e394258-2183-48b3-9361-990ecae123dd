package com.tencent.andata.smart.etl.process;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import java.util.Arrays;
import java.util.Collection;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

/**
 * Test class for StrategyAnalyzeLLMProcess (RichAsyncFunction).
 */
public class StrategyAnalyzeLLMProcessTest {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private StrategyAnalyzeLLMProcess asyncProcessorLLM;
    private ResultFuture<Strategy> resultFuture;

    @BeforeEach
    public void setUp() throws Exception {
        // Build the StrategyAnalyzeLLMProcess instance
        asyncProcessorLLM = StrategyAnalyzeLLMProcess
                .builder()
                .modelUrl("http://30.46.122.172:8080/gpt-api/api")
                .modelToken("2b663fe95b604593b74697950cac6c2e")
                .build();

        // Initialize the function
        asyncProcessorLLM.open(new Configuration());

        // Mock the ResultFuture
        resultFuture = mock(ResultFuture.class);
    }

    /**
     * Tests the asynchronous processing functionality.
     * Verifies that the LLM can be called correctly and a result is returned.
     */
    @Test
    public void testAsyncInvoke_Success() throws Exception {
        // Create test data
        Strategy strategy = buildTestStrategy();

        // Set analysis configurations
        strategy.analyzes = Arrays.asList(
                AnalyzeConfig.Professional_Skills,
                AnalyzeConfig.Service_Awareness,
                AnalyzeConfig.Communication_Skills,
                AnalyzeConfig.Service_Specifications).toArray(new Analyze[4]);

        // Call the asyncInvoke method
        asyncProcessorLLM.asyncInvoke(strategy, resultFuture);

        // Capture the argument of the collector
        ArgumentCaptor<Collection<Strategy>> captor = ArgumentCaptor.forClass(Collection.class);
        // Verify that complete is called within a timeout, as it's an async operation
        verify(resultFuture, timeout(3000000)).complete(captor.capture());

        // Verify the result
        Strategy result = captor.getValue().iterator().next();

        // Verify that each analyze has a result
        for (Analyze analyze : result.analyzes) {
            System.out.printf("TEST分析项 %s 的结果: %s%n", analyze.name, analyze.res);
        }
    }


    /**
     * Helper method to build a test Strategy object.
     */
    private static Strategy buildTestStrategy() {
        Strategy strategy = Strategy.builder()
                .id(7)
                .sceneIdentify("13912606")
                .name("测试策略")
                .scene(Scene.WebIM)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'webim'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(Chunk.builder()
                        .type(ChunkType.Conversation)
                        .maxSize(32 * 1024)
                        .delimiter("\n")
                        .conversationSpliceType(SpliceType.TicketQualityInspection)
                        .build())
                .build();

        // 设置触发时间戳
        strategy.trigger.setTriggerTimestamp(System.currentTimeMillis());

        // 设置对话内容
        strategy.chunk.conversation = "04-07 16:22 客户回复：你好\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）回复：您好\n" +
                "04-07 16:23 客户回复：我们有一个很重要的会议\n" +
                "04-07 16:23 客户回复：我现在进去了，结束不了，改不了时间，您有没有办法\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）回复：非常抱歉！根据您描述这边需要为您转接会话。可能需要排队请您耐心等待哦！\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）触发动作：转单，将会话转移给 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，很高兴为您服务，辛苦稍等，我看下您的问题\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：结束不了是指什么呢，您截图下我看下\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：结束不了提示您什么\n" +
                "04-07 16:35 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，还在线吗\n" +
                "04-07 16:44 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，由于长时间没有收到您的回复，我们将关闭本次会话，感谢您的咨询。为了给您提供更好的服务，我们诚挚地邀请您点击\"评价反馈\"或在稍后的弹窗中对本次服务做出评价，我们非常期待和重视您的满意度，9-10分代表着您的满意。祝您生活愉快、一切顺利！\n"
                +
                "04-07 16:44 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）触发动作：待客户确认结单\n" +
                "04-08 16:45 超过3天系统自动结单";

        return strategy;
    }

} 