package com.tencent.andata.smart.etl.process;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 限流效果验证测试
 * 用于验证修复后的限流器是否正常工作
 */
public class RateLimitingVerificationTest {

    @Test
    @DisplayName("验证限流效果 - 简单测试")
    void testSimpleRateLimiting() throws Exception {
        // 创建一个简单的限流器：2个事件/5秒，增加batchSize
        GenericSlidingWindowRateLimitingProcess<String> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(2, 5000, 1000, 2, String.class);

        KeyedOneInputStreamOperatorTestHarness<String, String, String> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        str -> "test-key",
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;

        // 快速发送5个事件
        for (int i = 1; i <= 5; i++) {
            testHarness.setProcessingTime(baseTime + i * 10); // 每10ms一个事件
            testHarness.processElement(new StreamRecord<>("event-" + i, baseTime + i * 10));
        }

        int immediateOutput = testHarness.getOutput().size();
        System.out.printf("立即输出: %d/5 个事件%n", immediateOutput);

        // 验证限流效果：5个快速事件中应该只有2个立即通过
        assertTrue(immediateOutput >= 1, "至少应该有1个事件立即通过");
        assertTrue(immediateOutput <= 2, String.format("5个快速事件应该只有2个以内立即通过，实际: %d", immediateOutput));

        // 推进时间，让定时器处理缓存事件（增加触发次数）
        for (int i = 1; i <= 20; i++) {
            testHarness.setProcessingTime(baseTime + 50 + i * 1000);
        }

        int finalOutput = testHarness.getOutput().size();
        System.out.printf("最终输出: %d/5 个事件%n", finalOutput);

        // 验证所有事件最终都被处理
        assertEquals(5, finalOutput, "所有5个事件最终都应该被输出");

        System.out.println("✅ 简单限流效果验证通过！");
    }

    @Test
    @DisplayName("验证严格限流 - 1个事件/5秒")
    void testStrictRateLimit() throws Exception {
        // 严格限流：1个事件/5秒，增加batchSize
        GenericSlidingWindowRateLimitingProcess<String> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(1, 5000, 1000, 3, String.class);

        KeyedOneInputStreamOperatorTestHarness<String, String, String> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        str -> "test-key",
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;

        // 快速发送10个事件
        for (int i = 1; i <= 10; i++) {
            testHarness.setProcessingTime(baseTime + i * 5); // 每5ms一个事件
            testHarness.processElement(new StreamRecord<>("strict-event-" + i, baseTime + i * 5));
        }

        int immediateOutput = testHarness.getOutput().size();
        System.out.printf("严格限流测试 - 立即输出: %d/10 个事件%n", immediateOutput);

        // 验证严格限流：10个快速事件中应该只有1个立即通过
        assertEquals(1, immediateOutput, String.format("严格限流下，10个快速事件应该只有1个立即通过，实际: %d", immediateOutput));

        // 推进时间处理缓存事件（大幅增加触发次数）
        for (int i = 1; i <= 30; i++) {
            testHarness.setProcessingTime(baseTime + 50 + i * 1000);
        }

        int finalOutput = testHarness.getOutput().size();
        System.out.printf("严格限流测试 - 最终输出: %d/10 个事件%n", finalOutput);

        // 验证所有事件最终都被处理
        assertEquals(10, finalOutput, "所有10个事件最终都应该被输出");

        System.out.println("✅ 严格限流验证通过！");
    }

    @Test
    @DisplayName("验证突发流量控制")
    void testBurstTrafficControl() throws Exception {
        // 突发流量控制：3个事件/2秒，增加batchSize
        GenericSlidingWindowRateLimitingProcess<Integer> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(3, 2000, 500, 5, Integer.class);

        KeyedOneInputStreamOperatorTestHarness<String, Integer, Integer> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        num -> "test-key",
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;

        // 突发流量：在50ms内发送20个事件
        for (int i = 1; i <= 20; i++) {
            testHarness.setProcessingTime(baseTime + i * 2); // 每2ms一个事件
            testHarness.processElement(new StreamRecord<>(i, baseTime + i * 2));
        }

        int burstOutput = testHarness.getOutput().size();
        System.out.printf("突发流量控制 - 立即输出: %d/20 个事件%n", burstOutput);

        // 验证突发流量被有效控制
        assertTrue(burstOutput <= 3, String.format("20个突发事件应该只有3个以内立即通过，实际: %d", burstOutput));

        // 推进时间处理缓存事件（增加触发次数和时间间隔）
        for (int i = 1; i <= 40; i++) {
            testHarness.setProcessingTime(baseTime + 40 + i * 500);
        }

        int finalOutput = testHarness.getOutput().size();
        System.out.printf("突发流量控制 - 最终输出: %d/20 个事件%n", finalOutput);

        // 验证所有事件最终都被处理
        assertEquals(20, finalOutput, "所有20个事件最终都应该被输出");

        System.out.println("✅ 突发流量控制验证通过！");
    }

    /**
     * 创建测试用的Strategy对象
     */
    private Strategy createTestStrategy(String id) {
        Strategy strategy = new Strategy();
        strategy.sceneIdentify = id;
        return strategy;
    }
}