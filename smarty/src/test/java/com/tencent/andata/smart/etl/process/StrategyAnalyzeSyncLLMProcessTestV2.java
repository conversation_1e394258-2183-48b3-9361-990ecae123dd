package com.tencent.andata.smart.etl.process;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import java.util.Arrays;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.util.Collector;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

public class StrategyAnalyzeSyncLLMProcessTestV2 {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private StrategyAnalyzeSyncLLMProcess syncProcessorLLM;
    private Collector<Strategy> collector;

    /**
     * 构建测试用的Strategy对象
     */
    private static Strategy buildTestStrategy() {
        Strategy strategy = Strategy.builder()
                .id(7)
                .sceneIdentify("13912606")
                .name("测试策略")
                .scene(Scene.WebIM)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'webim'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(Chunk.builder()
                        .type(ChunkType.Conversation)
                        .maxSize(32 * 1024)
                        .delimiter("\n")
                        .conversationSpliceType(SpliceType.TicketQualityInspection)
                        .build())
                .build();

        // 设置触发时间戳
        strategy.trigger.setTriggerTimestamp(System.currentTimeMillis());

        // 设置对话内容
        strategy.chunk.conversation = "04-07 16:22 客户回复：你好\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）回复：您好\n" +
                "04-07 16:23 客户回复：我们有一个很重要的会议\n" +
                "04-07 16:23 客户回复：我现在进去了，结束不了，改不了时间，您有没有办法\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）回复：非常抱歉！根据您描述这边需要为您转接会话。可能需要排队请您耐心等待哦！\n" +
                "04-07 16:23 坐席 一线 王哲（维音） （在线-腾讯会议购买咨询）触发动作：转单，将会话转移给 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，很高兴为您服务，辛苦稍等，我看下您的问题\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：结束不了是指什么呢，您截图下我看下\n" +
                "04-07 16:24 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：结束不了提示您什么\n" +
                "04-07 16:35 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，还在线吗\n" +
                "04-07 16:44 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）回复：您好，由于长时间没有收到您的回复，我们将关闭本次会话，感谢您的咨询。为了给您提供更好的服务，我们诚挚地邀请您点击\"评价反馈\"或在稍后的弹窗中对本次服务做出评价，我们非常期待和重视您的满意度，9-10分代表着您的满意。祝您生活愉快、一切顺利！\n"
                +
                "04-07 16:44 坐席 一线 齐强v_pqqiangqi（金道） （在线-办公协同队列）触发动作：待客户确认结单\n" +
                "04-08 16:45 超过3天系统自动结单";

        return strategy;
    }

    /**
     * 构建TDSQL测试用的Strategy对象
     */
    private static Strategy buildWebIMTestStrategy() {
        Strategy strategy = Strategy.builder()
                .id(5)
                .sceneIdentify("@TGS#25V3T3ARW")
                .name("WebIM结单质检")
                .scene(Scene.Ticket)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'webim'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(Chunk.builder()
                        .type(ChunkType.Conversation)
                        .maxSize(32 * 1024)
                        .delimiter("\n")
                        .conversationSpliceType(SpliceType.WebIMQualityInspection)
                        .build())
                .build();

        // 设置触发时间戳
        strategy.trigger.setTriggerTimestamp(System.currentTimeMillis());

        // 设置工单内容
        strategy.chunk.conversation = "06-17 11:02 客户回复：售后\n" +
                "06-17 11:02 坐席 一线 刘鹏（维音） （在线-腾讯会议购买咨询）回复：您好，在的\n" +
                "06-17 11:03 客户回复：自动续费扣款后能退吗？\n" +
                "06-17 11:03 坐席 一线 刘鹏（维音） （在线-腾讯会议购买咨询）回复：非常抱歉!根据您描述这边需要为您转接会话。可能需要排队请您耐心等待哦!\n" +
                "06-17 11:03 坐席 一线 刘鹏（维音） （在线-腾讯会议购买咨询）触发动作：转单，将会话转移给 坐席 一线 秦金（金道） （在线-腾讯会议购买咨询）\n" +
                "06-17 11:03 客户回复：昨天刚扣的款\n" +
                "06-17 11:04 客户回复：好\n" +
                "06-17 11:04 坐席 一线 秦金（金道） （在线-办公协同队列）回复：正在为您核实，请稍等\n" +
                "06-17 11:08 坐席 一线 秦金（金道） （在线-办公协同队列）触发动作：转单，将会话转移给 坐席 一线 v_pkanhuang （在线-办公协同队列）\n" +
                "06-17 11:08 坐席 一线 v_pkanhuang （官网产品试用审核队列）回复：请提供您的注册手机号\n" +
                "06-17 13:27 坐席 一线 v_pkanhuang （官网产品试用审核队列）回复：关于您反馈的问题，您于（2025-06-16）购买了（腾讯会议专业版连续包月）服务。此单已为您特殊申请退款1次，合计费用（88）元。退款金额按原支付路径返回按原支付路径返回，预计5-7个工作日到账，请您查收。取消连续包月路径：在微信中点击【钱包】-&gt;【支付设置】-&gt;【自动续费】-&gt;选择【腾讯会议】相关自动续费服务-&gt;"
                +
                "点击【关闭自动续费】即可。注意：在退款期间此订单请勿开发票，也请勿使用会员权益。\n" +
                "06-17 13:46 客户回复：13994539921\n" +
                "06-17 15:43 坐席 一线 v_pkanhuang （官网产品试用审核队列）触发动作：待客户确认结单";

        return strategy;
    }

    @Before
    public void setUp() throws Exception {
        // 构建StrategyAnalyzeSyncLLMProcess实例
        syncProcessorLLM = StrategyAnalyzeSyncLLMProcess
                .builder()
                .modelUrl("http://30.46.122.172:8080/gpt-api/api")
                .modelToken("2b663fe95b604593b74697950cac6c2e")
                .build();

        // 初始化处理函数
        syncProcessorLLM.open(new Configuration());

        // Mock收集器
        collector = mock(Collector.class);
    }

    /**
     * 测试同步处理功能
     * 验证是否能正确调用LLM并得到结果
     */
    @Test
    public void testProcessElement() {
        // 创建测试数据
        Strategy strategy = buildWebIMTestStrategy();

        // 设置分析配置
        strategy.analyzes = Arrays.asList(
                AnalyzeConfig.Professional_Skills,
                AnalyzeConfig.Service_Awareness,
                AnalyzeConfig.Communication_Skills,
                AnalyzeConfig.Service_Specifications).toArray(new Analyze[4]);

        // 调用processElement方法
        syncProcessorLLM.processElement(
                strategy,
                mock(StrategyAnalyzeSyncLLMProcess.Context.class),
                collector
        );

        // 捕获收集器的参数
        ArgumentCaptor<Strategy> captor = ArgumentCaptor.forClass(Strategy.class);
        verify(collector).collect(captor.capture());

        // 验证结果
        Strategy result = captor.getValue();

        // 验证每个analyze都有结果
        for (Analyze analyze : result.analyzes) {

            System.out.printf("分析项 %s 的结果: %s%n",
                    analyze.name, analyze.res);
        }
    }
}