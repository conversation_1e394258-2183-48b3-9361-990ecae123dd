package com.tencent.andata.smart.etl.process;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import java.nio.file.Files;
import java.nio.file.Paths;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.util.Collector;
import org.apache.flink.types.Row;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.util.List;

public class QualityStrategyToRowProcessTest {

    private static RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private QualityStrategyToRowProcess quaStrategyToRowProcess;
    private Collector<Row> collector;


    @Before
    public void setup() throws Exception {
        // 初始化处理函数
        quaStrategyToRowProcess = new QualityStrategyToRowProcess(rainbowUtils);
        quaStrategyToRowProcess.open(new Configuration());
        // Mock收集器
        collector = mock(Collector.class);
    }

    @Test
    public void testProcessElement() throws Exception {
        // 创建测试数据
        Strategy strategy = getQualityStrategy();
        quaStrategyToRowProcess.processElement(strategy, mock(QualityStrategyToRowProcess.Context.class), collector);

        // 捕获收集器的参数
        ArgumentCaptor<Row> captor = ArgumentCaptor.forClass(Row.class);

        // 验证调用次数与分析结果数量一致
        verify(collector, times(strategy.getAnalyzes().length)).collect(captor.capture());

        // 验证结果
        List<Row> rows = captor.getAllValues();
        rows.forEach(System.out::println);
    }

    private static Strategy getQualityStrategy() throws Exception {
        String filePath = "src/main/resources/qualityResult.txt"; // 替换为你的文件路径
        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
        // 将json转为List<JsonNode>
        return OBJECT_MAPPER.readValue(ticketOperationStr, new TypeReference<Strategy>() {});
    }
}