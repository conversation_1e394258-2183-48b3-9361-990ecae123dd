package com.tencent.andata.smart.etl.process;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * SlidingWindowRateLimitingProcess 优化效果验证测试
 */
public class SlidingWindowRateLimitingProcessTest {

    private KeyedOneInputStreamOperatorTestHarness<String, Strategy, Strategy> testHarness;
    private SlidingWindowRateLimitingProcess rateLimitingProcess;

    @BeforeEach
    void setUp() throws Exception {
        // QPM=30的参数设置：5个事件/10秒，每2秒检查，增加batchSize
        rateLimitingProcess = new SlidingWindowRateLimitingProcess(5, 10000, 2000, 3);

        testHarness = ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                rateLimitingProcess,
                (Strategy strategy) -> strategy.sceneIdentify,
                TypeInformation.of(String.class)
        );

        testHarness.open();
    }

    @Test
    @DisplayName("测试滑动窗口的精确性")
    void testSlidingWindowAccuracy() throws Exception {
        // 测试场景：在10秒窗口内发送6个事件，验证第6个事件被限流

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 快速发送6个事件来触发限流
        for (int i = 1; i <= 6; i++) {
            testHarness.setProcessingTime(baseTime + i * 100); // 每100ms一个事件
            Strategy strategy = createTestStrategy("event");
            testHarness.processElement(new StreamRecord<>(strategy, baseTime + i * 100));
        }

        // 验证有事件被限流
        int immediateOutput = testHarness.getOutput().size();
        assertTrue(immediateOutput <= 5, "应该有事件被限流，立即输出不应超过5个");
        assertTrue(immediateOutput >= 1, "至少应该有1个事件立即输出");

        // 推进时间，让定时器处理缓存事件
        for (int i = 1; i <= 20; i++) {
            testHarness.setProcessingTime(baseTime + 600 + i * 2000);
        }

        // 验证所有事件最终都被输出
        assertEquals(6, testHarness.getOutput().size(), "所有6个事件最终都应该被输出");
    }

    @Test
    @DisplayName("测试限流状态的稳定性")
    void testRateLimitingStability() throws Exception {
        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 快速发送10个事件，触发限流
        for (int i = 1; i <= 10; i++) {
            testHarness.setProcessingTime(baseTime + i * 100); // 每100ms一个事件
            Strategy strategy = createTestStrategy("burst-event");
            testHarness.processElement(new StreamRecord<>(strategy, baseTime + i * 100));
        }

        int initialOutputCount = testHarness.getOutput().size();
        assertTrue(initialOutputCount <= 5, "应该有事件被限流");

        // 推进时间，让定时器处理缓存事件
        for (int i = 1; i <= 20; i++) {
            testHarness.setProcessingTime(baseTime + 1000 + i * 2000);
        }

        // 验证所有事件最终都被输出
        assertEquals(10, testHarness.getOutput().size(), "所有事件最终都应该被输出");
    }

    @Test
    @DisplayName("测试状态清理的完整性")
    void testStateCleanup() throws Exception {
        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 发送一些事件
        for (int i = 1; i <= 3; i++) {
            testHarness.setProcessingTime(baseTime + i * 1000);
            Strategy strategy = createTestStrategy("cleanup-event");
            testHarness.processElement(new StreamRecord<>(strategy, baseTime + i * 1000));
        }

        // 推进时间到很久以后（超过窗口大小的2倍）
        testHarness.setProcessingTime(baseTime + 25000); // 25秒后

        // 发送新事件，验证状态已被清理
        testHarness.setProcessingTime(baseTime + 26000);
        Strategy newStrategy = createTestStrategy("new-event");
        testHarness.processElement(new StreamRecord<>(newStrategy, baseTime + 26000));

        // 验证新事件正常处理（说明状态已清理）
        assertTrue(testHarness.getOutput().size() >= 4, "新事件应该正常处理");
    }

    @Test
    @DisplayName("测试QPM=30的限流效果")
    void testQPMLimit() throws Exception {
        // 模拟1分钟内发送40个事件，验证只有30个通过
        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        int totalEvents = 40;
        int expectedPassedEvents = 30; // QPM=30

        // 快速发送40个事件来测试限流
        for (int i = 1; i <= totalEvents; i++) {
            testHarness.setProcessingTime(baseTime + i * 50L); // 每50ms一个事件
            Strategy strategy = createTestStrategy("qpm-event");
            testHarness.processElement(new StreamRecord<>(strategy, baseTime + i * 50L));
        }

        // 验证输出事件数量不超过QPM限制
        int actualOutputCount = testHarness.getOutput().size();
        assertTrue(actualOutputCount <= expectedPassedEvents + 5, // 允许5个事件的误差
                String.format("输出事件数量(%d)应该接近QPM限制(%d)", actualOutputCount, expectedPassedEvents));

        // 推进时间，让所有定时器都触发
        for (int i = 1; i <= 50; i++) {
            testHarness.setProcessingTime(baseTime + 2000 + i * 2000);
        }

        int finalOutputCount = testHarness.getOutput().size();
        assertEquals(totalEvents, finalOutputCount, "最终所有事件最终都应该被输出");
    }

    @Test
    @DisplayName("测试缓存事件的批量输出")
    void testBufferedEventOutput() throws Exception {
        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 快速发送大量事件，触发缓存
        for (int i = 1; i <= 15; i++) {
            testHarness.setProcessingTime(baseTime + i * 50); // 每50ms一个事件
            Strategy strategy = createTestStrategy("buffered-event");
            testHarness.processElement(new StreamRecord<>(strategy, baseTime + i * 50L));
        }

        int initialOutputCount = testHarness.getOutput().size();

        // 推进时间，触发多次定时器
        for (int i = 1; i <= 20; i++) {
            testHarness.setProcessingTime(baseTime + 750 + i * 2000);
        }

        // 验证缓存事件被批量输出
        int finalOutputCount = testHarness.getOutput().size();
        assertTrue(finalOutputCount > initialOutputCount, "缓存事件应该被批量输出");
        assertEquals(15, finalOutputCount, "所有事件最终都应该被输出");
    }

    /**
     * 创建测试用的Strategy对象
     */
    private Strategy createTestStrategy(String id) {
        Strategy strategy = new Strategy();
        strategy.sceneIdentify = id;
        // 设置其他必要字段...
        return strategy;
    }
}