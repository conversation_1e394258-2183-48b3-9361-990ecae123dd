package com.tencent.andata.smart.etl.process;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * GenericSlidingWindowRateLimitingProcess 泛型限流器测试
 * 测试不同数据类型的限流效果
 */
public class GenericSlidingWindowRateLimitingProcessTest {

    @Test
    @DisplayName("测试Strategy类型的泛型限流")
    void testStrategyTypeGeneric() throws Exception {
        // 使用Strategy类型的泛型限流器，增加batchSize
        GenericSlidingWindowRateLimitingProcess<Strategy> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(5, 10000, 2000, 3, Strategy.class);

        KeyedOneInputStreamOperatorTestHarness<String, Strategy, Strategy> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        strategy -> strategy.sceneIdentify,
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 快速发送6个Strategy事件
        for (int i = 1; i <= 6; i++) {
            testHarness.setProcessingTime(baseTime + i * 100);
            Strategy strategy = createTestStrategy("strategy-event-");
            testHarness.processElement(new StreamRecord<>(strategy, baseTime + i * 100));
        }

        int immediateOutput = testHarness.getOutput().size();
        assertTrue(immediateOutput <= 5, "Strategy类型限流器应该限流超出阈值的事件");

        // 推进时间处理缓存事件
        for (int i = 1; i <= 20; i++) {
            testHarness.setProcessingTime(baseTime + 600 + i * 2000);
        }

        // 验证所有事件最终都被输出
        assertEquals(6, testHarness.getOutput().size(), "所有Strategy事件最终都应该被输出");
    }

    @Test
    @DisplayName("测试String类型的泛型限流")
    void testStringTypeGeneric() throws Exception {
        // 使用String类型的泛型限流器
        GenericSlidingWindowRateLimitingProcess<String> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(3, 5000, 1000, 1, String.class);

        KeyedOneInputStreamOperatorTestHarness<String, String, String> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        str -> "test-key",
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 发送3个String事件
        for (int i = 1; i <= 3; i++) {
            testHarness.processElement("string-event-" + i, baseTime + i * 500);
        }

        // 验证前3个事件正常输出
        assertEquals(3, testHarness.getOutput().size(), "String类型限流器应该正常工作");

        // 发送第4个事件（应该被限流）
        testHarness.processElement("string-event-4", baseTime + 2000);

        // 验证第4个事件被限流
        assertEquals(3, testHarness.getOutput().size(), "第4个String事件应该被限流");
    }

    @Test
    @DisplayName("测试自定义对象类型的泛型限流")
    void testCustomObjectTypeGeneric() throws Exception {
        // 使用自定义TestEvent类型的泛型限流器
        GenericSlidingWindowRateLimitingProcess<TestEvent> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(2, 3000, 500, 1, TestEvent.class);

        KeyedOneInputStreamOperatorTestHarness<String, TestEvent, TestEvent> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        event -> event.getId(),
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 发送2个TestEvent事件
        for (int i = 1; i <= 2; i++) {
            TestEvent event = new TestEvent("event-", "test-data-" + i);
            testHarness.processElement(event, baseTime + i * 100);
        }

        // 验证前2个事件正常输出
        assertEquals(2, testHarness.getOutput().size(), "TestEvent类型限流器应该正常工作");

        // 发送第3个事件（应该被限流）
        TestEvent event3 = new TestEvent("event-", "test-data-3");
        testHarness.processElement(event3, baseTime + 1500);

        // 验证第3个事件被限流
        assertEquals(2, testHarness.getOutput().size(), "第3个TestEvent事件应该被限流");
    }

    @Test
    @DisplayName("测试泛型限流器的缓存事件输出")
    void testGenericBufferedEventOutput() throws Exception {
        GenericSlidingWindowRateLimitingProcess<String> rateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(2, 5000, 1000, 1, String.class);

        KeyedOneInputStreamOperatorTestHarness<String, String, String> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        rateLimiter,
                        str -> "test-key",
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 快速发送5个事件，触发限流和缓存
        for (int i = 1; i <= 5; i++) {
            testHarness.processElement("cached-event-" + i, baseTime + i * 100);
        }

        int initialOutputCount = testHarness.getOutput().size();
        assertTrue(initialOutputCount < 5, "应该有事件被限流缓存");

        // 推进时间，触发定时器输出缓存事件
        for (int i = 1; i <= 10; i++) {
            testHarness.setProcessingTime(baseTime + 1000 * i);
        }

        // 验证所有事件最终都被输出
        assertEquals(5, testHarness.getOutput().size(), "所有缓存事件最终都应该被输出");
    }

    @Test
    @DisplayName("测试泛型限流器的向后兼容性")
    void testBackwardCompatibility() throws Exception {
        // 测试原来的SlidingWindowRateLimitingProcess是否仍然工作
        SlidingWindowRateLimitingProcess oldRateLimiter =
                new SlidingWindowRateLimitingProcess(3, 5000, 1000, 1);

        KeyedOneInputStreamOperatorTestHarness<String, Strategy, Strategy> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        oldRateLimiter,
                        strategy -> strategy.sceneIdentify,
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 发送3个Strategy事件
        for (int i = 1; i <= 3; i++) {
            Strategy strategy = createTestStrategy("compat-event-");
            testHarness.processElement(strategy, baseTime + i * 500);
        }

        // 验证向后兼容性
        assertEquals(3, testHarness.getOutput().size(), "原来的SlidingWindowRateLimitingProcess应该仍然工作");
    }

    @Test
    @DisplayName("测试不同类型的QPM限流效果")
    void testDifferentTypesQPMLimit() throws Exception {
        // 测试Integer类型的QPM=6限流（每10秒1个事件）
        GenericSlidingWindowRateLimitingProcess<Integer> intRateLimiter =
                new GenericSlidingWindowRateLimitingProcess<>(1, 10000, 2000, 1, Integer.class);

        KeyedOneInputStreamOperatorTestHarness<String, Integer, Integer> testHarness =
                ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                        intRateLimiter,
                        num -> "int-key",
                        TypeInformation.of(String.class)
                );

        testHarness.open();

        long baseTime = 0L;
        testHarness.setProcessingTime(baseTime);

        // 在1分钟内发送10个Integer事件
        for (int i = 1; i <= 10; i++) {
            testHarness.processElement(i * 100, baseTime + i * 6000); // 每6秒一个
            testHarness.setProcessingTime(baseTime + i * 6000);
        }

        // 推进时间处理缓存事件
        // for (int i = 1; i <= 10; i++) {
        //    testHarness.setProcessingTime(baseTime + 60000 + i * 2000);
        // }

        int outputCount = testHarness.getOutput().size();
        // QPM=6，1分钟内应该输出6个事件
        assertTrue(outputCount >= 5 && outputCount <= 7,
                String.format("Integer类型QPM=6限流，输出应该在5-7之间，实际: %d", outputCount));
    }

    /**
     * 创建测试用的Strategy对象
     */
    private Strategy createTestStrategy(String id) {
        Strategy strategy = new Strategy();
        strategy.sceneIdentify = id;
        return strategy;
    }

    /**
     * 测试用的自定义事件类
     */
    public static class TestEvent {

        private String id;
        private String data;

        public TestEvent() {} // Jackson需要无参构造函数

        public TestEvent(String id, String data) {
            this.id = id;
            this.data = data;
        }

        public String getId() {return id;}

        public void setId(String id) {this.id = id;}

        public String getData() {return data;}

        public void setData(String data) {this.data = data;}

        @Override
        public String toString() {
            return "TestEvent{id='" + id + "', data='" + data + "'}";
        }
    }
}