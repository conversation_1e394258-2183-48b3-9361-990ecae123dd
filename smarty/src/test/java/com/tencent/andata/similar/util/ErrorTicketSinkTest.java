package com.tencent.andata.similar.util;

import com.tencent.andata.smart.similar.model.ErrorTicket;
import com.tencent.andata.smart.similar.util.ErrorTicketSink;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;

/**
 * ErrorTicketSink 测试类
 * 注意：这些测试需要实际的数据库连接，正常情况下应该使用 @Disabled 注解禁用
 */
@Disabled("需要实际数据库连接，仅用于本地测试")
public class ErrorTicketSinkTest {

    private ErrorTicketSink errorTicketSink;

    @BeforeEach
    void setUp() {
        // 初始化 ErrorTicketSink
        errorTicketSink = new ErrorTicketSink(
                "******************************************",
                "andon_aigc",
                "and*57b0NAi8kgc3",
                3  // 小批次便于测试
        );
    }

    @AfterEach
    void tearDown() {
        if (errorTicketSink != null) {
            errorTicketSink.shutdown();
        }
    }

    @Test
    void testWriteSingleErrorTicket() throws InterruptedException {
        // 创建测试错误票据
        ErrorTicket errorTicket = new ErrorTicket("TEST123", "测试错误消息", 999);

        // 异步写入
        errorTicketSink.writeAsync(errorTicket);

        // 等待一段时间确保写入完成
        Thread.sleep(2000);

        System.out.println("统计信息: " + errorTicketSink.getStatistics());
    }

    @Test
    void testWriteMultipleErrorTickets() throws InterruptedException {
        // 创建多个测试错误票据
        for (int i = 1; i <= 10; i++) {
            ErrorTicket errorTicket = new ErrorTicket(
                    "TEST" + i,
                    "批量测试错误消息 " + i,
                    999 + i
            );
            errorTicketSink.writeAsync(errorTicket);
        }

        // 等待一段时间确保写入完成
        Thread.sleep(5000);

        System.out.println("统计信息: " + errorTicketSink.getStatistics());
    }

    @Test
    void testInvalidErrorTicket() throws InterruptedException {
        // 测试无效的错误票据
        ErrorTicket invalidTicket1 = new ErrorTicket(null, "测试消息", 999);  // ticket_id 为 null
        ErrorTicket invalidTicket2 = new ErrorTicket("TEST456", "", 999);   // msg 为空
        ErrorTicket invalidTicket3 = new ErrorTicket("TEST789", "测试消息", null);  // status 为 null

        errorTicketSink.writeAsync(invalidTicket1);
        errorTicketSink.writeAsync(invalidTicket2);
        errorTicketSink.writeAsync(invalidTicket3);

        // 等待一段时间
        Thread.sleep(2000);

        System.out.println("统计信息: " + errorTicketSink.getStatistics());
    }

    @Test
    void testBatchFlush() throws InterruptedException {
        // 测试批量刷新机制
        System.out.println("开始批量写入测试...");

        // 写入足够的数据触发批量刷新
        for (int i = 1; i <= 5; i++) {
            ErrorTicket errorTicket = new ErrorTicket(
                    "BATCH_TEST_" + i,
                    "批量刷新测试消息 " + i,
                    888
            );
            errorTicketSink.writeAsync(errorTicket);

            // 稍微延迟，观察批量处理
            Thread.sleep(100);
        }

        // 等待批量处理完成
        Thread.sleep(3000);

        System.out.println("统计信息: " + errorTicketSink.getStatistics());
    }

    /**
     * 手动测试方法，用于验证数据库连接和表结构
     */
    public static void main(String[] args) throws InterruptedException {
        System.out.println("开始 ErrorTicketSink 手动测试...");

        ErrorTicketSink sink = new ErrorTicketSink(
                "******************************************",
                "andon_aigc",
                "and*57b0NAi8kgc3",
                3
        );

        // 创建测试数据
        ErrorTicket testTicket = new ErrorTicket(
                "MANUAL_TEST_001",
                "手动测试错误消息，特征提取接口返回999错误",
                999
        );

        System.out.println("写入测试票据: " + testTicket);
        sink.writeAsync(testTicket);

        // 等待写入完成
        Thread.sleep(5000);

        System.out.println("最终统计: " + sink.getStatistics());

        // 关闭
        sink.shutdown();

        System.out.println("测试完成!");
    }
}