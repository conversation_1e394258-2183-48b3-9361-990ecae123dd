CREATE TABLE dwm_im_group_risk_ctrl
(
    value_of_primary_key       character varying(1024) NOT NULL,
    record_update_time         character varying(1024),
    risk_id                    character varying(1024),
    risk_content               text,
    risk_link                  character varying(1024),
    ticket_blong               character varying(1024),
    queue                      character varying(1024),
    agent_id                   character varying(1024),
    group_id                   character varying(1024),
    group_name                 character varying(1024),
    ltc_id                     character varying(1024),
    ltc_name                   character varying(1024),
    service_provider           character varying(1024),
    service_channel            character varying(1024),
    uin                        character varying(1024),
    cid                        character varying(1024),
    gid                        character varying(1024),
    gid_name                   character varying(1024),
    customer_name              character varying(1024),
    need_create_chat           integer,
    is_valid                   integer,
    risk_type_list             character varying(1024),
    risk_source                character varying(1024),
    risk_level                 character varying(1024),
    is_complaint               integer,
    is_success_complaint       integer,
    complain_type              character varying(1024),
    complain_condition         character varying(1024),
    is_claim                   character varying(1024),
    is_created_chat            integer,
    is_annotated               integer,
    annotate_user              character varying(1024),
    annotate_time              character varying(1024),
    time_delay                 integer,
    finally_payment            character varying(1024),
    alarm_time                 timestamp without time zone,
    push_time                  character varying(1024),
    push_model                 character varying(1024),
    recog_type                 character varying(1024),
    ticket_id                  character varying(1024),
    im_group_type              integer,
    alarm_person               text,
    owner                      character varying(1024),
    leader                     character varying(1024),
    short_url                  character varying(1024),
    status                     bigint,
    priority                   bigint,
    service_scene_id           bigint,
    owner_first_view_time      timestamp without time zone,
    leader_first_view_time     timestamp without time zone,
    first_response_css         character varying(1024),
    css_first_response_time    timestamp without time zone,
    css_first_response_content text
);


COMMENT ON TABLE dwm_im_group_risk_ctrl IS 'IM群风控统计表';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.value_of_primary_key IS '主键';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.record_update_time IS '数据上报时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.risk_id IS '风险id';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.risk_content IS '风险内容';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.risk_link IS '风险消息链接';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.ticket_blong IS '归属';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.queue IS '值班队列';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.agent_id IS '坐席id';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.group_id IS 'im群id';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.group_name IS 'im群名称';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.ltc_id IS '项目编号';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.ltc_name IS '项目名称';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.service_provider IS '服务商';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.service_channel IS '服务通道';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.uin IS '客户uin';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.cid IS '客户cid';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.gid IS '客户gid';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.gid_name IS '集团名称';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.customer_name IS '客户名称';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.need_create_chat IS '是否拉群告警';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.is_valid IS '是否有效';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.risk_type_list IS '风险类型列表';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.risk_source IS '风险来源';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.risk_level IS '风险级别';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.is_complaint IS '是否投诉';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.is_success_complaint IS '是否成功投诉';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.complain_type IS '投诉类型';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.complain_condition IS '投诉概况';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.is_claim IS '是否索赔';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.is_created_chat IS '是否已创建群';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.is_annotated IS '是否已标注';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.annotate_user IS '标注人';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.annotate_time IS '标注时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.time_delay IS '标注时长';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.finally_payment IS '最终赔付';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.alarm_time IS '告警时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.push_time IS '系统推送时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.push_model IS '命中模型';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.recog_type IS '识别方式';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.ticket_id IS '工单id';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.im_group_type IS '群类型:1-大客户群;2-私有云客户群群;3-私有云一事一群;4-微信客户群';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.alarm_person IS '被告警人';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.owner IS 'owner';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.leader IS 'leader';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.short_url IS '短链接';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.status IS '工单状态';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.priority IS '工单优先级';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.service_scene_id IS '服务场景id';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.owner_first_view_time IS 'owner首次查看时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.leader_first_view_time IS 'leader首次查看时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.first_response_css IS '首次响应客服';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.css_first_response_time IS '客服首次响应时间';
COMMENT ON COLUMN dwm_im_group_risk_ctrl.css_first_response_content IS '客服首次响应内容';

ALTER TABLE dwm_im_group_risk_ctrl ADD CONSTRAINT dwm_im_group_risk_ctrl_pkey
    PRIMARY KEY (value_of_primary_key);