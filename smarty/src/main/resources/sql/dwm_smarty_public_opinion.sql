create table dwm_smarty_public_opinion(
      value_of_primary_key        text primary key,
      record_update_time          timestamp,
      risk_content                text,
      risk_link                   text,
      service_provider            text,
      service_channel             text,
      uin                         text,
      customer_name               text,
      need_create_chat            INT ,
      is_valid                    INT ,
      risk_type_list              text,
      risk_source                 text,
      risk_level                  text,
      is_complaint                INT,
      is_success_complaint        INT,
      complain_type               text,
      complain_condition          text,
      is_claim                    text,
      is_created_chat             INT ,
      is_annotated                INT ,
      annotate_user               text,
      annotate_time               timestamp,
      finally_payment             text,
      alarm_time                  TIMESTAMP ,
      push_model                  text,
      recog_type                  text,
      ticket_id                   text,
      opinion_type                INT,
      short_url                   text,
      is_alarmed	              INT ,
      keywords	                  text,
      is_semantic_hit	            INT ,
      send_time	                    TIMESTAMP ,
      conversation_id	            text,
      need_feature	                INT
);

alter table dwm_smarty_public_opinion
    add column request_uid text;

-- rt
SET
`supersql.bypass.forceAll`=true;
SET
`supersql.domain.mapping.jdbc.serverType`=livy;
SET
`supersql.datasource.default`=hive_online_internal;

ALTER TABLE andata_rt.dwm_smarty_public_opinion
    ADD COLUMNS (
    role                          STRING COMMENT'命中角色'
);


-- sr & pg
ALTER TABLE dwm_smarty_public_opinion
    ADD COLUMN role text;