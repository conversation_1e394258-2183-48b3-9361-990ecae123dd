CREATE TABLE ticket_priority_agent_judge (
    pk varchar(1024) PRIMARY KEY NOT NULL,
    scene varchar(1024),
    scene_identify varchar(1024),
    ticket_id bigint,
    service_scene_level1_name varchar(1024),
    service_scene_level2_name varchar(1024),
    service_scene_level3_name varchar(1024),
    service_scene_level4_name varchar(1024),
    service_channel varchar(1024),
    current_operator varchar(1024),
    customer_name varchar(1024),
    fact_assign varchar(1024),
    post varchar(1024),
    create_time timestamp without time zone,
    status varchar(1024),
    url varchar(1024),
    responsible varchar(1024),
    priority varchar(1024),
    title varchar(1024),
    question varchar(1024),
    trigger_time timestamp without time zone,
    duty_responsible varchar(1024),
    uin bigint,
    operation_id varchar(1024),
    problem_summary varchar(1024),
    reflection_type_classify varchar(1024),
    is_consult varchar(1024),
    is_obvious varchar(1024),
    reason varchar(1024),
    try_to_recovered varchar(1024),
    is_mention varchar(1024),
    is_first_judge int,
    is_rise_up int,
    event_time bigint
);

COMMENT ON COLUMN ticket_priority_agent_judge.pk IS '主键ID';
COMMENT ON COLUMN ticket_priority_agent_judge.scene IS '工单来源';
COMMENT ON COLUMN ticket_priority_agent_judge.scene_identify IS '来源ID';
COMMENT ON COLUMN ticket_priority_agent_judge.ticket_id IS '工单ID';
COMMENT ON COLUMN ticket_priority_agent_judge.service_scene_level1_name IS '一级归档';
COMMENT ON COLUMN ticket_priority_agent_judge.service_scene_level2_name IS '二级归档';
COMMENT ON COLUMN ticket_priority_agent_judge.service_scene_level3_name IS '三级归档';
COMMENT ON COLUMN ticket_priority_agent_judge.service_scene_level4_name IS '四级归档';
COMMENT ON COLUMN ticket_priority_agent_judge.service_channel IS '服务通道';
COMMENT ON COLUMN ticket_priority_agent_judge.current_operator IS '当前处理人';
COMMENT ON COLUMN ticket_priority_agent_judge.customer_name IS '客户名称';
COMMENT ON COLUMN ticket_priority_agent_judge.fact_assign IS '工单经手人';
COMMENT ON COLUMN ticket_priority_agent_judge.post IS '岗位';
COMMENT ON COLUMN ticket_priority_agent_judge.create_time IS '工单创建时间';
COMMENT ON COLUMN ticket_priority_agent_judge.status IS '工单状态';
COMMENT ON COLUMN ticket_priority_agent_judge.url IS '工单URL';
COMMENT ON COLUMN ticket_priority_agent_judge.responsible IS '工单负责人';
COMMENT ON COLUMN ticket_priority_agent_judge.priority IS '工单优先级';
COMMENT ON COLUMN ticket_priority_agent_judge.title IS '工单标题';
COMMENT ON COLUMN ticket_priority_agent_judge.question IS '工单问题';
COMMENT ON COLUMN ticket_priority_agent_judge.trigger_time IS '工单触发时间';
COMMENT ON COLUMN ticket_priority_agent_judge.duty_responsible IS '工单对列负责人';
COMMENT ON COLUMN ticket_priority_agent_judge.uin IS '工单uin';
COMMENT ON COLUMN ticket_priority_agent_judge.operation_id IS '工单流水ID';
COMMENT ON COLUMN ticket_priority_agent_judge.problem_summary IS '问题总结';
COMMENT ON COLUMN ticket_priority_agent_judge.reflection_type_classify IS '当前等级';
COMMENT ON COLUMN ticket_priority_agent_judge.is_consult IS '是否冲突';
COMMENT ON COLUMN ticket_priority_agent_judge.is_obvious IS '是否惯性';
COMMENT ON COLUMN ticket_priority_agent_judge.reason IS '原因';
COMMENT ON COLUMN ticket_priority_agent_judge.try_to_recovered IS '尝试恢复';
COMMENT ON COLUMN ticket_priority_agent_judge.is_mention IS '是否提醒';
COMMENT ON COLUMN ticket_priority_agent_judge.is_first_judge IS '是否首次判断';
COMMENT ON COLUMN ticket_priority_agent_judge.is_rise_up IS '是否升级';
COMMENT ON COLUMN ticket_priority_agent_judge.event_time IS '触发时间';