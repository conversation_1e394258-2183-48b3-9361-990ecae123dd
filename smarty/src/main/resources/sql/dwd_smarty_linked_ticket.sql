create table dwd_smarty_linked_ticket
(
    value_of_primary_key text primary key,
    record_update_time   timestamp,
    content              text,
    risk_type            text,
    risk_level           text,
    group_name           text,
    uuid                 text,
    ticket_id            text,
    service_provider     text,
    customer             text,
    source               text,
    url                  text
);

create view dws_smarty_linked_ticket as
(
select value_of_primary_key,
       record_update_time,
       content,
       risk_type,
       risk_level,
       group_name,
       uuid,
       ticket_id,
       service_provider,
       customer,
       source,
       url,
       c as count,
        case
            when rn = 1 then 1
            else 0
        end as is_current
from (
    select *,
    row_number() over(partition by uuid order by record_update_time desc) as rn,
    count (*) over(partition by uuid) as c
    from dwd_smarty_linked_ticket
    ) t
    );