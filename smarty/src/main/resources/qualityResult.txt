{"id": 2, "name": "WebIM结单质检", "scene": "WebIM", "condition": {"expression": "data_type == 'webim' && regexMatch(source, '^(PRESALE|MC|MP|MA)$') && regexMatch(rpc_name, '^(AutoFinishConversation|FinishUserConversation|FinishConversation)$')"}, "trigger": {"type": "Immediately", "needPersist": true, "data": [{"action_type": "insert", "record_update_time": "2025-06-17 11:10:10", "requestid": "93dd467f-4b28-11f0-97eb-52540055b09d", "rpc_name": "AutoFinishConversation", "table": "ods_im_online_customer_service_backend_data", "value_of_primary_key": "1750129810190683436|@TGS#2MSRPTARK", "conversation_id": "@TGS#2MSRPTARK", "owner_uin": 0, "uin": 0, "uid": "", "source": "MP", "status": 12, "category_id": 991, "first_should_assign": 5531, "should_assign": 5669, "fact_assign": 5669, "service_scene": 39790, "current_staff": "82349", "staffs": "77492,82349", "appraise": "", "service_rate": 0, "unsatisfy_reason": 0, "conversation_service_rate": 0, "conversation_unsatisfy_reason": "", "product_rate": 0, "product_unsatisfy_reason": "", "recommend_score": -1, "appraise_time": 0, "create_time": "2025-06-16 10:46:34", "customer_updated_time": 1750042017, "staff_updated_time": 1750043356, "ticket_ids": "", "conversation_ticket_ids": "14719662", "title": "", "all_level_category": "{\"first_level\":{\"id\":985,\"name\":\"腾讯会议\",\"name_en\":\"Tencent Conference\",\"service_scene\":0},\"second_level\":{\"id\":986,\"name\":\"产品咨询\",\"name_en\":\"Product consultation\",\"service_scene\":0},\"third_level\":{\"id\":991,\"name\":\"产品咨询\",\"name_en\":\"Product consultation\",\"service_scene\":259673}}", "customer_first_updated_time": 1750042012, "staff_first_updated_time": 1750041999, "is_alarm": false, "solve_status": 0, "customer_name": "橙橙", "is_clean": 1, "customer_type": 0, "finish_time": 1750129810, "staff_title": "非常抱歉!根据您描述这边需要为您转接会话。&nbsp;可能需要排队请您耐心等待哦!", "is_transferred": true, "chat_type": 0, "company_id": 336, "is_ticket_created": true, "set_wait_status_time": 0, "apply_finish_time": 1750043364, "awaiting_supplement_time": 0, "creator": "16802000000037533320564", "creator_type": 1, "finisher": "system", "finish_type": 3, "post": 2, "parent": "", "contact": "xanejzOpCdUo9Fk9QERdqjSvg4YFz3SY8KMCUG_ZRuPaaBWWmeRSA7ZKEWcGm0oJRw==", "marked_by_staffs": "", "is_ola_alarm": false, "has_complaint": false, "sem_category_id": 0, "language": "zh", "ticket_id": 14719662, "customerName": "", "fact_assign_duty_name": "在线-办公协同队列", "current_operator": "耿育城（金道）", "responsible": "耿育城（金道）", "url": "https://andon.cloud.tencent.com/ticket/nologin/redirect?id=14719662&sign=bcff4c0db6838adb8e49ffa65253882c", "start_time": 1750041994000, "service_channel": 50, "end_time": 1750129810000, "ticket_status": 5, "question": "【发送者】:administrator 【时间】:Mon, 16 Jun 2025 10:46:34 CST\n", "priority": 2, "group_id": "", "name": "橙橙", "service_scene_level4_name": "无效会话", "service_scene_level2_name": "无效会话", "service_scene_level1_name": "无效会话", "service_scene_level3_name": "无效会话", "data_type": "webim", "display_content": "", "risk_type": ""}], "triggerTimestamp": 1750129810396}, "chunk": {"type": "Conversation", "value": null, "maxSize": 32768, "delimiter": "\n", "conversationSpliceType": "WebIMQualityInspection", "conversation": "06-16 10:46 客户回复：邀请同事参加线上面试\n06-16 10:46 客户回复：流程\n06-16 10:49 坐席 一线 朱妍（维音） （在线-腾讯会议购买咨询）回复：非常抱歉!根据您描述这边需要为您转接会话。可能需要排队请您耐心等待哦!\n06-16 10:49 坐席 一线 朱妍（维音） （在线-腾讯会议购买咨询）触发动作：转单，将会话转移给 坐席 一线 耿育城（金道） （在线-腾讯会议购买咨询）\n06-16 10:50 坐席 一线 耿育城（金道） （在线-办公协同队列）回复：您好很荣幸为您服务您的问题我们已经收到了这里先看下您的问题有结果第一时间反馈您\n06-16 10:51 坐席 一线 耿育城（金道） （在线-办公协同队列）回复：您好，您这边是要发起会议邀请他人加入会议吗\n06-16 10:57 坐席 一线 耿育城（金道） （在线-办公协同队列）回复：您好，还在线吗，还有需要帮助您的吗\n06-16 11:09 坐席 一线 耿育城（金道） （在线-办公协同队列）回复：您好，由于长时间没有收到您的回复，稍后我们将结束本次会话。邀请您点击“评价反馈”或在稍后的弹窗中对本次服务做出评价，9-10星代表满意。祝您生活愉快！\n06-16 11:09 坐席 一线 耿育城（金道） （在线-办公协同队列）触发动作：待客户确认结单", "operations": null, "aviatorExpressions": null}, "analyzes": [{"type": "Quality_Inspection", "name": "Professional_Skills", "desc": "专业技能", "prompt": "你是一位云计算售后服务质量检查员，请分析不同坐席与客户的对话，按以下步骤执行：\n1、总结对话中客户的问题与坐席提供的解决方案，客户的问题和坐席提供的解决方案要一一对应输出。\n2、针对总结的问题与解决方案判断坐席是否遗漏客户问题，具体细节如下：\n如果坐席没有为客户问题提供解答，也没有将无法解答的客户问题转交或升级到其他坐席进行处理，则质检不通过，反之质检通过。\n如果坐席为客户的问题提供了合理的解决方案，则视为坐席解答了客户的问题，质检通过。\n如果坐席在解决客户的问题的过程中，因为客户不回复坐席询问而导致坐席无法解决客户的问题，则这种情况可以不被认定为坐席遗漏了客户的问题，质检通过。\n如果坐席在解决客户的问题的过程中，因为客户结单而导致坐席无法解决客户的问题，则这种情况可以不被认定为坐席遗漏了客户的问题，质检通过。\n\n注意：\n根据以上质检项，以纯json格式分别输出针对不同坐席的分析过程与质检结果，分析过程需要输出具体的时间和内容，质检结果包括“通过”、“不通过”、“不涉及”，不要输出markdown格式。\n输出格式：\n[{\n    \"坐席\": \"xxx\",\n    \"问题\": \"1.xxx，2.xxx\",\n    \"解决方案\":\"1.xxx，2.xxx\",\n    \"是否遗漏客户问题\": {\n        \"分析\": \"xxx\",\n        \"结果\":\"通过/不通过/不涉及\"\n    }\n}]\n\n对话：\n%s", "res": "[\n    {\n        \"坐席\": \"朱妍（维音）\",\n        \"问题\": \"邀请同事参加线上面试流程\",\n        \"解决方案\": \"转单给耿育城（金道）\",\n        \"是否遗漏客户问题\": {\n            \"分析\": \"坐席朱妍（维音）将客户问题转单给耿育城（金道），未遗漏客户问题。\",\n            \"结果\": \"通过\"\n        }\n    },\n    {\n        \"坐席\": \"耿育城（金道）\",\n        \"问题\": \"邀请同事参加线上面试流程\",\n        \"解决方案\": \"坐席耿育城（金道）未提供有效解决方案，主动结束会话\",\n        \"是否遗漏客户问题\": {\n            \"分析\": \"坐席耿育城（金道）未提供有效解决方案，主动结束会话，导致客户问题未解决。\",\n            \"结果\": \"不通过\"\n        }\n    }\n]", "model": "quality_check_json_sft_professional_skills", "resType": "Json", "modelExtraParams": null, "messages": null}, {"type": "Quality_Inspection", "name": "Service_Awareness", "desc": "服务意识", "prompt": "你是一位腾讯云售后服务质量检查员，分析客户与坐席的对话并从以下3个质检项判断坐席质检是否通过。\n\n质检项1：请严格依据以下分类标准分析对话，判断坐席是否采取了安抚客户情绪的措施。当客户出现任一负面情形时，坐席须在当轮对话中采取了安抚客户情绪的措施方可通过。\n第1类，强烈负面情绪措辞：客户使用了明确的否定词汇表达愤怒/失望，不含辱骂成份，例如\"太烂了\"\"差劲\"\"简直是笑话\"\"完全没解决问题！\"，如果含有侮辱性/人身攻击的词汇归入第2类。注意：客户触发动作“投诉”或者客户的回复中明确表示要投诉的情况，需要在质检项2中进行检查。注意：如果客户的回复中仅包含问号\"?\"，则这种情形不属于需要被质检的负面情形。\n第2类，侮辱性/人身攻击词汇：客户使用了针对个人/组织的辱骂性词汇，例如\"骗子\"\"垃圾公司\"\"你们根本不懂技术\"。\n第3类，行政投诉威胁：客户提及向监管机构/媒体投诉，例如“再不解决就投诉到消协/媒体”\"准备曝光给电视台\"\"明天就向工信部投诉\"等。\n第4类，法律行动声明：客户表示将采取法律手段，例如“律师将联系你们”\"已联系律师处理\"\"法院见\"等。\n第5类，对腾讯云的产品和服务进行负面评价：客户的回复中使用明确的文字对产品、品牌、服务进行负面评价，例如“再也不会买你们的产品\"你们系统设计有缺陷\"\"这根本不是云服务该有的表现\"”“你们公司毫无信誉”“你们这是在欺骗客户”“如果你无法回答，就请你将这个问题转给能给出明确回复的人”等。注意，质检的时候需要严格区分客户是在“正常陈述客户的问题”还是“对产品、品牌、服务进行负面评价”。\n第6类，明确质疑/吐槽问题的处理进展：客户的回复中使用明确的文字对问题的处理进展表达质疑/吐槽，例如“都1天了，还没有解决么”等。注意：如果客户的回复中仅包含问号\"?\"，则这种情形不属于需要被质检的负面情形。\n第7类，终止合作意向：客户表示了终止合作的意向，例如\"立即终止所有合同\"\"请安排全额退款\"等。\n\n质检项2：客户触发动作“投诉”或者客户的回复中明确表示要投诉时，如果坐席向客户表示了歉意或者采取了安抚客户情绪的措施，则判定为坐席“通过”本质检项，反之则“不通过”。\n\n质检项3：客户要求电话沟通，如果坐席同意则质检“通过”，如果坐席拒绝或者忽视客户的电话沟通的请求，或者坐席要求客户通过腾讯云热线电话4009100100或95716进行电话沟通，则“不通过”本质检项。\n\n注意：\n根据以上质检项，以纯json格式分别输出针对不同坐席的分析过程与质检结果，分析过程需要输出具体的时间和内容，质检结果包括“通过”、“不通过”、“不涉及”，不要输出markdown格式。\n输出格式：\n[\n    {\n        \"坐席\": \"xxx\",\n        \"不满情绪处理\": {\n            \"分析\": \"[1]客户在xx时间回复：xx，属于第x类，xxx。坐席在xxx时间回复：xxx，是否采取了致歉或安抚客户情绪的措施，是否符合质检项要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"投诉后安抚\": {\n            \"分析\": \"[1]客户在xx时间触发动作“投诉”或者在回复中明确表示要投诉。坐席在xxx时间回复：xxx，是否采取了致歉或安抚客户情绪的措施，是否符合质检项要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"拒绝电话沟通\": {\n            \"分析\": \"[1]客户在xx时间要求电话沟通，坐席是否拒绝或者忽视客户的要求，是否符合质检项的要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        }\n    }\n]\n\n对话：\n%s", "res": "[\n    {\n        \"坐席\": \"朱妍（维音）\",\n        \"不满情绪处理\": {\n            \"分析\": \"客户未表达强烈不满。\",\n            \"结果\": \"不涉及\"\n        },\n        \"投诉后安抚\": {\n            \"分析\": \"客户未触发动作“投诉”或者在回复中明确表示要投诉。，不涉及质检项要求。\",\n            \"结果\": \"不涉及\"\n        },\n        \"拒绝电话沟通\": {\n            \"分析\": \"客户未要求电话沟通，不涉及质检项要求。\",\n            \"结果\": \"不涉及\"\n        }\n    },\n    {\n        \"坐席\": \"耿育城（金道）\",\n        \"不满情绪处理\": {\n            \"分析\": \"客户未表达强烈不满。\",\n            \"结果\": \"不涉及\"\n        },\n        \"投诉后安抚\": {\n            \"分析\": \"客户未触发动作“投诉”或者在回复中明确表示要投诉，不涉及质检项要求。\",\n            \"结果\": \"不涉及\"\n        },\n        \"拒绝电话沟通\": {\n            \"分析\": \"客户未要求电话沟通，不涉及质检项要求。\",\n            \"结果\": \"不涉及\"\n        }\n    }\n]", "model": "quality_check_json_sft_service_awareness", "resType": "Json", "modelExtraParams": null, "messages": null}, {"type": "Quality_Inspection", "name": "Communication_Skills", "desc": "沟通技巧", "prompt": "你是一位云计算售后服务质量检查员，分析客户与坐席的对话并从以下两个方面判断坐席质检是否通过，\n质检项1：礼貌友善。如果坐席的回复中是否存在侮辱性词汇、贬低性表述、嘲讽语气、质问语气等不礼貌内容。如发现类似'你是听不懂吗？'、'你自己不会看说明吗？'等表述，则视为不通过。\n质检项2：使用3次相同话术。如果坐席3次使用完全相同的文字回复客户，则视为不通过。\n本质检项需注意：如果文字的语义是以下三种情况，则视为通过：\n1、坐席询问客户问题是否已经解决，例如“您的问题已经解决了么”、“您还有其他问题么”；\n2、坐席邀请客户对本次服务进行评价，例如“麻烦结束后对我的服务评分”、“请为我的服务做出评价”；\n3、坐席回复客户问题正在核实/处理需要客户等待，例如“正在为您核实请稍等”、“请稍等”。\n\n注意：\n根据以上质检项，以纯json格式分别输出针对不同坐席的分析过程与质检结果，质检结果包括“通过”、“不通过”，不要输出markdown格式。\n# 输出格式：\n[\n    {\n        \"坐席\": \"xxx\",\n        \"礼貌友善\": {\n            \"分析\": \"xxx\",\n            \"结果\": \"通过/不通过\"\n        },\n        \"使用3次相同话术\": {\n            \"分析\": \"坐席是否连续3次使用完全相同的一段话术（文字）来解答客户的问题。如果是，坐席在xx时间、xx时间、xx时间连续3次使用完全相同的一段话术（文字）来解答客户的问题，文字内容为“xxx”\",\n            \"结果\": \"通过/不通过\"\n        }\n    }\n]\n\n对话：\n%s", "res": "[\n{\n\"坐席\": \"朱妍（维音）\",\n\"礼貌友善\": {\n\"分析\": \"坐席的回复中没有侮辱性词汇、贬低性表述、嘲讽语气、质问语气等不礼貌内容。\",\n\"结果\": \"通过\"\n},\n\"使用3次相同话术\": {\n\"分析\": \"坐席仅回复一次，未达到3次相同话术的标准。\",\n\"结果\": \"通过\"\n}\n},\n{\n\"坐席\": \"耿育城（金道）\",\n\"礼貌友善\": {\n\"分析\": \"坐席的回复中没有侮辱性词汇、贬低性表述、嘲讽语气、质问语气等不礼貌内容。\",\n\"结果\": \"通过\"\n},\n\"使用3次相同话术\": {\n\"分析\": \"坐席未3次使用完全相同的文字回复客户，且其回复内容不属于三种例外情况。\",\n\"结果\": \"通过\"\n}\n}\n]", "model": "quality_check_json_sft_communication_skills", "resType": "Json", "modelExtraParams": null, "messages": null}, {"type": "Quality_Inspection", "name": "Service_Timeliness", "desc": "服务时效", "prompt": "你是一位云计算售后服务质量检查员，分析客户与坐席的对话并从以下6个方面判断坐席质检是否通过，\n质检项1：仅针对对话中的第1位坐席进行质检，检查坐席是否在3分钟内及时回复客户的第1条回复，如果对话中出现的第1位坐席不回复客户而将会话转移给其他坐席，则判定该坐席不通过本质检项的检查；\n质检项2：如果坐席提供了预期回复时间，检查坐席是否在预期时间前回复客户，如果坐席A在提供了预期的答复时间之后将会话转移给坐席B，坐席B将会话转移给坐席C，坐席A、坐席B、坐席C中只要有一位坐席在预期的回复时间前回复客户，则坐席A、坐席B、坐席C都通过本质检项的检查；如果坐席A、坐席B、坐席C都没有在预期的回复时间前回复客户，则最后一位坐席（即：坐席C）没有通过本质检项的检查，其他坐席（即：坐席A、坐席B）通过本质检项的检查；\n质检项3：如果坐席回复客户需要时间处理客户的问题，并且没有向客户提供预期回复的时间，检查坐席是否在30分钟内回复客户，如果坐席A在在处理客户的问题的过程中将会话转移给坐席B，坐席B将会话转移给坐席C，则坐席A、坐席B、坐席C中只要有一位坐席在30分钟内回复客户，则坐席A、坐席B、坐席C都通过本质检项的检查；如果坐席A、坐席B、坐席C都没有在在30分钟回复客户，则最后一位坐席（即：坐席C）没有通过本质检项的检查，其他坐席（即：坐席A、坐席B）通过本质检项的检查；\n质检项4：如果会话中包含：“触发动作：催单”，判断坐席是否在3分钟内响应客户；\n质检项5：如果会话中包含：“触发动作：投诉”，判断坐席是否在3分钟内响应客户；\n质检项6：如果客户的问题处理需要跨自然日，检查坐席是否在每个自然日回复了客户；\n如果某天有多个坐席参与了与客户的对话（坐席参与了与客户的对话的含义是：坐席出现在与客户的对话中，包括坐席回复客户、坐席触发动作等），其中的一个坐席已经回复了客户，则其他不回复客户的坐席可以视为通过本质检项的检查。\n根据以上质检项，以纯json格式分别输出针对不同坐席的分析过程与质检结果，分析过程需要输出具体的时间和内容，质检结果包括“通过”、“不通过”、“不涉及”，不要输出markdown格式。\n输出格式：\n[\n    {\n        \"坐席\": \"xxx\",\n        \"首次响应时效\": {\n            \"分析\": \"xxx\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"预期时间回复\": {\n            \"分析\": \"[1]坐席xx时间提供了预期答复的时间为xx时间，在xx时间进行了回复，是否符合质检要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"30分钟内回复\": {\n            \"分析\": \"[1]坐席在xx时间回复客户xxx，未提供预期的答复时间，坐席在xx时间进行了回复，是否符合30分钟内回复的要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"催单\": {\n            \"分析\": \"[1]xx时间触发动作：催单，坐席xx时间回复客户，是否在符合3分钟内回复客户的要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"投诉\": {\n            \"分析\": \"[1]xx时间触发动作：投诉，坐席xx时间回复客户，是否在符合3分钟内回复客户的要求。[2]...[3]...\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"跨自然日\": {\n            \"分析\": \"客户的问题处理从日期xx-xx到日期xx-xx。日期xx-xx，坐席xx、xx、xx...回复了客户。日期xx-xx，坐席xx、xx、xx...回复了客户。....。本坐席xx参与了日期xx-xx、日期xx-xx...的对话，按照质检项6的要求，本坐席xx是否需要在日期xx-xx、日期xx-xx...回复客户，是否符合质检项6的要求。\",\n            \"结果\": \"通过/不通过/不涉及\"\n        }\n    }\n]\n\n对话：\n%s", "res": "[\n    {\n        \"坐席\": \"朱妍（维音）\",\n        \"首次响应时效\": {\n            \"分析\": \"客户在06-16 10:46发送第一条消息，坐席在06-16 10:49回复客户，间隔3分钟，符合3分钟回复客户的要求。\",\n            \"结果\": \"通过\"\n        },\n        \"预期时间回复\": {\n            \"分析\": \"坐席不存在需要在预期的时间内回复客户的情形。\",\n            \"结果\": \"不涉及\"\n        },\n        \"30分钟内回复\": {\n            \"分析\": \"坐席朱妍（维音）不存在需要在30分钟内回复客户的情形\",\n            \"结果\": \"不涉及\"\n        },\n        \"催单\": {\n            \"分析\": \"会话中未出现客户触发动作：催单。\",\n            \"结果\": \"不涉及\"\n        },\n        \"投诉\": {\n            \"分析\": \"会话中未出现客户触发动作：投诉。\",\n            \"结果\": \"不涉及\"\n        },\n        \"跨自然日\": {\n            \"分析\": \"客户的问题处理在06-16当天完成，未跨自然日。\",\n            \"结果\": \"不涉及\"\n        }\n    },\n    {\n        \"坐席\": \"耿育城（金道）\",\n        \"首次响应时效\": {\n            \"分析\": \"本坐席不是对话中的第1位坐席。\",\n            \"结果\": \"不涉及\"\n        },\n        \"预期时间回复\": {\n            \"分析\": \"坐席耿育城（金道）不存在需要在预期的时间内回复客户的情形。\",\n            \"结果\": \"不涉及\"\n        },\n        \"30分钟内回复\": {\n            \"分析\": \"坐席耿育城（金道）不存在需要在30分钟内回复客户的情形\",\n            \"结果\": \"不涉及\"\n        },\n        \"催单\": {\n            \"分析\": \"会话中未出现客户触发动作：催单。\",\n            \"结果\": \"不涉及\"\n        },\n        \"投诉\": {\n            \"分析\": \"会话中未出现客户触发动作：投诉。\",\n            \"结果\": \"不涉及\"\n        },\n        \"跨自然日\": {\n            \"分析\": \"客户的问题处理在06-16当天完成，未跨自然日。\",\n            \"结果\": \"不涉及\"\n        }\n    }\n]", "model": "quality_check_json_sft_service_timeliness", "resType": "Json", "modelExtraParams": null, "messages": null}, {"type": "Quality_Inspection", "name": "Service_Specifications", "desc": "服务规范", "prompt": "你是一位腾讯云售后服务质量检查员，分析客户与坐席的对话并从以下三个方面判断坐席质检是否通过，\n质检项1：坐席每次触发动作：待客户确认结单之前的3条回复中，如果坐席询问客户问题是否已经解决，则质检通过；如果坐席询问客户是否还有其他的问题需要协助（帮助），则质检通过。如果坐席没有触发动作：待客户确认结单，则不进行此质检项的质检。      \n质检项2：坐席每次触发动作：待客户确认结单之前的3条回复中，坐席是否邀请客户对本次服务进行评价；如果坐席没有触发动作：待客户确认结单，则不进行此质检项的质检。        \n质检项3：仅针对以下场景，检查坐席每次触发动作：转单之前的3条回复中，坐席是否向客户说明客户的问题被转交（反馈）给其他人员进行处理（核实）。\n场景：一线转1.5线（一线坐席将会话转移给1.5线坐席），注意：运维和产研不是1.5线，运维是2线，产研是3线；\n\n根据以上质检项，分别针对不同坐席输出json格式的分析过程与质检结果，质检结果包括“通过”、“不通过”、“不涉及”\n输出格式：\n[\n    {\n        \"坐席\": \"xxx\",\n        \"问题是否解决\": {\n            \"分析\": \"[1]坐席xx在xx时间触发动作：待客户确认结单，触发前的3条回复中坐席是否询问客户问题是否已经解决或者是否还有其他的问题需要协助（帮助），是否符合质检项要求。[2]...。[3]...。\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"邀请评价\": {\n            \"分析\": \"[1]坐席xx在xx时间触发动作：待客户确认结单，触发前的3条回复中是否邀请客户对服务进行评价，是否符合质检项要求。[2]...。[3]...。\",\n            \"结果\": \"通过/不通过/不涉及\"\n        },\n        \"转单规范\": {\n            \"分析\": \"[1]坐席xx在xx时间触发动作：转单，会话由一线/1.5线/运维/产研坐席（xx队列）转移给一线/1.5线/运维/产研坐席（xx队列），是否属于一线转1.5线，触发前的3条回复中是否向客户说明客户的问题被转交（反馈）给其他人员进行处理（核实），是否符合质检项要求。[2]...。[3]...。\",\n            \"结果\": \"通过/不通过/不涉及\"\n        }\n    }\n]\n\n对话：\n%s", "res": "[\n    {\n        \"坐席\": \"朱妍（维音）\",\n        \"问题是否解决\": {\n            \"分析\": \"坐席朱妍（维音）未触发动作：待客户确认结单，因此不进行此质检项的质检。\",\n            \"结果\": \"不涉及\"\n        },\n        \"邀请评价\": {\n            \"分析\": \"坐席朱妍（维音）未触发动作：待客户确认结单，因此不进行此质检项的质检。\",\n            \"结果\": \"不涉及\"\n        },\n        \"转单规范\": {\n            \"分析\": \"[1]坐席朱妍（维音）在10:49触发动作：转单，会话由一线坐席（在线-腾讯会议购买咨询）转移给一线坐席（在线-腾讯会议购买咨询），不属于一线转1.5线，因此不进行此质检项的质检。\",\n            \"结果\": \"不涉及\"\n        }\n    },\n    {\n        \"坐席\": \"耿育城（金道）\",\n        \"问题是否解决\": {\n            \"分析\": \"[1]坐席耿育城（金道）在11:09触发动作：待客户确认结单，触发前的3条回复中坐席在10:57询问客户是否还有其他的问题需要协助（帮助），符合质检项要求。\",\n            \"结果\": \"通过\"\n        },\n        \"邀请评价\": {\n            \"分析\": \"[1]坐席耿育城（金道）在11:09触发动作：待客户确认结单，触发前的3条回复中坐席在11:09邀请客户对服务进行评价，符合质检项要求。\",\n            \"结果\": \"通过\"\n        },\n        \"转单规范\": {\n            \"分析\": \"坐席耿育城（金道）未触发动作：转单，因此不进行此质检项的质检。\",\n            \"结果\": \"不涉及\"\n        }\n    }\n]", "model": "quality_check_json_sft_service_specifications", "resType": "Json", "modelExtraParams": null, "messages": null}], "sceneIdentify": "@TGS#2MSRPTARK"}