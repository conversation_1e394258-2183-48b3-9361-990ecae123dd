{"AddPermanentConversationMembers": "增加群成员", "AddWeworkGroupMember": "企业微信会话加人", "ApplyFinishConversation": "申请结束会话. （同时可选择是否确认四级归档）", "AppraiseConversation": "评价会话", "AskForContact": "坐席申请获取客户联系方式", "AssignConversation": "把会话分派给一个指定坐席", "AssignPostponed": "派单因非工作时间延迟的会话", "AutoFinishConversation": "AutoFinishConversation 超过3天的自动结单(提供服务给conversation expire定时服务调用).", "BindAndonTicket": "绑定工单", "CancelConversation": "取消会话，排队", "CheckServiceTime": "CheckServiceTime 检测是否在服务时间", "CleanExpireConversation": "把过期会话从坐席侧清理掉", "ClickEventCallback": "企微点击回调", "Complain": "投诉", "CreateConversation": "创建会话", "CreateConversationForWeworkExternalGroup": "创建企业微信外部群会话并接入", "CreateConversationOpen": "", "CreateConversationZX": "坐席创建会话", "DecryptMsgUser": "用户侧消息解密", "DecryptMsgZX": "坐席消息解密", "DeletePermanentConversationMembers": "删除群成员", "EnterChatEventCallback": "企微进入会话事件回调, 返回热点问题", "FinishConversation": "客服结束会话", "FinishUserConversation": "客户侧结束会话", "GetActiveConversationMetrics": "dashboard: 获取活跃会话指标", "GetActiveConversations": "获取活跃群聊", "GetAndataValueMapping": "翻译状态，操作类型", "GetAntoolGroup": "代理 Antool 群详情接口", "GetConversationAttr": "根据会话ID获取会话的一些属性", "GetConversationDetail": "dashboard: 获取会话详情", "GetConversationForWeworkExternalGroup": "获取企业微信群关联的会话", "GetConversationMetrics": "获取总体的会话的指标数据， 供视图使用", "GetConversationMsgs": "拉取会话内容消息列表", "GetConversationWhiteList": "白名单", "GetConversations": "根据会话ID批量获取会话", "GetConversationsOpen": "无状态获取会话信息", "GetCustomerInfo": "获取客户售后定级信息", "GetCustomerStatusByUin": "根据客户 uin，查询当前排队和坐席分配状态", "GetDistinctChannels": "GetDistinctChannels 按通道id查询会话数", "GetExpireConversations": "拉取待客户回复或待激活会话列表", "GetHistoryConversations": "客服拉用户历史会话列表（服务记录）", "GetLatestConversationByRtx": "获取内部服务台客户最近的一次会话", "GetMeetingCustomer": "获取腾讯会议客户信息", "GetOverConversations": "获取所有结束群聊", "GetPermanentConversationAttr": "获取企业微信外部群会话属性", "GetPermanentConversations": "根据id获取永久会话", "GetPrimaryFields": "获取关键字段", "GetQueuedConversationMetrics": "dashboard: 获取排队会话指标", "GetTobeCleanOverConversations": "拉取待清理的已结束会话列表", "GetTopTags": "智能客服热门标签", "GetTransferConversationToOneHalfLineTemplate": "获取转接会话到1.5线模版.", "GetUserConversation": "获取用户单个会话", "GetUserConversationCount": "获取用户的会话数量", "GetUserConversations": "获取用户群聊", "GetUserCurrentConversations": "获取用户当前群聊", "GetUserHistoryConversations": "获取用户历史群聊", "GetUserSig": "获取用户IM sig", "GetWeworkGroupInfo": "企业微信获取群信息", "GetZXActiveConversationsByZXId": "根据客服 id 获取客服名下活跃会话 id, uin", "GetZXMetrics": "获取坐席的指标数据", "GetZXNotReplyConversations": "获取坐席超时未回复的会话", "HandleStdExGroupMsg": "标准化通道\n// //  标准化通道企业微信外部群消息处理", "JoinConversation": "客服主动接入一个当前在排队会话", "JoinPermanentConversation": "主动加入企业微信外部群会话", "MarkConversation": "标记会话", "MarkHistoryMessage": "标记历史消息（文件查杀等)", "ModifyConversation": "修改会话属性(如归档信息等)", "ModifyGroupBaseInfo": "修改群基本信息（不改会话表）", "ModifyPermanentConversation": "修改会话属性(如告警)", "ModifyUin": "修改子账户 uin", "NotifyTicketChange": "通知会话绑定的工单信息修改", "PollingInputStatus": "轮询输入中状态", "PreUpgradeConversation": "预升级群聊", "ProvideContact": "客户提供联系方式", "QueueChange": "排队发生变化", "QuitWeworkGroup": "企业微信手动退出群聊", "ReloadChannel": "更新通道配置", "ReportConversationExt": "会话额外属性上报", "ReportInputStatus": "上报输入中状态", "RestoreTimMsgs": "转存IM聊天消息", "RevokeZXMsg": "撤回消息", "SearchConversation": "搜索企业微信外部群会话", "SendChatCallbackMsg": "企微回调发消息", "SendPermanentConversationMsg": "企微外部群收消息后发送到conversation", "SendSystemMsg": "发送系统消息", "SendUserMsg": "用户侧发送消息", "SendWeworkExternalGroupMsg": "企业微信外部群发送消息", "SendZXMsg": "坐席发消息", "SmartWelcome": "用户进入智能客服后，给到用户的欢迎语", "TicketAwaitingSupplement": "会话工单待补充状态变更", "TicketChangeNotifyByKafka": "Kafka通知会话工单状态变更", "UnmarkConversation": "取消标记会话", "UpgradeConversation": "升级群聊", "UploadLead": "电销转点石商机"}