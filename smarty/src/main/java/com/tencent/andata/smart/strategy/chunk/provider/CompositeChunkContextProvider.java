package com.tencent.andata.smart.strategy.chunk.provider;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
public class CompositeChunkContextProvider implements ChunkContextProvider {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private final java.util.List<ChunkContextProvider> providers;
    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final Map<String, String> providerMap = new HashMap<String, String>() {{
        put("HbaseGroupMsgChunkContextProvider", "msg_data");
        put("HbaseOperationChunkContextProvider", "operation_data");
    }};

    public CompositeChunkContextProvider(java.util.List<ChunkContextProvider> providers) {
        this.providers = providers;
    }

    @Override
    public CompletableFuture<String> getContext(Strategy strategy) {
        return Try.of(() -> {
                    // 转换为vavr的List并处理每个provider
                    List<CompletableFuture<Tuple2<String, String>>> futures = List.ofAll(providers)
                            .map(provider -> getProviderResult(provider, strategy));

                    // 等待所有异步操作完成
                    CompletableFuture<?>[] futuresArray = futures.toJavaArray(CompletableFuture[]::new);

                    return CompletableFuture.allOf(futuresArray)
                            .thenApply(v -> combineResults(futures));
                })
                .getOrElseGet(e -> {
                    logger.error("[CompositeChunkContextProvider] Failed to process providers: " + e);
                    return CompletableFuture.completedFuture("{}");
                });
    }

    private CompletableFuture<Tuple2<String, String>> getProviderResult(
            ChunkContextProvider provider,
            Strategy strategy) {
        String providerName = provider.getClass().getSimpleName();
        return provider.getContext(strategy)
                .thenApply(result -> Tuple.of(providerMap.get(providerName), result));
    }

    private String combineResults(List<CompletableFuture<Tuple2<String, String>>> futures) {
        return Try.of(() -> {
                    HashMap<String, String> resultMap = futures.toJavaStream()
                            .map(CompletableFuture::join)
                            .filter(tuple -> tuple != null && tuple._2 != null)
                            .collect(HashMap::new, (map, tuple) -> map.put(tuple._1, tuple._2), HashMap::putAll);

                    return MAPPER.writeValueAsString(resultMap);
                })
                .getOrElseGet(e -> {
                    logger.error("[combineResults] Failed to combine results: " + e);
                    return "{}";
                });
    }

    @Override
    public boolean supports(Strategy strategy) {
        return strategy.chunk.type == ChunkType.Composite;
    }
}