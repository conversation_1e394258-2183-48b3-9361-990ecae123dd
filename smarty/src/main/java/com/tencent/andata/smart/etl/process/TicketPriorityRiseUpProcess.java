package com.tencent.andata.smart.etl.process;

import java.util.HashMap;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

public class TicketPriorityRiseUpProcess extends KeyedProcessFunction<Integer, Tuple2<Integer, String>, String> {

    private HashMap<String, Integer> map;
    private ObjectMapper objectMapper;
    // 储存模型识别出来的工单优先级最大值
    private transient ValueState<Integer> priorityState;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES,false);
        map = new HashMap<String, Integer>() {{
            put("L1", 5);
            put("L2", 4);
            put("L3", 3);
            put("L4", 2);
            put("L5", 1);
        }};

        ValueStateDescriptor<Integer> maxPriorityDescriptor = new ValueStateDescriptor<>(
                "max_priority_state",
                Integer.class);

        priorityState = getRuntimeContext().getState(maxPriorityDescriptor);

    }

    @Override
    public void processElement(Tuple2<Integer, String> input, Context ctx, Collector<String> out) throws Exception {
        HashMap<String, Object> data = objectMapper.readValue(input.f1, new TypeReference<HashMap<String, Object>>() {
        });

        String ticketStatus = (String) data.get("status"); // 工单优先级
        String ticketDbPriorityLevel = (String) data.get("priority"); // 工单数据库中的优先级
        String agentPriorityLevel = (String) data.get("reflection_type_classify"); // 模型识别出来的优先级

        // 如果没有上一次，则直接下发
        if (priorityState.value() == null || priorityState.value() == 0) {
            data.put("is_first_judge", 1);
            data.put("is_rise_up", map.get(agentPriorityLevel) > map.get(ticketDbPriorityLevel) ? 1 : 0);
            priorityState.update(Math.max(map.get(agentPriorityLevel), map.get(ticketDbPriorityLevel)));
        } else {
            data.put("is_first_judge", 0);
            data.put("is_rise_up", map.get(agentPriorityLevel) > priorityState.value() ? 1 : 0);
            // 状态中保留最高优先级
            priorityState.update(Math.max(map.get(agentPriorityLevel), priorityState.value()));
        }

        out.collect(objectMapper.writeValueAsString(data));
    }
}