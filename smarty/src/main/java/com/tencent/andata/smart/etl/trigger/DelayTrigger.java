package com.tencent.andata.smart.etl.trigger;

import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.GlobalWindow;

/**
 * 延迟触发器
 * 用于在全局窗口中实现基于处理时间的延迟触发
 */
public class DelayTrigger extends Trigger<Object, GlobalWindow> {
    private final long interval;
    private long nextFireTimestamp;

    public DelayTrigger(Time interval) {
        this.interval = interval.toMilliseconds();
    }

    @Override
    public TriggerResult onElement(Object element, long timestamp, GlobalWindow window, TriggerContext ctx) throws Exception {
        if (nextFireTimestamp == 0) {
            nextFireTimestamp = ctx.getCurrentProcessingTime() + interval;
            ctx.registerProcessingTimeTimer(nextFireTimestamp);
        }
        return TriggerResult.CONTINUE;
    }

    @Override
    public TriggerResult onProcessingTime(long time, GlobalWindow window, TriggerContext ctx) throws Exception {
        if (time >= nextFireTimestamp) {
            nextFireTimestamp = ctx.getCurrentProcessingTime() + interval;
            ctx.registerProcessingTimeTimer(nextFireTimestamp);
            return TriggerResult.FIRE;
        }
        return TriggerResult.CONTINUE;
    }

    @Override
    public TriggerResult onEventTime(long time, GlobalWindow window, TriggerContext ctx) {
        return TriggerResult.CONTINUE;
    }

    @Override
    public void clear(GlobalWindow window, TriggerContext ctx) throws Exception {
        ctx.deleteProcessingTimeTimer(nextFireTimestamp);
        nextFireTimestamp = 0;
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void onMerge(GlobalWindow window, OnMergeContext ctx) {
        long nextFireTimestamp = ctx.getCurrentProcessingTime() + interval;
        ctx.registerProcessingTimeTimer(nextFireTimestamp);
    }
}