package com.tencent.andata.smart.utils.aviatorFuns;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import java.util.Map;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class AddAttributeFunction extends AbstractFunction {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject key, AviatorObject value) {
        String k = FunctionUtils.getStringValue(key, env);
        Object v = value.getValue(env);
        env.put(k, v);

        // 将Map转换为JsonNode
        JsonNode jsonNode = objectMapper.valueToTree(env);
        return FunctionUtils.wrapReturn(jsonNode);
    }

    @Override
    public String getName() {
        return "put";
    }
}