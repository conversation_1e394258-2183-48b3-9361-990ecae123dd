package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * 重试控制处理函数
 * 处理需要重试的数据流，并根据重试次数和时间戳决定是否输出到主流程或重试队列
 */
public class RetryControlProcessFunction extends ProcessFunction<Tuple3<Strategy, Integer, Long>, Strategy> {

    private final int maxRetryCount;
    private static final FlinkLog logger = FlinkLog.getInstance();
    private final OutputTag<Tuple3<Strategy, Integer, Long>> retryTag;

    /**
     * 构造函数
     *
     * @param maxRetryCount 最大重试次数
     * @param retryTag 需要重试的数据标签
     */
    public RetryControlProcessFunction(int maxRetryCount, OutputTag<Tuple3<Strategy, Integer, Long>> retryTag) {
        this.maxRetryCount = maxRetryCount;
        this.retryTag = retryTag;
    }

    @Override
    public void processElement(Tuple3<Strategy, Integer, Long> input, Context ctx, Collector<Strategy> out) {
        if (input == null) {
            return;
        }

        int retryCount = input.f1;
        Strategy strategy = input.f0;
        long nextRetryTime = input.f2;

        // 检查是否需要等待
        long currentTime = System.currentTimeMillis();

        if (currentTime < nextRetryTime && nextRetryTime > 0) {
            // 如果还没到重试时间，输出到重试队列
            ctx.output(retryTag, input);
            logger.info("[RetryControlProcessFunction] Strategy not ready for retry yet, queued for later. sceneIdentify: "
                    + strategy.sceneIdentify + ", currentRetryCount: " + retryCount + ", nextRetryTime: " + nextRetryTime);
            return;
        }

        // 检查重试次数上限
        if (retryCount >= maxRetryCount) {
            // 达到最大重试次数，直接丢弃数据，只记录日志
            logger.error("[RetryControlProcessFunction] Strategy max retry reached, discarding data. sceneIdentify: "
                    + strategy.sceneIdentify + ", maxRetryCount: " + maxRetryCount + ", currentRetryCount: " + retryCount);
            return;
        }

        // 达到重试时间且未超过最大重试次数，进入正常处理流程
        logger.info("[RetryControlProcessFunction] Processing strategy in attempt: " + retryCount
                + ", sceneIdentify: " + strategy.sceneIdentify);
        out.collect(strategy);
    }

    /**
     * 创建重试元组
     *
     * @param strategy 策略对象
     * @param retryCount 重试次数
     * @param retryIntervalSeconds 重试间隔（秒）
     * @return 包含策略、重试次数和下次重试时间的元组
     */
    public static Tuple3<Strategy, Integer, Long> createRetryTuple(Strategy strategy, int retryCount, int retryIntervalSeconds) {
        long nextRetryTime = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(retryIntervalSeconds);

        logger.info("[RetryControlProcessFunction] Scheduling retry for strategy, retryCount: " + retryCount +
                ", sceneIdentify: " + strategy.sceneIdentify + ", nextRetryTime: " + nextRetryTime);

        return new Tuple3<>(strategy, retryCount, nextRetryTime);
    }
}