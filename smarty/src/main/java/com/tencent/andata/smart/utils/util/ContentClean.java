package com.tencent.andata.smart.utils.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class ContentClean {

    // TODO 这里数据清洗有点乱，后面整合一下
    public static ArrayList<String> deleteTag = new ArrayList<String>() {{
        add("<a[^>]*>(.*?)<\\/a>");
        add("<br>");
        add("&quot;");
        add("<br />");
        add("&nbsp;");
        add("\\[br\\]");
        add("\\[img.*(png|jpeg|jpg)\\]");
    }};
    public static ArrayList<String> opinionDeleteTag = new ArrayList<String>() {{
        add("<a[^>]*>(.*?)<\\/a>");
        add("<br>");
        add("&quot;");
        add("&nbsp;");
        add("d&amp;");
        add("&amp;");
        add("</div>");
        add("<div>");
        add("&lt;");
        add("/div&gt;");
        add("div&gt;");
        add("sign&lt;");
        add("\n");
        add("\r");
        add("\t");
        add("\\[br\\]");
        add("\\[img.*(png|jpeg|jpg)\\]");
    }};

    public static String UserContentClean(String content) {
        if (content == null) {
            return null;
        }
        // 只要分隔符以后的内容
        String splitTag = "-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-&nbsp;-";
        if (content.contains(splitTag)) {
            String[] split = content.split(splitTag);
            content = split[split.length - 1];
        }

        for (String tag : opinionDeleteTag) {
            if (tag.equals("\\[img.*(png|jpeg|jpg)\\]")) {
                content = content.replaceAll(tag, "[图片] ");
            } else {content = content.replaceAll(tag, "");}
        }
        return content;
    }

    public static String OpinionContentClean(String content) {
        if (content == null) {
            return null;
        }
        for (String tag : opinionDeleteTag) {
            content = content.replaceAll(tag, "");
        }
        return content;
    }

    /*
     * 提取智能客服聊天记录中的客户发言内容，以便于过小模型
     * @param conversation
     */
    public static String extractRobotRawCustomerMessages(JsonNode message) {
        // 匹配"客户:"开头的内容
        Pattern pattern = Pattern.compile("客户:.*", Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(message.get("display_content").asText());

        // 保存所有匹配到的客户发言
        List<String> customerMessages = new ArrayList<>();
        while (matcher.find()) {
            customerMessages.add(matcher.group());
        }

        // 用换行符连接所有客户发言
        return String.join("\n", customerMessages);
    }
}