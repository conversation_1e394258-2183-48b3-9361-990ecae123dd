package com.tencent.andata.smart.etl.handler;

import static com.tencent.andata.utils.ExceptionWrapperUtil.function;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.etl.domain.DutyInfo;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.strategy.model.Strategy;
import io.vavr.Predicates;
import io.vavr.Tuple4;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.io.Serializable;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

@RequiredArgsConstructor
public abstract class AbstractSceneHandler implements SceneHandler, Serializable {

    private static final FlinkLog logger = FlinkLog.getInstance();
    protected static final ObjectMapper MAPPER = new ObjectMapper();

    protected final DutyRepository dutyRepository;
    protected final TicketRepository ticketRepository;
    protected final CustomerRepository customerRepository;


    protected Option<JsonNode> getFirstData(Strategy strategy) {
        return Option.of(strategy)
                .filter(s -> s.trigger != null && s.trigger.data != null && s.trigger.data.length > 0)
                .flatMap(s -> Option.of(s.trigger.data[0]))
                .flatMap(this::parseTriggerData)
                .onEmpty(() -> logger.error("[AbstractSceneHandler] Failed to parse first data from strategy: " + strategy));
    }

    @Override
    public Option<String> getTriggerContent(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("display_content").asText());
    }

    @Override
    public Option<String> getRiskType(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("risk_type").asText())
                .map(riskType -> Stream.of(riskType.split(",")))
                .map(stream -> stream.filter(type -> !StringUtils.equals(type, "无风险")))
                .map(filteredStream -> filteredStream.collect(Collectors.joining(",")));
    }

    @Override
    public Option<String> getDutyResponsible(Strategy strategy) {
        return Try.of(() -> {
            // 查询队列信息
            int dutyId = getFirstData(strategy)
                    .map(data -> data.get("fact_assign").asInt())
                    .getOrElse(0);

            DutyInfo dutyInfo = dutyRepository
                    .findById(dutyId)
                    .getOrElse(DutyInfo.builder().build());

            // 使用vavr的List处理JsonNode
            return Option.of(dutyInfo.getResponsible())
                    .map(function(MAPPER::readTree))
                    .map(node ->
                            List.ofAll(node::elements)                                  // 将JsonNode转换为vavr的List
                                    .map(JsonNode::asText)                                  // 提取文本值
                                    .filter(text -> !StringUtils.isEmpty(text))       // 过滤空值
                                    .mkString(","))                                 // 用逗号连接
                    .getOrElse("");                                               // 处理空值情况
        }).recover(e -> {
            logger.error("[AbstractSceneHandler] Failed to get duty for strategy: " + strategy + " errMsg: " + e);
            return "";  // 发生异常时返回空字符串
        }).toOption();
    }

    private Option<JsonNode> parseTriggerData(Object data) {
        return Match(data).of(
                Case($(Predicates.instanceOf(JsonNode.class)), Option::of),
                Case($(), d -> Try.of(() -> MAPPER.readTree(d.toString()))
                        .onFailure(e -> logger.error("Failed to parse data element: " + data + " errMsg:" + e))
                        .toOption())
        );

        // return parseJsonString(data);
    }

    private Option<JsonNode> parseJsonString(Object data) {
        return Try.of(() -> MAPPER.readTree(data.toString()))
                .onFailure(e -> logger.error("Failed to parse JSON string: " + data + " errMsg:" + e))
                .toOption();
    }

    @Override
    public Option<Integer> getMsgSeq(Strategy strategy) {
        return getFirstData(strategy)
                .flatMap(data -> Try.of(() -> data.get("msgseq").asInt())
                        .toOption());
    }

    @Override
    public Option<JsonNode> getTriggerDataByField(Strategy strategy, String field) {
        return getFirstData(strategy)
                .flatMap(data -> Try.of(() -> data.get(field))
                        .toOption());
    }

    @Override
    public Option<Tuple4<String, String, String, String>> getServiceScenesName(Strategy strategy) {
        JsonNode firstData = getFirstData(strategy).get();
        return Option.of(new Tuple4<>(
                Try.of(() -> firstData.get("service_scene_level1_name").asText()).getOrElse(""),
                Try.of(() -> firstData.get("service_scene_level2_name").asText()).getOrElse(""),
                Try.of(() -> firstData.get("service_scene_level3_name").asText()).getOrElse(""),
                Try.of(() -> firstData.get("service_scene_level4_name").asText()).getOrElse("")
        ));
    }
}