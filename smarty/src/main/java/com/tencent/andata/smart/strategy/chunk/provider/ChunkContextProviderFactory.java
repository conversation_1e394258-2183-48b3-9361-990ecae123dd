package com.tencent.andata.smart.strategy.chunk.provider;

import com.google.common.collect.ImmutableMap;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.chunk.model.HbaseTable;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import org.apache.hadoop.hbase.client.AsyncConnection;

public class ChunkContextProviderFactory {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final RainbowUtils rainbow = RainbowAppConfig.getInstance();
    // 声明静态成员变量
    private static final ImmutableMap<Scene, String> sceneTableNameMap;
    private static final ImmutableMap<Scene, HbaseTable> operationTableMap;
    private static final ImmutableMap<Scene, HbaseTable> groupsMsgTableMap;

    static {
        // 使用静态方法初始化
        sceneTableNameMap = initSceneTableNameMap();
        operationTableMap = initOperationTableMap(sceneTableNameMap);
        groupsMsgTableMap = initGroupsMsgTableMap(sceneTableNameMap);
    }

    private final List<ChunkContextProvider> providers = new ArrayList<>();

    public ChunkContextProviderFactory(AsyncConnection asyncConnection) {
        ExecutorService executorService = Executors.newFixedThreadPool(16);

        // 创建复合Provider
        List<ChunkContextProvider> primitiveProvider = Arrays.asList(
                new RetryStrategyContextProvider(),
                new HbaseOperationChunkContextProvider(asyncConnection, operationTableMap, executorService),
                new HbaseGroupMsgChunkContextProvider(asyncConnection, groupsMsgTableMap, executorService)
        );

        // 组合上下文，不能是重试策略（重试策略已经构建过上下文了，无需再次构建）
        ChunkContextProvider compositeProvider = new CompositeChunkContextProvider(
                primitiveProvider.stream()
                        .filter(provider -> !(provider instanceof RetryStrategyContextProvider))
                        .collect(Collectors.toList())
        );

        this.providers.addAll(primitiveProvider);
        this.providers.add(compositeProvider);
    }

    // 使用Guava静态初始化sceneTableNameMap
    private static ImmutableMap<Scene, String> initSceneTableNameMap() {
        return ImmutableMap.<Scene, String>builder()
                .put(Scene.C2000, rainbow.getStringValue("cdc.database.hbase", "GROUP_MSG"))
                .put(Scene.Group, rainbow.getStringValue("cdc.database.hbase", "GROUP_MSG"))
                .put(Scene.WebIM, rainbow.getStringValue("cdc.database.hbase", "WEBIM_OPERATION"))
                .put(Scene.Ticket, rainbow.getStringValue("cdc.database.hbase", "TICKET_OPERATION"))
                .build();
    }

    // 使用Guava静态初始化operationTableMap
    private static ImmutableMap<Scene, HbaseTable> initOperationTableMap(ImmutableMap<Scene, String> sceneTableNameMap) {
        return ImmutableMap.<Scene, HbaseTable>builder()
                .put(Scene.C2000, createHbaseTable(sceneTableNameMap, Scene.Group))
                .put(Scene.Group, createHbaseTable(sceneTableNameMap, Scene.Group))
                .put(Scene.WebIM, createHbaseTable(sceneTableNameMap, Scene.WebIM))
                .put(Scene.Ticket, createHbaseTable(sceneTableNameMap, Scene.Ticket))
                .build();
    }

    // 使用Guava静态初始化groupsMsgTableMap
    private static ImmutableMap<Scene, HbaseTable> initGroupsMsgTableMap(ImmutableMap<Scene, String> sceneTableNameMap) {
        return ImmutableMap.<Scene, HbaseTable>builder()
                .put(Scene.C2000, createHbaseTable(sceneTableNameMap, Scene.Group))
                .put(Scene.Group, createHbaseTable(sceneTableNameMap, Scene.Group))
                .put(Scene.Ticket, createHbaseTable(sceneTableNameMap, Scene.Group))
                .build();
    }

    // 提取公共的HbaseTable创建逻辑
    private static HbaseTable createHbaseTable(ImmutableMap<Scene, String> sceneTableMap, Scene scene) {
        return new HbaseTable(sceneTableMap.get(scene), "cf", "data");
    }

    public List<ChunkContextProvider> getProviders() {
        return providers;
    }
}