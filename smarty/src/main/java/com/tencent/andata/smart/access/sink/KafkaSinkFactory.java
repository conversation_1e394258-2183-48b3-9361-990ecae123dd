package com.tencent.andata.smart.access.sink;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.smart.access.config.AppConfig;
import io.vavr.control.Try;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import java.util.Random;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KafkaSinkFactory {

    private static final Logger log = LoggerFactory.getLogger(KafkaSinkFactory.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public KafkaSink<JsonNode> createKafkaSink(AppConfig appConfig) {
        MqConf sinkKafkaConf = getKafkaConf(appConfig);
        return createKafkaSink(appConfig, sinkKafkaConf.getTopic());
    }

    public KafkaSink<JsonNode> createKafkaSink(AppConfig appConfig, String topic) {
        MqConf sinkKafkaConf = getKafkaConf(appConfig);

        // 使用传入的topic，如果为null则使用配置中的topic
        String targetTopic = StringUtils.isEmpty(topic) ? sinkKafkaConf.getTopic() : topic;
        log.info("Creating Kafka sink with topic: {}", targetTopic);
        Properties sinkKafkaConfig = createKafkaProperties();

        return KafkaSink
                .<JsonNode>builder()
                .setBootstrapServers(sinkKafkaConf.getBroker())
                .setKafkaProducerConfig(sinkKafkaConfig)
                .setRecordSerializer(createRecordSerializer(targetTopic))
                /* *************************************************************************************************
                 * TODO 如果开启事务，从CK/SP恢复的话，KafkaSink会一直报错：
                 *  TimeoutException: Timeout expired after 60000 milliseconds while awaiting InitProducerId，
                 *  原因未知怀疑和kafka配置有关，参考：https://blog.csdn.net/ZhuXinYi_3016/article/details/128497093
                 **************************************************************************************************/
                //.setTransactionalIdPrefix(createTransactionalIdPrefix())
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();
    }

    /**
     * 获取Kafka配置
     *
     * @param appConfig 应用配置
     * @return Kafka配置
     * @throws IllegalStateException 如果配置缺失
     */
    private MqConf getKafkaConf(AppConfig appConfig) {
        MqConf sinkKafkaConf = appConfig.getKafkaConfigs().get("ansmartQuality");
        if (sinkKafkaConf == null) {
            throw new IllegalStateException("Missing Kafka configuration for ansmart_quality");
        }
        return sinkKafkaConf;
    }

    private Properties createKafkaProperties() {
        Properties config = new Properties();
        config.setProperty("retries", "3");
        config.setProperty("acks", "all");
        // Flink消费Kafka时会根据Kafka消息的压缩类型自动进行解压缩
        config.setProperty("compression.type", "gzip");
        config.setProperty("compression.codec", "gzip");
        config.setProperty("enable.idempotence", "true");
        config.setProperty("max.request.size", "12582912");
        config.setProperty("transaction.timeout.ms", "900000");
        config.setProperty("commit.offsets.on.checkpoint", "true");
        config.setProperty("partition.discovery.interval.ms", "1800000");
        return config;
    }

    private KafkaRecordSerializationSchema<JsonNode> createRecordSerializer(String topic) {
        return KafkaRecordSerializationSchema
                .<JsonNode>builder()
                .setTopic(topic)
                .setValueSerializationSchema(createJsonNodeSerializer())
                .build();
    }

    private SerializationSchema<JsonNode> createJsonNodeSerializer() {
        return element ->
                Try.of(() -> objectMapper.writeValueAsBytes(element))
                   .onFailure(e -> log.error("Failed to serialize jsonNode: {}", element, e))
                   .getOrElse(element.toString().getBytes(StandardCharsets.UTF_8));
    }

    private String createTransactionalIdPrefix() {
        //SnowflakeDistributeId idWorker = new SnowflakeDistributeId(getWorkId(), getDataCenterId());
        return String.format("smarty-access-%d-%d", System.nanoTime(), new Random().nextInt(100000)); //, idWorker.nextId());
    }
}