package com.tencent.andata.smart.enums;

import io.vavr.collection.List;
import lombok.Getter;

@Getter
public enum ActionType {
    URGE("4", "催单"),
    TRANSFER("5", "转单"),
    TRANSFER_RESPONSIBLE("10", "交接"),
    WAIT_CONFIRM("15", "待客户确认结单"),
    CLOSE("16", "结单"),
    COMPLAINT("31", "投诉");

    private final String code;
    private final String desc;

    ActionType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActionType fromCode(String code) {
        return List.of(values())
                   .filter(t -> t.code.equals(code))
                   .getOrNull();
    }

    public static String getDesc(String code) {
        return List.of(values())
                   .filter(t -> t.code.equals(code))
                   .map(ActionType::getDesc)
                   .getOrNull();
    }
}