package com.tencent.andata.smart.etl.process;

import static com.tencent.andata.utils.ExceptionWrapperUtil.function;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_SINGLE_QUOTES;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_READING_DUP_TREE_KEY;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_TRAILING_TOKENS;
import static org.apache.hadoop.hbase.util.Threads.LOGGING_EXCEPTION_HANDLER;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.analyze.ResType;
import com.tencent.andata.utils.AsyncHttpClientUtils;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.gpt.CommonGptApiV2;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonFactory;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.util.concurrent.ExecutorThreadFactory;

/**
 * 策略分析LLM处理类
 * <p>
 * 该类负责处理Strategy对象中的多个analyzes，对每个analyze使用LLM进行分析处理。
 * <p>
 * 【重要并发处理说明】
 * 2025.4.8 修复了一个关键并发处理bug：当处理多个analyzes时，确保所有analyzes都处理完成后
 * 才调用一次resultFuture.complete。这是因为Flink的ResultFuture只能被complete一次，
 * 之后的所有complete调用都会被忽略。
 * <p>
 * 旧代码错误：在每个analyze处理完成后都调用resultFuture.complete，导致只有第一个
 * 完成的analyze结果被返回，其他analyze的处理结果虽然会更新到analyze对象中，但由于
 * resultFuture已经complete，这些更新不会被正确传递到下游算子。
 * <p>
 * 当前正确实现：使用CompletableFuture.allOf等待所有analyzes处理完成后，再调用一次
 * resultFuture.complete，确保所有analyze的处理结果都能被正确返回。
 */
@Builder
public class StrategyAnalyzeLLMProcess extends RichAsyncFunction<Strategy, Strategy> {

    private static FlinkLog logger = FlinkLog.getInstance();

    private static final JsonFactory JSON_FACTORY = new JsonFactory();

    private static final HttpClientUtils httpClientUtils = new HttpClientUtils(300000, 300000);


    private static final ObjectMapper MAPPER = new ObjectMapper()
            .configure(FAIL_ON_TRAILING_TOKENS, true)  // 确保没有多余的内容
            .configure(FAIL_ON_READING_DUP_TREE_KEY, true)  // 重复键检查
            .configure(ALLOW_SINGLE_QUOTES, false)  // 禁止单引号
            .configure(ALLOW_UNQUOTED_FIELD_NAMES, false);  // 禁止无引号字段名

    private static final String JSON_REPAIR_URL = "http://11.145.81.86:8080/gpt-api/json_repair";

    private String modelUrl;
    private String modelToken;
    private transient RetryConfig config;
    private transient CommonGptApiV2 LLMAPI;
    private transient ScheduledExecutorService executorService;
    private transient AsyncHttpClientUtils asyncHttpClientUtils;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.LLMAPI = new CommonGptApiV2(modelToken, modelUrl, "");
        this.config = RetryConfig.custom().maxAttempts(5).waitDuration(Duration.ofSeconds(20)).build();
        this.asyncHttpClientUtils = new AsyncHttpClientUtils(6000000, 6000000);
        this.executorService = Executors.newScheduledThreadPool(2,
                new ExecutorThreadFactory("llm-async-call-worker", LOGGING_EXCEPTION_HANDLER));
    }

    @Override
    public void asyncInvoke(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        CompletableFuture.runAsync(() -> processStrategy(strategy, resultFuture), executorService);
    }


    /**
     * 处理策略对象中的所有analyzes
     * <p>
     * 【并发处理说明】
     * 该方法并行处理strategy中的所有analyzes，并使用CompletableFuture.allOf等待所有
     * analyzes处理完成后，再调用一次resultFuture.complete。
     * <p>
     * 这种设计确保了在处理多个analyzes时，所有analyzes的处理结果都能被正确返回。
     * 如果在每个analyze处理完成后就调用resultFuture.complete，则只有第一个完成的
     * analyze的结果会被返回，因为ResultFuture只能被complete一次，后续的complete调用
     * 都会被忽略。
     *
     * @param strategy 待处理的策略对象
     * @param resultFuture Flink异步IO的结果Future
     */
    private void processStrategy(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        CompletableFuture<?>[] futures = Arrays.stream(strategy.analyzes)
                .map(analyze -> {
                    prepareLLMRequest(analyze, strategy.chunk.conversation);
                    return executeWithRetry(analyze, strategy);
                })
                .toArray(CompletableFuture[]::new);

        // 使用CompletableFuture.allOf等待所有analyze处理完成
        // 只在所有任务完成后调用一次resultFuture.complete，确保所有analyze的结果都能被正确返回
        CompletableFuture.allOf(futures)
                .whenComplete((result, throwable) -> resultFuture.complete(Collections.singleton(strategy)));
    }

    private void prepareLLMRequest(Analyze analyze, String conversation) {
        String promptTmp = analyze.prompt;
        this.LLMAPI.setModel(analyze.model);
        this.LLMAPI.setMessages(new CopyOnWriteArrayList<>());
        this.LLMAPI.setExtraParams(analyze.modelExtraParams);
        this.LLMAPI.messages.addAll(analyze.messages == null ? new CopyOnWriteArrayList<>() : analyze.messages);
        ConcurrentHashMap<String, Object> userPrompt = new ConcurrentHashMap<String, Object>() {{
            put("role", "user");
            put("content", String.format(promptTmp, conversation));
        }};

        // timeout重试时，需要先删除再添加，否则会重复
        this.LLMAPI.messages.remove(userPrompt);
        this.LLMAPI.messages.add(userPrompt);

        this.LLMAPI.buildRequest(String.format(promptTmp, conversation));
    }

    /**
     * 执行带重试机制的LLM请求
     * <p>
     * 【注意】该方法不再直接调用resultFuture.complete，而是返回一个CompletableFuture
     * 由上层的processStrategy方法在所有analyze处理完成后再统一调用resultFuture.complete。
     * <p>
     * 这是为了解决多个analyze并行处理时，只有第一个完成的analyze结果被返回的问题。
     * 之前的实现中，每个analyze处理完成后都会调用resultFuture.complete，但Flink的
     * ResultFuture只能被complete一次，导致后续的analyze处理结果虽然会更新到analyze对象中，
     * 但这些更新不会被传递到下游算子。
     *
     * @param analyze 需要处理的analyze对象
     * @param strategy 策略对象
     * @return 一个表示异步处理完成的CompletableFuture
     */
    private CompletableFuture<Void> executeWithRetry(Analyze analyze, Strategy strategy) {
        Retry retry = Retry.of("LLMAPIRetry", this.config);

        // 重试失败日志
        retry.getEventPublisher().onRetry(event ->
                logger.error(String.format("[StrategyAnalyzeLLMProcess] Retry attempt %s failed. Reason: %s",
                        event.getNumberOfRetryAttempts(), event.getLastThrowable())));

        return retry.executeCompletionStage(executorService, () -> requestWithRetry(analyze))
                .toCompletableFuture()
                // 记录异常但不完成resultFuture，让上层的allOf等待所有任务并处理
                .exceptionally(throwable -> {fallback(strategy, throwable); return null;});
    }

    private CompletionStage<Void> requestWithRetry(Analyze analyze) {
        CompletableFuture<Void> future = new CompletableFuture<>();

        Try.of(() -> this.LLMAPI.requestAndParse(this.asyncHttpClientUtils))
                .map(completionStage -> completionStage
                        .thenApply(origResp -> handleJsonResponse(analyze, Tuple.of(getLLMAnswer(origResp, "requestId"), getLLMAnswer(origResp, "answer"))))
                        .thenAccept(x -> Option.of(x._2)
                                .filter(this::isValidResponse)
                                .peek(res -> analyze.res = res)
                                // 通知异步请求完成
                                .peek(res -> future.complete(null))
                                .onEmpty(() -> future.completeExceptionally(new RuntimeException("LLM response is null or empty, requestId: " + x._1)))))
                .onFailure(future::completeExceptionally);

        return future;
    }

    /**
     * 处理LLM调用失败的场景
     * <p>
     * 【注意】该方法不再调用resultFuture.complete，因为我们需要等待所有analyze处理
     * 完成后再统一调用resultFuture.complete。
     *
     * @param strategy 策略对象
     * @param e 异常信息
     */
    public void fallback(Strategy strategy, Throwable e) {
        logger.error(String.format("[StrategyAnalyzeLLMProcess] Call to LLM failed after five retries, lastRequestId: %s, strategy: %s, errMsg: %s", LLMAPI.requestId, strategy, e));
        // 这里可以设置错误标记或默认值，但不调用resultFuture.complete
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    /**
     * 处理超时情况
     * <p>
     * 【注意】在超时处理中，我们重新调用processStrategy，它会为所有的analyze重新
     * 执行请求，并在所有请求完成后统一调用resultFuture.complete。
     *
     * @param strategy 策略对象
     * @param resultFuture Flink异步IO的结果Future
     */
    @Override
    public void timeout(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        String sceneIdentify = strategy.sceneIdentify;
        logger.error(String.format("场景id：%s 调用大模型超时，请求id: %s", sceneIdentify, LLMAPI.requestId));

        // 对每个analyze重新执行请求
        processStrategy(strategy, resultFuture);
        //resultFuture.complete(Collections.singleton(strategy));
    }

    /**
     * 检查一个字符串是否是有效的 JSON。
     *
     * @param jsonStr 要检查的字符串
     * @return 如果字符串是有效的 JSON，返回 true；否则返回 false
     */
    private boolean isValidJson(String jsonStr) {
        return Try.withResources(() -> JSON_FACTORY.createParser(jsonStr))
                .of(parser -> {
                    while (parser.nextToken() != null) {}
                    return true; // 如果没有异常，说明 JSON 是有效的
                }).recover(JsonProcessingException.class, e -> {
                    logger.warn("Invalid JSON: " + e.getMessage());
                    return false;
                }).getOrElse(false); // 如果发生其他异常，返回 false
    }

    private boolean isValidResponse(String response) {
        return !StringUtils.isEmpty(response)
                && !StringUtils.equalsIgnoreCase(response, "null");
    }


    // 修复JSON格式
    private String repairJson(String invalidJson) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("json_data", invalidJson);

        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json;charset=UTF-8");

        return Try.of(() -> httpClientUtils.post(JSON_REPAIR_URL, requestBody, headers))
                .map(function(res -> MAPPER.readTree(res).get("data").get("converted_data").asText()))
                // 使用JSON解析器自动处理Unicode转义字符
                .map(convertedData -> Option.of(convertedData)
                        .filter((x) -> !StringUtils.equals(convertedData, "\"\""))
                        .map(function((x) -> MAPPER.readTree(convertedData)))
                        .map(function(x -> MAPPER.writeValueAsString(convertedData)))
                        .onEmpty(() -> logger.error("JSON repair failed, response is empty, origin invalidJson is: " + invalidJson))
                        .getOrElse(invalidJson))
                .getOrElse(invalidJson);
    }


    // 处理JSON响应
    private Tuple2<String, String> handleJsonResponse(Analyze analyze, Tuple2<String, String> result) {
        if (analyze.resType != ResType.Json || isValidJson(result._2)) {
            return result;
        }

        // 修复JSON格式
        return Tuple.of(result._1, repairJson(result._2));
    }


    /*
     * 获取LLM的回答
     * 1. 获取LLM的回答
     * 2. 获取请求LLM的请求id
     */
    private String getLLMAnswer(String response, String key) {
        return Try.of(() -> MAPPER.readTree(response))
                .map(tree -> tree.get(key))
                .map(JsonNode::asText)
                .getOrElse("null");
    }
}