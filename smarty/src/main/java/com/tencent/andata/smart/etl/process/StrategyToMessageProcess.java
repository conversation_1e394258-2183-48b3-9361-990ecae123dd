package com.tencent.andata.smart.etl.process;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.util.Collector;

public class StrategyToMessageProcess extends ProcessFunction<Strategy, Message> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final String tableName = "ods_smarty_strategy_res";
    private final Map<Scene, String> pkMap;
    private final ObjectMapper objectMapper;
    private final RowDataConverter converter;

    public StrategyToMessageProcess(String dbName) throws TableNotExistException {
        this.pkMap = new HashMap<Scene, String>() {{
            put(Scene.WebIM, "value_of_primary_key");
            put(Scene.Ticket, "operation_id");
            put(Scene.Group, "msg_id");
            put(Scene.C2000, "msg_id");
        }};
        this.objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
        // iceberg table rowType
        this.converter = new RowDataConverter(new IcebergCatalogReader().getTableRowType(dbName, tableName), false);
    }

    @Override
    public void processElement(Strategy strategy, Context context, Collector<Message> collector) throws Exception {
        // 过滤掉结果为空的数据
        Analyze[] toRemove = Stream.of(strategy.analyzes)
                .filter(analyze -> analyze.res == null || analyze.res.equalsIgnoreCase("null"))
                .toArray(Analyze[]::new);

        // 无效数据打印日志
        Stream.of(toRemove).forEach(analyze -> {
            analyze.prompt = String.format(analyze.prompt, strategy.chunk.conversation);
            logger.error(String.format("[StrategyToMessageProcess] Analyze res is invalid, Strategy: %s, Analyze: %s", strategy, analyze));
        });

        strategy.analyzes = removeElements(strategy.analyzes, toRemove);

        Message retMessage = new Message();
        retMessage.setSchemaName(tableName);
        retMessage.setMsgType(MessageType.ROW_DATA);
        retMessage.setProcTime(System.currentTimeMillis());
        RowData rowData = buildStrategyRowData(strategy);
        retMessage.setData(ByteBuffer.wrap(converter.serializeRowDataToBytes(rowData)));

        // 模型结果数据上报 zhiyan
        logger.info(String.format("[StrategyToMessageProcess] LLM Result Strategy: %s", strategy));

        collector.collect(retMessage);
    }

    /**
     * 获取结果数据主键
     *
     * @param strategy 策略数据
     * @return 主键
     */
    private String getResPk(Strategy strategy) {
        // 遍历每个trigger的数据，获取数据对应的唯一键进行拼接
        String pks = Arrays.stream(strategy.trigger.data).map(data -> {
            try {
                switch (strategy.scene) {
                    case Ticket:
                        return String.valueOf(data.get(pkMap.get(strategy.scene)).asLong());
                    case WebIM:
                    case Group:
                    case C2000:
                        return data.get(pkMap.get(strategy.scene)).asText();
                    default:
                        return "";
                }
            } catch (Exception e) {
                logger.info(String.format("[getResPk]  Strategy: %s, error:%s", strategy, e));
                return "";
            }
        }).collect(Collectors.joining("-"));
        return String.format("%s-%s", strategy.id, pks);
    }

    /**
     * 生成策略RowData
     *
     * @param strategy 策略
     * @return RowData
     */
    private RowData buildStrategyRowData(Strategy strategy) throws JsonProcessingException {
        GenericRowData res = new GenericRowData(8);
        res.setField(0, StringData.fromString(getResPk(strategy)));
        res.setField(1, StringData.fromString(strategy.name));
        res.setField(2, StringData.fromString(strategy.scene.toString()));
        res.setField(3, StringData.fromString(strategy.sceneIdentify));
        res.setField(4, StringData.fromString(objectMapper.writeValueAsString(strategy.condition)));
        res.setField(5, StringData.fromString(objectMapper.writeValueAsString(strategy.trigger)));
        res.setField(6, StringData.fromString(objectMapper.writeValueAsString(strategy.chunk)));
        res.setField(7, StringData.fromString(objectMapper.writeValueAsString(strategy.analyzes)));
        return res;
    }

    private static Analyze[] removeElements(Analyze[] original, Analyze[] toRemove) {
        return Arrays.stream(original)
                .filter(num -> Arrays.stream(toRemove).noneMatch(remove -> remove == num)) // 过滤掉要移除的元素
                .toArray(Analyze[]::new);
    }
}