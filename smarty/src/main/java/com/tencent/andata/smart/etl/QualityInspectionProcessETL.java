package com.tencent.andata.smart.etl;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.config.AppConfig;
import com.tencent.andata.smart.etl.filter.ModelResultFilter;
import com.tencent.andata.smart.etl.process.QualityStrategyToRowProcess;
import com.tencent.andata.smart.etl.serializer.KafkaStrategyStringDeserializer;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.sink.pg.ExecutionConfig;
import com.tencent.andata.utils.sink.pg.PgSinkBuilder;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/**
 * 质检模型识别结果处理，进行分发入库PG
 */
@Builder
public class QualityInspectionProcessETL {

    private static final String pgTableName = "smarty_quality_inspection";
    public static FlinkLog logger = FlinkLog.getInstance();
    private static String modelResultMQGroup = "mq.kafka.ansmart_model_result";
    private static String smartyDbConfGroup = "cdc.database.pgsql.smarty";
    private RainbowUtils rainbowUtils;

    public void run(FlinkEnvUtils.FlinkEnv flinkEnv) throws Exception {
        // 配置数据库
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(smartyDbConfGroup)
                .build();

        // 获取数据源
        DataStream<Strategy> modelResultStream = getMqSource(flinkEnv);
        // 拆分策略生成质检维度数据
        SingleOutputStreamOperator<Row> qualityDataStream = modelResultStream
                // 过滤质检数据
                .filter(new ModelResultFilter(AppConfig.QualityInspectionConfig))
                .uid("quality_inspection_filter")
                .name("quality_inspection_filter")
                // 处理质检数据
                .process(new QualityStrategyToRowProcess(rainbowUtils))
                .uid("quality_inspection_process")
                .name("quality_inspection_process")
                .setParallelism(5);

        // Sink to PG
        qualityDataStream.addSink(getPgSink(pgDBConf))
                .uid("quality_inspection_sink_to_pg")
                .name("quality_inspection_sink_to_pg")
                .setParallelism(2);

    }


    /**
     * PG Sink
     *
     * @param dbConf 数据库配置
     * @return SinkFunction
     */
    private SinkFunction<Row> getPgSink(DatabaseConf dbConf) {
        ExecutionConfig execConf = ExecutionConfig.builder()
                .batchSize(30)
                .maxRetries(5)
                .batchIntervalMs(5000)
                .build();

        // 使用PgSinkBuilder创建PG Sink
        // 排除不需要写入数据库的列（自动生成或由数据库管理的列）
        return PgSinkBuilder.builder()
                .fromDatabaseConf(dbConf)
                .executionConfig(execConf)
                .loadSchemaFromDatabase(pgTableName)
                .build();
    }

    /**
     * 获取模型识别结果的MQ数据
     *
     * @param flinkEnv FlinkEnv
     * @return DataStream<Strategy>
     */
    public DataStream<Strategy> getMqSource(FlinkEnvUtils.FlinkEnv flinkEnv) {
        // 使用质检的consumer
        String consumerGroup = rainbowUtils.getStringValue(modelResultMQGroup, "QUALITY_CONSUMER");
        String entryPoint = rainbowUtils.getStringValue(modelResultMQGroup, "BROKERS");
        String topic = rainbowUtils.getStringValue(modelResultMQGroup, "TOPICS");

        KafkaSource<Strategy> source = KafkaSource.<Strategy>builder()
                .setBootstrapServers(entryPoint)
                .setTopics(topic)
                // 从消费组提交offset开始消费，不存在则从最新的消费
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setGroupId(consumerGroup)
                .setDeserializer(new KafkaStrategyStringDeserializer())
                .setProperties(new Properties() {{
                    // 在CK时Commit数据
                    setProperty("commit.offsets.on.checkpoint", "true");
                    setProperty("enable.auto.commit", "false");
                    setProperty("fetch.max.wait.ms", "10000");
                }})
                .build();

        return flinkEnv.env()
                .fromSource(source, WatermarkStrategy.noWatermarks(), "model_result_mq_source")
                .uid("model_result_mq_source")
                .name("model_result_mq_source")
                .setParallelism(4);
    }
}