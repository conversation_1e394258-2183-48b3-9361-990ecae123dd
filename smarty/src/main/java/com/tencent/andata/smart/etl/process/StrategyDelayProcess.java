package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.concurrent.TimeUnit;

/**
 * 策略延迟处理器
 *
 * 功能：判断Strategy.Chunk.SpliceType，如果是CallCenterQualityInspection则2小时后将数据发往下游算子，
 * 否则直接发往下游算子。
 *
 * 应用位置：triggeredStrategy之后，contextStrategy之前
 */
public class StrategyDelayProcess extends KeyedProcessFunction<String, Strategy, Strategy> {

    private static final FlinkLog logger = FlinkLog.getInstance();

    // 2小时的毫秒数
    private static final long DELAY_TIME_MS = TimeUnit.HOURS.toMillis(2);

    // 存储需要延迟处理的Strategy
    private transient ValueState<Strategy> delayedStrategyState;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化状态
        ValueStateDescriptor<Strategy> delayedStrategyDescriptor = new ValueStateDescriptor<>(
                "delayedStrategy",
                TypeInformation.of(Strategy.class)
        );
        delayedStrategyState = getRuntimeContext().getState(delayedStrategyDescriptor);
    }

    @Override
    public void processElement(Strategy strategy, Context context, Collector<Strategy> collector) throws Exception {
        try {
            // 检查strategy和chunk是否为空
            if (strategy == null || strategy.chunk == null) {
                logger.warn("[StrategyDelayProcess] Strategy or chunk is null, forwarding directly");
                collector.collect(strategy);
                return;
            }

            SpliceType spliceType = strategy.chunk.conversationSpliceType;

            // 判断是否为CallCenterQualityInspection类型
            if (SpliceType.CallCenterQualityInspection.equals(spliceType)) {
                logger.info("[StrategyDelayProcess] CallCenterQualityInspection detected, scheduling 2-hour delay for strategy: " + strategy.id);

                // 存储策略到状态中
                delayedStrategyState.update(strategy);

                // 注册2小时后的定时器
                long delayTime = context.timestamp() + DELAY_TIME_MS;
                context.timerService().registerProcessingTimeTimer(delayTime);

                logger.info("[StrategyDelayProcess] Timer registered for strategy " + strategy.id + " at timestamp: " + delayTime);

            } else {
                // 非CallCenterQualityInspection类型，直接发往下游
                logger.debug("[StrategyDelayProcess] Non-CallCenterQualityInspection type: " + spliceType + ", forwarding directly");
                collector.collect(strategy);
            }

        } catch (Exception e) {
            logger.error("[StrategyDelayProcess] Error processing strategy: " + strategy, e.getMessage());
            // 发生异常时，直接转发数据，避免数据丢失
            collector.collect(strategy);
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Strategy> out) throws Exception {
        try {
            // 获取延迟的策略
            Strategy delayedStrategy = delayedStrategyState.value();

            if (delayedStrategy != null) {
                logger.info("[StrategyDelayProcess] Timer triggered, forwarding delayed strategy: " + delayedStrategy.id);

                // 发送延迟的策略到下游
                out.collect(delayedStrategy);

                // 清理状态
                delayedStrategyState.clear();

                logger.info("[StrategyDelayProcess] Successfully forwarded delayed strategy: " + delayedStrategy.id);
            } else {
                logger.warn("[StrategyDelayProcess] Timer triggered but no delayed strategy found in state");
            }

        } catch (Exception e) {
            logger.error("[StrategyDelayProcess] Error in onTimer at timestamp: " + timestamp, e.getMessage());

            // 尝试清理状态，避免状态泄漏
            try {
                Strategy delayedStrategy = delayedStrategyState.value();
                if (delayedStrategy != null) {
                    logger.warn("[StrategyDelayProcess] Forwarding strategy due to timer error: " + delayedStrategy.id);
                    out.collect(delayedStrategy);
                    delayedStrategyState.clear();
                }
            } catch (Exception cleanupException) {
                logger.error("[StrategyDelayProcess] Error during cleanup", cleanupException.getMessage());
            }
        }
    }
}