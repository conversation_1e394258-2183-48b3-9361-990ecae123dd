package com.tencent.andata.smart.strategy.chunk.source;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import static com.tencent.andata.utils.ExceptionWrapperUtil.function;
import static net.minidev.json.JSONValue.isValidJson;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.model.HbaseTable;
import com.tencent.andata.smart.enums.Scene;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.AsyncTable;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.client.ScanResultConsumer;

@Slf4j
public class HbaseGroupMsgSource implements ConversationSource {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper MAPPER = new ObjectMapper();

    private final AsyncConnection asyncConnection;
    private final Map<Scene, HbaseTable> sceneTableMap;
    private final ExecutorService executorService;

    public HbaseGroupMsgSource(
            AsyncConnection asyncConnection,
            Map<Scene, HbaseTable> sceneTableMap,
            ExecutorService executorService) {
        this.asyncConnection = asyncConnection;
        this.sceneTableMap = sceneTableMap;
        this.executorService = executorService;
    }

    @Override
    public CompletableFuture<String> getOperation(Strategy strategy) {
        HbaseTable table = sceneTableMap.get(strategy.scene);
        if (table == null) {
            logger.error("[HbaseGroupMsgSource] No table configuration for scene: " + strategy.scene);
        }

        assert table != null;
        return getOperationsFromHbase(buildRangeScan(strategy, table), table)
                .thenApply(function(MAPPER::writeValueAsString));
    }

    @SneakyThrows
    private Scan buildRangeScan(Strategy strategy, HbaseTable table) {
        JsonNode data = strategy.trigger.data[0];
        Tuple2<Long, Long> timeRange = getTimeRange(strategy);
        Scan scan = new Scan().setCaching(1000).setBatch(1000);
        String goupId = data.get("group_id").asText();
        scan.withStopRow(String.format("%s-%s", goupId, timeRange.f1).getBytes());
        scan.withStartRow(String.format("%s-%s", goupId, timeRange.f0).getBytes());
        return scan.addColumn(table.getColumnFamily().getBytes(), table.getQualifier().getBytes());
    }

    @SneakyThrows
    private Tuple2<Long, Long> getTimeRange(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        assert isValidJson(data.toString());
        long endTimestamp = data.get("end_time").asLong();
        long startTimestamp = data.get("start_time").asLong();
        return Tuple2.of(startTimestamp, endTimestamp);
    }

    private CompletableFuture<List<JsonNode>> getOperationsFromHbase(Scan scan, HbaseTable table) {
        AsyncTable<ScanResultConsumer> asyncTable = asyncConnection.getTable(
                TableName.valueOf(table.getTableName()),
                executorService
        );

        return asyncTable.scanAll(scan).thenApply(results -> {
            List<JsonNode> jsonNodes = new ArrayList<>();
            results.forEach(r -> Arrays.stream(r.rawCells())
                    .forEach(consumer(c -> jsonNodes.add(MAPPER.readTree(CellUtil.cloneValue(c))))));
            return jsonNodes;
        });
    }
}