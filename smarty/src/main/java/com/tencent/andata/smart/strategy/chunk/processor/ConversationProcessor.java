package com.tencent.andata.smart.strategy.chunk.processor;

import static com.tencent.andata.smart.enums.TicketServiceChannel.CALLCENTER;
import static com.tencent.andata.smart.enums.TicketServiceChannel.fromId;
import static com.tencent.andata.utils.IterableUtils.getElementsWithContextParallel;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.provider.ChunkContextProvider;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.utils.CallCenterInfoUtils;
import com.tencent.andata.smart.utils.SpliceUtils;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import com.tencent.andata.utils.TimeUtil;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * 对话处理器 - 负责处理各种场景下的消息上下文提取和分析
 * <p>
 * 主要功能：
 * 1. 处理群消息、WebIM、工单等不同场景的数据
 * 2. 根据策略配置提取相关的上下文信息
 * 3. 对消息流水进行智能截取和排序
 * </p>
 * 重构优化记录 (v2.0)：
 * <p>
 * 性能优化：
 * - 减少JSON字段访问次数：从原来的多次get()调用优化为一次预处理
 * - 优化遍历次数：从3次独立遍历合并为1次预处理 + 函数式操作
 * - 使用vavr的lazy evaluation：避免不必要的计算
 * - 预编译正则表达式：避免重复编译开销
 * </p>
 * 代码质量提升：
 * - 引入MessageData封装类：提供类型安全的数据访问
 * - 函数职责拆分：大函数拆分为多个单一职责的小函数
 * - 使用vavr函数式编程：Option、Try等提供更安全的空值和异常处理
 * - 符合SOLID原则：每个方法职责单一，便于测试和维护
 * <p>
 * 错误处理增强：
 * - 全局异常捕获：防止单个消息解析失败影响整个流程
 * - Option类型：明确表示可能失败的操作
 * - 优雅降级：出错时返回空列表而不是抛出异常
 * - 详细日志记录：便于问题排查和监控
 * </p>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025.05.23
 */
@Slf4j
public class ConversationProcessor {

    // 电销工单的 fact_assign 为 5107, 5531, 8974, 6939
    private static final Set<Integer> pureTeleAssigns = new HashSet<>(Arrays.asList(5107, 5531, 8974, 6939));
    private static String REGEXPATTERN = "^\\d{2}-\\d{2} \\d{2}:\\d{2}";
    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private final SpliceUtils spliceUtils = new SpliceUtils();

    /**
     * 在不超过最大大小的情况下追加字符串
     */
    private static StringBuilder appendWithSizeLimit(StringBuilder sb, String str, long maxSize) {
        return Option.when(
                sb.length() + str.length() <= maxSize
                        && !sb.toString().contains(str.replaceAll(REGEXPATTERN, "")),
                () -> sb.append(str)
        ).getOrElse(sb);
    }

    /**
     * 安全提取消息数据
     * <p>
     * 使用vavr的Try.of()和Option来处理可能的异常情况：
     * 1. JSON节点可能不存在指定字段（返回None）
     * 2. 字段类型转换可能失败（如字符串无法转换为Long）
     * 3. JsonNode本身可能为null
     * </p>
     * 函数式编程优势：
     * - 避免传统的try-catch嵌套，代码更简洁
     * - 使用Option类型明确表示可能失败的操作
     * - 便于后续的链式调用和过滤操作
     *
     * @param node 原始JSON节点
     * @return Option<MessageData> 成功时返回Some(MessageData)，失败时返回None
     */
    private Option<MessageData> extractMessageData(JsonNode node) {
        return Try.of(() -> new MessageData(
                node.get("msg_time").asLong(),        // 可能抛出NPE或NumberFormatException
                node.get("display_content").asText(), // 可能抛出NPE
                node
        )).toOption(); // 将Try转换为Option，异常时返回None
    }

    /**
     * 查找引用消息
     * <p>
     * 在消息列表中查找匹配指定正则表达式的引用/回复消息。
     * 引用消息的格式通常为："这是一条引用/回复消息：\n[原始内容]"
     * </p>
     * 实现细节：
     * 1. 使用vavr.collection.List提供更丰富的函数式操作
     * 2. find()方法返回第一个匹配的元素，符合业务需求（通常只有一条引用消息）
     * 3. 正则匹配确保找到正确的引用消息格式
     * <p>
     * 性能考虑：
     * - 使用find()而不是filter().head()，找到第一个匹配项后立即停止遍历
     * - 正则表达式在外部预编译，避免重复编译开销
     * </p>
     *
     * @param messages 预处理后的消息数据列表
     * @param pattern 预编译的正则表达式模式
     * @return Option<MessageData> 找到引用消息时返回Some，否则返回None
     */
    private Option<MessageData> findReferenceMessage(List<MessageData> messages, Pattern pattern) {
        return io.vavr.collection.List.ofAll(messages)
                .find(msg -> pattern.matcher(msg.getDisplayContent()).find());
    }

    /**
     * 查找最早的开始时间
     * <p>
     * 业务逻辑：
     * 1. 在引用消息中包含了多条历史消息的内容
     * 2. 需要找到这些历史消息中最早的那条作为上下文起始点
     * 3. 确保上下文窗口能够完整覆盖相关的对话历史
     * </p>
     * 算法步骤：
     * 1. 过滤出时间早于或等于引用消息的所有消息
     * 2. 在这些消息中找到内容被引用消息包含的消息（即被引用的原始消息）
     * 3. 取这些被引用消息中时间最早的作为起始时间
     * 4. 如果找不到匹配的子消息，则使用引用消息本身的时间作为fallback
     * <p>
     * 函数式编程优势：
     * - 链式调用清晰表达了业务逻辑的每个步骤
     * - minBy()提供了安全的最小值查找，避免空集合异常
     * - getOrElse()提供了优雅的默认值处理
     * </p>
     *
     * @param messages 所有消息数据
     * @param referenceMsg 引用消息
     * @return 最早的开始时间戳
     */
    private Long findEarliestStartTime(List<MessageData> messages, MessageData referenceMsg) {
        return io.vavr.collection.List.ofAll(messages)
                .filter(msg -> msg.getMsgTime() < referenceMsg.getMsgTime()) // 只考虑引用消息之前的消息
                .filter(msg ->
                        referenceMsg.getDisplayContent().contains(msg.getDisplayContent())
                                && !referenceMsg.getDisplayContent().equals(msg.getDisplayContent())
                ) // 内容被引用消息包含
                .maxBy(MessageData::getMsgTime) // 找到时间最新的消息
                .map(MessageData::getMsgTime)   // 提取时间戳
                .getOrElse(referenceMsg.getMsgTime()); // 如果找不到，使用引用消息的时间
    }

    /**
     * 过滤指定时间之后的消息
     * <p>
     * 根据业务规则，需要截取起始时间点之后的所有消息作为分析上下文。
     * 这些消息包含了从问题提出到当前时刻的完整对话流程。
     * </p>
     * 实现要点：
     * 1. 使用>= 而不是>，确保起始时间点的消息也被包含
     * 2. 最终返回原始的JsonNode列表，保持与现有API的兼容性
     * 3. toJavaList()确保返回的是可变的Java List，便于后续处理
     * <p>
     * 函数式编程的优势：
     * - filter操作是lazy的，只在需要时计算
     * - map操作提供了类型安全的转换
     * - 整个操作链是immutable的，避免副作用
     * </p>
     *
     * @param messages 所有消息数据
     * @param startTime 起始时间戳
     * @return 过滤后的JsonNode列表
     */
    private List<JsonNode> filterMessagesAfterTime(List<MessageData> messages, Long startTime) {
        return io.vavr.collection.List.ofAll(messages)
                .filter(msg -> msg.getMsgTime() >= startTime) // 保留起始时间之后的消息
                .map(MessageData::getOriginalNode)             // 转换回原始JsonNode
                .toJavaList();                                 // 转换为Java List
    }

    /**
     * 生成上下文
     */
    private String buildContext(List<JsonNode> itemList, Strategy strategy) {
        return io.vavr.collection.List
                .ofAll(getElementsWithContextParallel(itemList))
                .map(item -> buildItemContext(item, strategy))
                .filter(Option::isDefined)
                .map(Option::get)
                .foldLeft(new StringBuilder(), (sb, itemStr) -> appendWithSizeLimit(sb, itemStr, strategy.chunk.maxSize))
                .toString()
                .trim();
    }

    /**
     * 处理单个item的上下文
     */
    private Option<String> buildItemContext(ElementContext<JsonNode> item, Strategy strategy) {
        return Option.of(spliceUtils.execute(item, strategy));
    }

    public CompletableFuture<String> process(Strategy strategy, ChunkContextProvider provider) throws Exception {
        return provider.getContext(strategy)
                .thenApply(contextStr -> processContext(contextStr, strategy, provider));
    }

    private String processContext(String ctx, Strategy strategy, ChunkContextProvider provider) {
        String providerName = provider.getClass().getSimpleName();
        return Match(provider.getClass().getSimpleName())
                .of(
                        // 如果是 RetryStrategyContextProvider，直接返回原始字符串，上下文信息之前已拼接过
                        Case($("RetryStrategyContextProvider"), () -> ctx),
                        Case($(), () -> Try.of(() -> MAPPER.readTree(ctx))
                                .onFailure(e -> logger.error(String.format("Failed to parse context string: %s, error: %s", ctx, e)))
                                .map(contextNode -> Match(providerName)
                                        .of(
                                                Case($("CompositeChunkContextProvider"), () -> processCompositeContext(contextNode, strategy)),
                                                Case($("HbaseGroupMsgChunkContextProvider"), () -> processSimpleContext(contextNode, strategy)),
                                                Case($("HbaseOperationChunkContextProvider"), () -> processSimpleContext(contextNode, strategy)),
                                                Case($(), () -> "Unknown provider type: " + providerName)
                                        )
                                ).getOrElse("not support context type")
                        )
                );
    }

    private String processCompositeContext(JsonNode ctxNode, Strategy strategy) {
        List<JsonNode> groupNodes = new ArrayList<>();
        List<JsonNode> operationNodes = new ArrayList<>();

        // 收集消息和操作数据
        // 使用 Try 安全地解析和处理 JSON 数据
        Try.of(() -> MAPPER.readTree(ctxNode.get("msg_data").asText()))
                .onFailure(e -> logger.error("解析msg_data失败，err_msg: " + e))
                .forEach(data -> data.forEach(groupNodes::add));

        Try.of(() -> MAPPER.readTree(ctxNode.get("operation_data").asText()))
                .onFailure(e -> logger.error("解析operation_data失败，err_msg: " + e))
                .peek(data -> handleTicketScene(data, strategy))
                .forEach(data -> data.forEach(operationNodes::add));

        List<JsonNode> snippedGroupMsgNodes = snipperGroupMsg(groupNodes, strategy);

        if (snippedGroupMsgNodes.isEmpty()) {
            return "群消息未引用建单标题";
        }

        // 合并两个列表
        List<JsonNode> nodes = ListUtils.union(operationNodes, snippedGroupMsgNodes);
        // 组合模式下，按时间排序
        // 群消息 - 取msg_time
        // 工单流水 - 取operate_time
        nodes.sort(createCompositeComparator());
        return buildContext(nodes, strategy);
    }

    private String processSimpleContext(JsonNode contextNode, Strategy strategy) {
        List<JsonNode> nodes = new ArrayList<>();
        if (contextNode.isArray()) {
            handleTicketScene(contextNode, strategy);
            addNodesToList(contextNode, nodes);
        }

        // 将CallCenter信息数据合并到节点列表中
        fetchCallCenterInfoForTicket(strategy)
                .peek(node -> mergeCallCenterData(node, nodes));

        if (strategy.name.equals("WebIM结单质检") && shouldFilterPureTeleSales(nodes)) {
            return "纯电销数据";
        }

        // 对流水数据进行排序
        nodes.sort(createSimpleComparator(strategy.scene));
        return buildContext(nodes, strategy);
    }

    // 工单场景，需要添加 data_type 字段
    // 因为：工单流水是直接通过CDC同步到HBase的，没有 data_type 字段
    // 后续的拼接逻辑需要根据 data_type 字段来判断
    private void handleTicketScene(JsonNode contextNode, Strategy strategy) {
        if (strategy.scene == Scene.Ticket) {
            contextNode.forEach(context ->
                    ((ObjectNode) context).put("data_type", "ticket_operation"));
        }
    }

    /**
     * 为工单场景获取CallCenter信息数据
     * <p>
     * 业务逻辑：
     * 1. 首先检查策略场景是否为工单场景(Scene.Ticket)，如果不是则直接返回null
     * 2. 从策略的触发数据中获取第一个数据节点
     * 3. 检查该节点的service_channel字段是否为CALLCENTER
     * 4. 如果是CALLCENTER渠道，则提取ticket_id字段作为CallCenter信息的查询条件
     * 5. 调用CallCenterInfoUtils.getCallCenterInfo()方法获取对应的CallCenter信息
     * </p>
     * 函数式编程优势：
     * - 使用Option进行空安全处理，避免NPE
     * - 链式调用清晰表达业务逻辑
     * - filter和map操作确保只有在满足条件时才继续执行
     *
     * @param strategy 策略对象，包含场景信息和触发数据
     * @return JsonNode CallCenter信息数据，如果不满足条件则返回null
     */
    private Option<JsonNode> fetchCallCenterInfoForTicket(Strategy strategy) {
        if (strategy.scene != Scene.Ticket) {
            return Option.none();
        }

        return Option.of(strategy.trigger.data[0])
                .filter(data -> CALLCENTER == fromId(data.get("service_channel").asInt()))
                .map(data -> data.get("ticket_id").asLong())
                .map(CallCenterInfoUtils::getCallCenterInfo);
    }

    /**
     * 合并CallCenter数据到目标节点列表
     * <p>
     * 功能说明：
     * 1. 将CallCenter信息数据(jsonNode)合并到目标节点列表(nodes)中
     * 2. 支持处理多种JSON格式：
     * - JSON数组：转换为流处理
     * - JSON对象：作为单个元素处理
     * - 其他格式：忽略不处理
     * </p>
     * 实现细节：
     * 1. 使用Option安全处理可能为空的输入
     * 2. 使用模式匹配(Match)处理不同JSON格式
     * 3. 最终将所有有效节点添加到目标列表
     *
     * @param jsonNode CallCenter信息数据，可能是数组、对象或其他格式
     * @param nodes 目标节点列表，合并后的数据将添加到此列表
     */
    private void mergeCallCenterData(JsonNode jsonNode, List<JsonNode> nodes) {
        Option.of(jsonNode)
                .map(node -> Match(node).of(
                        Case($(JsonNode::isArray), (JsonNode n) -> StreamSupport.stream(n.spliterator(), false)),
                        Case($(JsonNode::isObject), (JsonNode n) -> Stream.of(n)),
                        Case($(), (JsonNode n) -> Stream.<JsonNode>empty())
                ))
                .getOrElse(() -> Stream.<JsonNode>empty())
                .filter(n -> n.has("operate_time"))
                .forEach(nodes::add);
    }

    private void addNodesToList(JsonNode contextNode, List<JsonNode> nodes) {
        contextNode.forEach(nodes::add);
    }

    /*
     * 组合模式下（群消息和工单流水组合），需要根据时间排序
     * 根据消息类型，创建时间比较器 1. 群消息，取msg_time 2. 工单流水，取operate_time
     */
    private Comparator<JsonNode> createCompositeComparator() {
        return Comparator.comparing(node ->
                Option.of(node.get("msg_time"))
                        .map(JsonNode::asLong)
                        .getOrElse(() -> Option
                                .of(node.get("operate_time"))
                                .map(JsonNode::asText)
                                .map(TimeUtil::getEpochMilli)
                                .getOrElse(Long.MAX_VALUE)));
    }

    /*
     * 简单场景下，需要根据 Strategy 的 scene 场景来匹配排序规则
     * 1. 工单流水，取operation_id
     * 2. 群消息，取msg_time
     * 3.webim流水，取record_update_time
     */
    private Comparator<JsonNode> createSimpleComparator(Scene scene) {
        return Comparator.comparing(n -> getSceneTimestamp(n, scene));
    }

    private Long getSceneTimestamp(JsonNode node, Scene scene) {
        return Match(scene).of(
                Case($(Scene.WebIM), () -> Long.parseLong(node.get("value_of_primary_key").asText().split("\\|")[0])),
                Case($(Scene.Ticket), () -> TimeUtil.getEpochMilli(node.get("operate_time").asText())),
                Case($(Scene.Group), () -> TimeUtil.getEpochMilli(node.get("msg_time").asText())),
                Case($(Scene.C2000), () -> TimeUtil.getEpochMilli(node.get("msg_time").asText())),
                Case($(), () -> Long.MAX_VALUE)
        );
    }

    private boolean shouldFilterPureTeleSales(List<JsonNode> nodes) {
        // WebIM结单质检场景下，剔除纯电销数据
        Set<Integer> factAssignSet = nodes
                .stream()
                .map(node -> node.get("fact_assign").asInt())
                .filter(factAssign -> factAssign != 0)
                .collect(Collectors.toSet());

        // 如果去重后的factAssigns中，包含纯电销的factAssigns，则剔除
        return pureTeleAssigns.containsAll(factAssignSet);
    }

    /**
     * 群消息场景数据截取处理
     * <p>
     * 业务背景：
     * 在群消息场景中，需要基于工单建单标题来确定分析的上下文窗口。
     * 当用户在群里引用/回复某条消息并创建工单时，需要提取从被引用消息开始到当前的完整对话上下文。
     * </p>
     * 重构优化点：
     * 1. 性能优化：减少JSON字段访问次数，从3次遍历优化为1次预处理 + 函数式操作
     * 2. 代码结构：拆分大函数为多个职责单一的小函数，提高可读性和可测试性
     * 3. 错误处理：使用Option和Try提供更安全的空值和异常处理
     * 4. 类型安全：引入MessageData封装避免直接操作JsonNode的风险
     * <p>
     * 算法流程：
     * 1. 从策略中提取工单标题
     * 2. 构建正则表达式匹配引用消息格式
     * 3. 预处理所有消息，提取关键字段并过滤无效数据
     * 4. 查找包含工单标题的引用消息
     * 5. 在引用消息中找到最早的被引用消息作为起始点
     * 6. 返回从起始点到当前的所有消息
     * </p>
     * 错误处理策略：
     * - 任何步骤出现异常都会被捕获并记录
     * - 返回空列表而不是抛出异常，保证系统稳定性
     * - 日志记录便于问题排查和监控
     *
     * @param groupMsgItems 原始群消息列表
     * @param strategy 分析策略，包含触发数据和工单信息
     * @return 截取后的消息列表，如果处理失败则返回空列表
     */
    private List<JsonNode> snipperGroupMsg(List<JsonNode> groupMsgItems, Strategy strategy) {
        // 步骤1: 提取工单标题
        // 从策略的触发数据中获取工单标题，这个标题将用于匹配群消息中的引用内容
        String title = strategy.trigger.data[0].get("title").asText();

        // 步骤2: 构建引用消息匹配正则表达式
        // 引用消息的固定格式："这是一条引用/回复消息：\n[任意内容][工单标题]"
        // Pattern.quote()确保标题中的特殊字符被正确转义
        String regex = "^这是一条引用/回复消息：\\n[\\s\\S]*?" + Pattern.quote(title) + "$";
        Pattern pattern = Pattern.compile(regex);

        // 步骤3: 预处理消息数据（性能优化的关键步骤）
        // 一次性提取所有消息的关键字段，避免后续操作中重复的JSON解析
        // 使用Option过滤掉解析失败的消息，保证数据质量
        List<MessageData> messages = groupMsgItems.stream()
                .map(this::extractMessageData)           // 安全提取消息数据，失败时返回None
                .filter(Option::isDefined)               // 过滤掉解析失败的消息
                .map(Option::get)                        // 提取有效的MessageData
                .collect(Collectors.toList());

        // 步骤4-6: 函数式处理流程
        // 使用函数式编程的链式调用，清晰表达业务逻辑
        return findReferenceMessage(messages, pattern)  // 查找包含工单标题的引用消息
                .map(referenceMsg -> {                      // 如果找到引用消息，继续处理
                    // 步骤5: 确定上下文起始时间
                    // 在引用消息的内容中查找最早的被引用消息，作为分析窗口的起始点
                    Long startTime = findEarliestStartTime(messages, referenceMsg);

                    // 步骤6: 截取目标消息范围
                    // 返回从起始时间到当前的所有消息，构成完整的分析上下文
                    return filterMessagesAfterTime(messages, startTime);
                })
                .getOrElse(Collections.emptyList());        // 如果未找到引用消息，返回空列表
    }

    /**
     * 消息数据封装类，用于缓存解析后的JSON字段
     * <p>
     * 设计目的：
     * 1. 避免重复的JSON字段访问操作（每次调用node.get()都有一定开销）
     * 2. 提供类型安全的数据访问，避免直接操作JsonNode可能出现的类型错误
     * 3. 将数据结构和业务逻辑分离，提高代码的可读性和可维护性
     * 4. 支持函数式编程模式，便于使用vavr进行链式操作
     * </p>
     * 性能优化：
     * - 一次解析，多次使用：避免在多个方法中重复解析相同的JSON字段
     * - 内存友好：只存储必要的字段，原始JsonNode仅在最终结果时使用
     */
    private static class MessageData {

        private final Long msgTime;           // 消息时间戳，用于时间排序和过滤
        private final String displayContent;  // 消息显示内容，用于正则匹配和内容查找
        private final JsonNode originalNode;  // 原始JSON节点，用于最终结果返回

        public MessageData(Long msgTime, String displayContent, JsonNode originalNode) {
            this.msgTime = msgTime;
            this.displayContent = displayContent;
            this.originalNode = originalNode;
        }

        public Long getMsgTime() {return msgTime;}

        public String getDisplayContent() {return displayContent;}

        public JsonNode getOriginalNode() {return originalNode;}
    }
}