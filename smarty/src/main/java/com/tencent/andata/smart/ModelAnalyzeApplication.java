package com.tencent.andata.smart;

import com.tencent.andata.smart.config.AppConfig;
import com.tencent.andata.smart.etl.SmartyModelAnalyzeETL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.java.utils.ParameterTool;

public class ModelAnalyzeApplication {

    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        AppConfig app = AppConfig.getAppByParams(parameterTool.get("app", "Quality"));
        String isSync = parameterTool.get("isSync", "false");
        final SmartyModelAnalyzeETL etl = SmartyModelAnalyzeETL.builder()
                .rainbowUtils(rainbowUtils)
                .dbName(parameterTool.get("dbName"))
                .rateLimit(parameterTool.getLong("rateLimit", 40L))
                .windowSize(parameterTool.getLong("windowSize", 10000L))
                .slideStep(parameterTool.getLong("slideStep", 5000L))
                .batchSize(parameterTool.getInt("batchSize", 20))
                .isSync(isSync.equals("true"))
                .appConfig(app)
                .build();
        etl.run(flinkEnv);
        flinkEnv.env().execute("Smarty Model Analyze");
    }
}