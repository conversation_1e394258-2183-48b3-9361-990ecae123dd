package com.tencent.andata.smart;

import com.tencent.andata.smart.etl.Pg2StarRocksETL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import java.util.Properties;

public class Pg2StarRocksApplication {

    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        final Pg2StarRocksETL etl = Pg2StarRocksETL.builder()
                .rainbowUtils(rainbowUtils)
                .build();
        etl.run(flinkEnv);
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Smarty Pg2starrocks start");
    }
}