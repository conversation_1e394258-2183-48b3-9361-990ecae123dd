package com.tencent.andata.smart.etl.repository.imp;

import com.tencent.andata.smart.etl.domain.TicketInfo;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.tools.ValidationException;

@Slf4j
public class JdbcTicketRepository implements TicketRepository, Serializable {

    private final HashMapJDBCLookupQuery ticketQuery;
    private final HashMapJDBCLookupQuery convQuery;

    public JdbcTicketRepository(DatabaseConf dataWareConf) throws ValidationException {
        this.ticketQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .tableName("dwm_ticket_statistic")
                        .selectFieldWithAlias(getFieldMappings())
                        .conditionKeyList(Collections.singletonList("ticket_id"))
                        .limit(1)
        );

        this.convQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .tableName("dwm_ticket_statistic")
                        .selectField(Collections.singletonList("ticket_id"))
                        .conditionKeyList(Collections.singletonList("conversation_id"))
                        .limit(1)
        );
    }

    @Override
    public Option<TicketInfo> findById(Long ticketId) {
        return Option.of(ticketId)
                .flatMap(this::queryDbByTicketId);
    }

    @Override
    public Option<Long> findByConvId(String conversationId) {
        return Option.of(conversationId)
                .flatMap(this::queryDbByConvId);
    }

    private Option<TicketInfo> queryDbByTicketId(Long ticketId) {
        return Try.of(() -> {
            HashMap<String, Object> params = new HashMap<>();
            params.put("ticket_id", ticketId);

            return Option.of(ticketQuery.query(params))
                    .filter(results -> !results.isEmpty())
                    .map(results -> results.get(0))
                    .map(result ->
                            TicketInfo.builder()
                                    .serviceSceneLevel1Name((String) result.get("service_scene_level1_name"))
                                    .serviceSceneLevel2Name((String) result.get("service_scene_level2_name"))
                                    .serviceSceneLevel3Name((String) result.get("service_scene_level3_name"))
                                    .serviceSceneLevel4Name((String) result.get("service_scene_level4_name"))
                                    .currentOperator((String) result.get("current_operator"))
                                    .serviceChannel((String) result.get("service_channel"))
                                    .customerName((String) result.get("customer_name"))
                                    .responsible((String) result.get("responsible"))
                                    .uin(Long.valueOf(result.get("uin").toString()))
                                    .factAssign((String) result.get("fact_assign"))
                                    .companyId((String) result.get("company_id"))
                                    .priority((String) result.get("priority"))
                                    .question((String) result.get("question"))
                                    .post((String) result.get("current_post"))
                                    .title((String) result.get("title"))
                                    .url((String) result.get("url"))
                                    .build())
                    .getOrElse(TicketInfo.builder().build());
        }).toOption();
    }

    private Map<String, String> getFieldMappings() {
        return new HashMap<String, String>() {{
            put("service_scene_level1_name", "service_scene_level1_name");
            put("service_scene_level2_name", "service_scene_level2_name");
            put("service_scene_level3_name", "service_scene_level3_name");
            put("service_scene_level4_name", "service_scene_level4_name");
            put("current_operator", "current_operator");
            put("service_channel", "service_channel");
            put("customer_name", "customer_name");
            put("current_post", "current_post");
            put("responsible", "responsible");
            put("fact_assign", "fact_assign");
            put("company_id", "company_id");
            put("priority", "priority");
            put("question", "question");
            put("title", "title");
            put("url", "url");
            put("uin", "uin");
        }};
    }


    private Option<Long> queryDbByConvId(String conversationId) {
        return Try.of(() -> {
            HashMap<String, Object> params = new HashMap<>();
            params.put("conversation_id", conversationId);

            return Option.of(convQuery.query(params))
                    .map(results -> results.get(0))
                    .map(result -> (Long) result.get("ticket_id"))
                    .getOrElse(0L);
        }).toOption();
    }

    public void open() {
        Try.run(ticketQuery::open)
                .onFailure(e -> log.error("Failed to open ticket query", e));
    }

    public void close() {
        Try.run(ticketQuery::close)
                .onFailure(e -> log.error("Failed to close ticket query", e));
    }
}