package com.tencent.andata.smart.enums;

import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.Getter;

/**
 * 工单操作类型枚举
 */
@Getter
public enum TicketOperationType {
    CREATE("1", "建单"),
    URGE("4", "催单"),
    TRANSFER("5", "转单"),
    REPLY("8", "回复"),
    CUSTOMER_REPLY("9", "客户回复"),
    TRANSFER_RESPONSIBLE("10", "交接"),
    WAIT_CUSTOMER_ADD_INFO("11", "待客户补充"),
    WAIT_CUSTOMER_CLOSE("15", "待客户确认结单"),
    CLOSE("16", "结单"),
    //APPRAISE("27", "评价"),
    COMPLAINT("31", "投诉");

    private final String code;
    private final String desc;

    TicketOperationType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static TicketOperationType fromCode(String code) {
        return List.of(values())
                   .filter(t -> t.code.equals(code))
                   .getOrNull();
    }
} 