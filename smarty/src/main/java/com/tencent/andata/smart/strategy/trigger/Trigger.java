package com.tencent.andata.smart.strategy.trigger;

import java.io.Serializable;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Trigger implements Serializable {
    public TriggerType type;
    // 触发数据是否需要持久化留存
    public boolean needPersist;
    // 触发的数据，如果是CEP的话可能会有多条
    public JsonNode[] data;
    // 触发时间
    public long triggerTimestamp;

    @Override
    public String toString() {
        return "Trigger{" +
                "type=" + type +
                ", needPersist=" + needPersist +
                ", data=" + Arrays.toString(data) +
                ", triggerTimestamp=" + triggerTimestamp +
                '}';
    }
}