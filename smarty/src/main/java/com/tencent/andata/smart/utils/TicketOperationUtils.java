package com.tencent.andata.smart.utils;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.lookup.TicketOperationQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Try;
import java.util.Properties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class TicketOperationUtils {

    private static final String ERROR_MSG = "TicketOperationUtils get info from db error: %s, ticketId: %s";
    public static FlinkLog logger = FlinkLog.getInstance();
    public static TicketOperationQuery dbQuery;

    static {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        DatabaseConf datawareDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.mysql.work")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        dbQuery = Try.of(() -> {
            TicketOperationQuery query = new TicketOperationQuery(DatabaseEnum.MYSQL, datawareDbConf);
            query.open();
            return query;
        }).getOrElseThrow(e -> new RuntimeException(e));
    }

    public static JsonNode getClosedTicketOperation(Long ticketId) {
        return Try.of(() -> dbQuery.query(ticketId))
                .onFailure(e -> logger.error(String.format(ERROR_MSG, e, ticketId)))
                .getOrNull();
    }
}