package com.tencent.andata.smart.etl.process;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_SINGLE_QUOTES;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_READING_DUP_TREE_KEY;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_TRAILING_TOKENS;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.analyze.ResType;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.gpt.CommonGptApiV2;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.event.RetryEvent;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;
import java.util.function.Supplier;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * LLM处理策略使用串行同步模式分析的Flink处理函数
 * 该类采用了函数式编程和模块化设计，提高了代码的可维护性和扩展性
 */
@Builder
public class StrategyAnalyzeSyncLLMProcess extends ProcessFunction<Strategy, Strategy> {

    private static final FlinkLog LOGGER = FlinkLog.getInstance();

    // 常量定义，使用static final并大写命名

    private static final int HTTP_TIMEOUT_MS = 6000000;
    private static final int MAX_RETRY_ATTEMPTS = 5;
    private static final int RETRY_WAIT_SECONDS = 20;
    private static final String JSON_REPAIR_URL = "http://11.145.81.86:8080/gpt-api/json_repair";

    // 配置参数
    private final String modelUrl;
    private final String modelToken;

    // 组件依赖，使用transient减少序列化开销
    private transient HttpClientUtils httpClient;
    private transient RetryConfig retryConfig;
    private transient JsonProcessor jsonProcessor;
    private transient GenericObjectPool<LLMClient> llmClientPool;

    /**
     * 初始化处理函数
     * 创建必要的依赖组件和配置
     */
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 创建HTTP客户端
        this.httpClient = new HttpClientUtils(HTTP_TIMEOUT_MS, HTTP_TIMEOUT_MS);

        // 创建JSON处理器
        this.jsonProcessor = new JsonProcessor();

        // 配置重试策略
        this.retryConfig = RetryConfig.custom()
                .maxAttempts(MAX_RETRY_ATTEMPTS)
                .waitDuration(Duration.ofSeconds(RETRY_WAIT_SECONDS))
                .build();

        // 配置并创建LLMClient对象池
        LLMClientFactory factory = new LLMClientFactory(modelToken, modelUrl, httpClient, jsonProcessor);
        GenericObjectPoolConfig<LLMClient> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(Runtime.getRuntime().availableProcessors()); // 池大小设置为CPU核心数
        poolConfig.setBlockWhenExhausted(true);
        this.llmClientPool = new GenericObjectPool<>(factory, poolConfig);
    }

    /**
     * 处理输入的策略对象
     * 如果策略有效，则进行处理并收集结果
     */
    @Override
    public void processElement(Strategy strategy, Context context, Collector<Strategy> collector) {
        // 使用Option过滤有效策略并处理
        Option.of(strategy)
                .filter(this::isValidStrategy)
                .peek(this::processStrategy)
                .peek(collector::collect);
    }

    /**
     * 判断策略是否有效
     * 主要检查conversation内容
     */
    private boolean isValidStrategy(Strategy strategy) {
        return Option.of(strategy)
                .map(s -> s.chunk.conversation)
                .filter(StringUtils::isNotEmpty)
                .filter(content -> !"纯电销数据".equals(content))
                .filter(content -> !content.equalsIgnoreCase("null"))
                .isDefined();
    }

    /**
     * 处理策略的分析任务
     * 对每个分析进行LLM处理
     */
    private void processStrategy(Strategy strategy) {
        String conversation = strategy.chunk.conversation;

        Arrays.stream(strategy.analyzes)
                .parallel()  // 并行处理每个分析任务
                .forEach(analyze -> processAnalyze(analyze, conversation, strategy));
    }

    /**
     * 处理单个分析任务
     * 设置参数，执行LLM请求，并应用重试机制
     */
    private void processAnalyze(Analyze analyze, String conversation, Strategy strategy) {
        LLMClient llmClient = null;
        try {
            // 从池中借用LLMClient实例
            llmClient = llmClientPool.borrowObject();
            final LLMClient finalLlmClient = llmClient; // 在lambda中使用的final变量

            // 准备LLM请求
            finalLlmClient.prepareRequest(analyze, conversation, UUID.randomUUID().toString());

            // 创建重试处理器
            RetryHandler retryHandler = new RetryHandler(retryConfig, event ->
                    LOGGER.error(String.format("[StrategyAnalyzeLLMProcess] Retry attempt %d failed. Reason: %s",
                            event.getNumberOfRetryAttempts(), event.getLastThrowable()))
            );

            // 定义LLM处理任务
            Supplier<Void> llmTask = () -> {
                // 执行LLM请求
                String response = finalLlmClient.executeRequest();

                // 提取响应内容
                String id = finalLlmClient.llmApi.requestId;
                Tuple2<String, String> result = finalLlmClient.extractResponse(response);

                // 如果需要，修复JSON格式
                if (analyze.resType == ResType.Json && !jsonProcessor.isValidJson(result._2)) {
                    result = finalLlmClient.repairJson(result);
                }

                // 验证响应有效性
                if (!isValidResponse(result._2)) {
                    throw new LLMProcessException("LLM response is null or empty, requestId: " + result._1);
                }

                // 设置分析结果
                analyze.res = result._2;
                return null;
            };

            // 执行带重试的任务
            Try.of(() -> retryHandler.executeWithRetry(llmTask))
                    .onFailure(e -> handleProcessFailure(strategy, e));

        } catch (Exception e) {
            LOGGER.error(String.format("[StrategyAnalyzeLLMProcess] Failed to process analyze for strategy %s. Error: %s",
                    strategy.name, e.getMessage()));
        } finally {
            // 确保将LLMClient实例归还到池中
            if (llmClient != null) {
                try {
                    llmClientPool.returnObject(llmClient);
                } catch (Exception e) {
                    LOGGER.error(String.format("[StrategyAnalyzeLLMProcess] Could not return llm client to the pool. Error: %s", e.getMessage()));
                }
            }
        }
    }

    /**
     * 检查响应是否有效
     */
    private boolean isValidResponse(String response) {
        return !StringUtils.isEmpty(response)
                && !StringUtils.equalsIgnoreCase(response, "null");
    }

    /**
     * 处理处理失败的情况
     */
    private void handleProcessFailure(Strategy strategy, Throwable e) {
        LOGGER.error(String.format("[StrategyAnalyzeLLMProcess] " +
                "Call to LLM failed after %d retries, strategy: %s, " +
                "errMsg: %s", MAX_RETRY_ATTEMPTS, strategy, e.getMessage()));
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() throws Exception {
        super.close();
        if (llmClientPool != null) {
            try {
                llmClientPool.close();
            } catch (Exception e) {
                LOGGER.error(String.format("Failed to close llmClientPool. Error: %s", e.getMessage()));
            }
        }
        if (httpClient != null) {
            httpClient.close();
        }
    }

    /**
     * LLM客户端对象池的工厂类
     * 负责创建和包装LLMClient实例
     */
    private static class LLMClientFactory extends BasePooledObjectFactory<LLMClient> {

        private final String token;
        private final String url;
        private final HttpClientUtils httpClient;
        private final JsonProcessor jsonProcessor;

        public LLMClientFactory(String token, String url, HttpClientUtils httpClient, JsonProcessor jsonProcessor) {
            this.token = token;
            this.url = url;
            this.httpClient = httpClient;
            this.jsonProcessor = jsonProcessor;
        }

        @Override
        public LLMClient create() {
            return new LLMClient(token, url, httpClient, jsonProcessor);
        }

        @Override
        public PooledObject<LLMClient> wrap(LLMClient client) {
            return new DefaultPooledObject<>(client);
        }

        @Override
        public void destroyObject(PooledObject<LLMClient> p) {
            p.getObject().close();
        }
    }

    /**
     * LLM客户端，负责与LLM API的通信
     * 封装了请求准备、执行和响应处理的逻辑
     */
    private static class LLMClient {

        private final CommonGptApiV2 llmApi;
        private final JsonProcessor jsonProcessor;
        private final HttpClientUtils httpClient;


        LLMClient(String token, String url, HttpClientUtils httpClient, JsonProcessor jsonProcessor) {
            this.httpClient = httpClient;
            this.jsonProcessor = jsonProcessor;
            this.llmApi = new CommonGptApiV2(token, url, "");
        }

        /**
         * 准备LLM请求
         * 设置模型、消息和参数
         */
        void prepareRequest(Analyze analyze, String conversation, String requestId) {
            // 设置模型和参数
            llmApi.setModel(analyze.model);
            llmApi.setExtraParams(analyze.modelExtraParams);

            // 设置消息列表，使用不可变集合避免副作用
            CopyOnWriteArrayList<ConcurrentHashMap<String, Object>> messages = new CopyOnWriteArrayList<>();
            Option.of(analyze.messages).forEach(messages::addAll);
            llmApi.setMessages(messages);

            // 添加用户提示
            ConcurrentHashMap<String, Object> userPrompt = new ConcurrentHashMap<>();
            String formattedPrompt = String.format(analyze.prompt, conversation);
            userPrompt.put("content", formattedPrompt);
            userPrompt.put("role", "user");

            // 移除可能存在的重复提示（避免重试时重复）
            llmApi.messages.remove(userPrompt);
            llmApi.messages.add(userPrompt);
            llmApi.setRequestId(requestId);

            // 构建请求
            llmApi.buildRequestWithRid(formattedPrompt, requestId);
        }

        /**
         * 执行LLM请求
         * 返回原始响应字符串
         */
        String executeRequest() {

            return Try.of(() -> llmApi.requestAndParseSync(httpClient))
                    .getOrElseThrow(e -> new LLMProcessException("Failed to request LLM API", e));
        }

        /**
         * 从LLM响应中提取所需信息
         * 返回请求ID和答案
         */
        Tuple2<String, String> extractResponse(String response) {
            return Try.of(() -> {
                String requestId = extractField(response, "requestId");
                String answer = extractField(response, "answer");
                return Tuple.of(requestId, answer);
            }).getOrElse(Tuple.of("unknown", "null"));
        }

        /**
         * 从JSON响应中提取指定字段
         */
        private String extractField(String response, String fieldName) {
            return Try.of(() -> {
                JsonNode node = jsonProcessor.parseJson(response).get(fieldName);
                return node != null ? node.asText() : "null";
            }).getOrElse("null");
        }

        /**
         * 修复无效的JSON
         */
        Tuple2<String, String> repairJson(Tuple2<String, String> result) {
            return Try.of(() -> {
                String repairedJson = callJsonRepairService(result._2);
                return repairedJson != null ? Tuple.of(result._1, repairedJson) : result;
            }).getOrElse(result);
        }

        /**
         * 调用JSON修复服务
         */
        private String callJsonRepairService(String invalidJson) {
            if (StringUtils.isEmpty(invalidJson)) {
                return null;
            }

            return Try.of(() -> {
                // 准备请求参数
                Map<String, Object> requestBody = new HashMap<>(1);
                requestBody.put("json_data", invalidJson);

                Map<String, String> headers = new HashMap<>(2);
                headers.put("Accept", "application/json");
                headers.put("Content-Type", "application/json;charset=UTF-8");

                // 执行HTTP请求
                String response = httpClient.post(JSON_REPAIR_URL, requestBody, headers);

                // 解析响应
                JsonNode responseNode = jsonProcessor.parseJson(response);
                String convertedData = responseNode.get("data").get("converted_data").asText();

                // 使用JSON解析器自动处理Unicode转义
                JsonNode parsedNode = jsonProcessor.parseJson(convertedData);
                return jsonProcessor.mapper.writeValueAsString(parsedNode);
            }).getOrElseGet(e -> {
                LOGGER.error("JSON repair failed: " + e.getMessage());
                return null;
            });
        }

        /**
         * 关闭资源
         */
        void close() {
            // 如果需要关闭资源，在这里实现
        }
    }

    /**
     * JSON处理器，负责JSON的解析、验证和处理
     */
    public static class JsonProcessor {

        // 配置JSON解析器，禁用不安全特性
        private final ObjectMapper mapper = new ObjectMapper()
                .configure(FAIL_ON_TRAILING_TOKENS, true)  // 确保没有多余的内容
                .configure(FAIL_ON_READING_DUP_TREE_KEY, true)  // 重复键检查
                .configure(ALLOW_SINGLE_QUOTES, false)  // 禁止单引号
                .configure(ALLOW_UNQUOTED_FIELD_NAMES, false);  // 禁止无引号字段名

        /**
         * 解析JSON字符串
         */
        JsonNode parseJson(String jsonStr) {
            return Try.of(() -> mapper.readTree(jsonStr))
                    .getOrElseThrow(e -> new RuntimeException("Failed to parse JSON: " + e.getMessage()));
        }

        /**
         * 验证JSON是否有效
         */
        boolean isValidJson(String jsonStr) {
            if (StringUtils.isEmpty(jsonStr)) {
                return false;
            }

            return Try.of(() -> {
                mapper.readTree(jsonStr);
                return true;
            }).getOrElse(false);
        }
    }

    /**
     * 重试处理器，封装了重试逻辑
     */
    private static class RetryHandler {

        private final Retry retry;

        /**
         * 构造重试处理器
         */
        RetryHandler(RetryConfig config, Consumer<RetryEvent> onRetryListener) {
            this.retry = Retry.of("LLMAPIRetry", config);

            // 注册重试事件监听器
            this.retry.getEventPublisher().onRetry(onRetryListener::accept);
        }

        /**
         * 执行带重试的任务
         */
        <T> T executeWithRetry(Supplier<T> task) {
            return retry.executeSupplier(() -> {
                try {
                    return task.get();
                } catch (Exception e) {
                    throw new RuntimeException("Task execution failed", e);
                }
            });
        }
    }

    /**
     * LLM处理异常类
     */
    private static class LLMProcessException extends RuntimeException {

        LLMProcessException(String message) {
            super(message);
        }

        LLMProcessException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}