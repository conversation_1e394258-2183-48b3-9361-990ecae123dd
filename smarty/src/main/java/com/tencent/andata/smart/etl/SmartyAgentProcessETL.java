package com.tencent.andata.smart.etl;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.config.AppConfig;
import com.tencent.andata.smart.etl.filter.ModelResultFilter;
import com.tencent.andata.smart.etl.process.AgentDataToRowProcess;
import com.tencent.andata.smart.etl.process.AgentResultProcess;
import com.tencent.andata.smart.etl.process.SplitPreStrategyProcess;
import com.tencent.andata.smart.etl.process.TicketPriorityRiseUpProcess;
import com.tencent.andata.smart.etl.process.ToREngineMessageProcess;
import com.tencent.andata.smart.etl.serializer.KafkaREngineMessageSerializer;
import com.tencent.andata.smart.etl.serializer.KafkaStrategyStringDeserializer;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.struct.regnine.REngineMessage;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;
import lombok.Builder;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.SqlTimeTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.clients.producer.ProducerConfig;


@Builder
public class SmartyAgentProcessETL implements Serializable {

    private String rdbTableName;
    private String preStrategyIds;
    private String icebergTableName;
    private String reCheckStrategyIds;
    private transient RainbowUtils rainbowUtils;

    private static FlinkLog logger = FlinkLog.getInstance();
    private static String smartyDbConfGroup = "cdc.database.pgsql.smarty";
    public static final String RENGINE_MSG_SINK_CONF = "mq.kafka.rengine_msg_mq";
    private static final String modelResultMQGroup = "mq.kafka.ansmart_model_result";
    private static final OutputTag<JsonNode> L1AgentStreamTag = new OutputTag<JsonNode>("l1-agent-side-output") {
    };
    private static final ObjectMapper objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);

    private static final RowTypeInfo typeInfo = new RowTypeInfo(
            new TypeInformation[]{
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.LONG_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    SqlTimeTypeInfo.TIMESTAMP, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, SqlTimeTypeInfo.TIMESTAMP,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                    BasicTypeInfo.BOOLEAN_TYPE_INFO, BasicTypeInfo.BOOLEAN_TYPE_INFO,
            },
            new String[]{
                    "pk", "scene", "scene_identify", "ticket_id", "service_scene_level1_name",
                    "service_scene_level2_name", "service_scene_level3_name", "service_scene_level4_name",
                    "post", "status", "create_time", "service_channel", "current_operator", "uin", "event_time",
                    "customer_name", "fact_assign", "url", "responsible", "priority", "title", "duty_responsible",
                    "question", "trigger_time", "operation_id", "problem_summary", "is_consult", "is_obvious", "reason",
                    "reflection_type_classify", "try_to_recovered", "is_mention", "is_first_judge", "is_rise_up"
            }
    );


    public void run(FlinkEnvUtils.FlinkEnv flinkEnv) throws Exception {
        // 获取数据源
        DataStream<Strategy> modelResultStream = getMqSource(flinkEnv);
        // 基于策略数据补充工单数据和其他数据
        SingleOutputStreamOperator<Strategy> agentStream = modelResultStream
                // 过滤Agent数据
                .filter(new ModelResultFilter(AppConfig.AgentAnalyzeConfig))
                // 前置校验策略通过侧输出流分发到L1AgentStream
                .process(new SplitPreStrategyProcess(L1AgentStreamTag, preStrategyIds));

        // 通过侧输出流获取前置校验策略流,需要重新发到接入层MQ
        DataStream<JsonNode> L1AgentStream = agentStream.getSideOutput(L1AgentStreamTag);
        sinkToAccessMQ(L1AgentStream);

        SingleOutputStreamOperator<String> agentResultStream = agentStream
                // 处理解析数据，获取工单信息
                .process(new AgentResultProcess(rainbowUtils, reCheckStrategyIds))
                // 基于工单ID做KeyBy
                .keyBy(a -> a.f0)
                // 计算同个工单的优先级是否上升
                .process(new TicketPriorityRiseUpProcess());

        // 将数据转化成REngineMessage
        SingleOutputStreamOperator<REngineMessage> rEngineStream = agentResultStream
                .process(new ToREngineMessageProcess(icebergTableName));

        // Sink to REngine
        rEngineStream.sinkTo(getREngineKafkaSink())
                .uid("AgentResultSinkToREngine")
                .name("AgentResultSinkToREngine");

        // Sink PG
        // 为什么使用Stream方式Sink PG
        // TableSql方式Sink pg的话， Stream sink kafka则会被屏蔽不生效，并且Kafka sink的序列化方式是REngineMessage，使用Table Sink非常麻烦
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(smartyDbConfGroup)
                .build();

        SingleOutputStreamOperator<Row> resultRowStream = agentResultStream
                .process(new AgentDataToRowProcess());

        resultRowStream.addSink(getPgSink(pgDBConf))
                .uid("AgentResultSinkToPGSql")
                .name("AgentResultSinkToPGSql");

        // Sink Kafka
        // 发送优先级升级消息
        DataStream<TicketPriorityUpgradeMsg> upgradeMsgStream = resultRowStream
                .filter(new TicketPriorityUpgradeFilter())
                .map(new RowToTicketPriorityUpgradeMsgMapper())
                .name("Generate TicketPriorityUpgradeMsg");

        upgradeMsgStream.sinkTo(getUpgradeMsgKafkaSink())
                .uid("AgentResultSinkToKafka")
                .name("AgentResultSinkToKafka");
    }

    private KafkaSink<TicketPriorityUpgradeMsg> getUpgradeMsgKafkaSink() {
        MqConf kafkaConf = MqConf.fromRainbow(rainbowUtils, "mq.kafka.upgrade_msg");
        Properties kafkaProducerProps = new Properties();
        kafkaProducerProps.setProperty("max.request.size", "12582912");
        kafkaProducerProps.setProperty("transaction.timeout.ms", "900000");

        return KafkaSink.<TicketPriorityUpgradeMsg>builder()
                .setBootstrapServers(kafkaConf.getBroker())
                .setKafkaProducerConfig(kafkaProducerProps)
                .setRecordSerializer(KafkaRecordSerializationSchema
                        .builder()
                        .setTopic(kafkaConf.getTopic())
                        .setValueSerializationSchema((SerializationSchema<TicketPriorityUpgradeMsg>)
                                element -> {
                                    try {
                                        return objectMapper.writeValueAsBytes(element);
                                    } catch (Exception e) {
                                        logger.error("Failed to serialize TicketPriorityUpgradeMsg: " + e);
                                        return null;
                                    }
                                })
                        .build())
                .build();
    }


    /**
     * 发送到规则引擎
     */
    private KafkaSink<REngineMessage> getREngineKafkaSink() {
        final String sinkAddr = rainbowUtils.getStringValue(RENGINE_MSG_SINK_CONF, "ENTRYPOINT");
        final String sinkTopic = rainbowUtils.getStringValue(RENGINE_MSG_SINK_CONF, "TOPICS");
        Properties properties = new Properties();
        properties.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, 15 * 60 * 1000);
        properties.put("max.request.size", "12582912");
        return KafkaSink.<REngineMessage>builder()
                .setKafkaProducerConfig(properties)
                .setBootstrapServers(sinkAddr)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema
                                .builder()
                                .setTopic(sinkTopic)
                                .setValueSerializationSchema(new KafkaREngineMessageSerializer())
                                .build())
                .setTransactionalIdPrefix(String.format("AgentResultSink.%d", System.currentTimeMillis()))
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .build();
    }


    public DataStream<Strategy> getMqSource(FlinkEnvUtils.FlinkEnv flinkEnv) {
        String entryPoint = rainbowUtils.getStringValue(modelResultMQGroup, "BROKERS");
        String topic = rainbowUtils.getStringValue(modelResultMQGroup, "TOPICS");
        // 使用agent的consumer
        String consumerGroup = rainbowUtils.getStringValue(modelResultMQGroup, "AGENT_CONSUMER");

        KafkaSource<Strategy> source = KafkaSource
                .<Strategy>builder()
                .setBootstrapServers(entryPoint)
                .setTopics(topic)
                // 从消费组提交offset开始消费，不存在则从最新的消费
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setGroupId(consumerGroup)
                .setDeserializer(new KafkaStrategyStringDeserializer())
                .setProperties(
                        new Properties() {{
                            // 在CK时Commit数据
                            setProperty("commit.offsets.on.checkpoint", "true");
                            setProperty("enable.auto.commit", "false");
                            setProperty("fetch.max.wait.ms", "10000");
                        }})
                .build();

        return flinkEnv
                .env()
                .fromSource(source, WatermarkStrategy.noWatermarks(), "model_agent_result_mq_source")
                .uid("model_agent_result_mq_source")
                .name("model_agent_result_mq_source")
                .setParallelism(2);
    }


    private void sinkToAccessMQ(DataStream<JsonNode> ds) {

        MqConf accessKafka = MqConf.fromRainbow(rainbowUtils, "mq.kafka.ansmart_quality");

        Properties properties = new Properties();
        properties.setProperty("max.request.size", "12582912");
        properties.setProperty("transaction.timeout.ms", "900000");

        KafkaSink<JsonNode> sink = KafkaSink
                .<JsonNode>builder()
                .setKafkaProducerConfig(properties)
                .setBootstrapServers(accessKafka.getBroker())
                .setRecordSerializer(KafkaRecordSerializationSchema
                        .builder()
                        .setTopic(accessKafka.getTopic())
                        .setValueSerializationSchema((SerializationSchema<JsonNode>)
                                element -> {
                                    try {
                                        return objectMapper.writeValueAsBytes(element);
                                    } catch (Exception e) {
                                        logger.error("Failed to serialize strategy: {}",
                                                String.valueOf(element));
                                        return element.toString().getBytes(StandardCharsets.UTF_8);
                                    }
                                })
                        .build())
                .build();

        ds.sinkTo(sink)
                .name("retry strategy sink to access mq")
                .uid("Sink_to_access_mq")
                .setParallelism(1);
    }

    /**
     * PG Sink
     */
    private SinkFunction<Row> getPgSink(DatabaseConf dbConf) {
        return JdbcSink.sink(
                buildInsertSql(),
                (JdbcStatementBuilder<Row>) (statement, row) -> {
                    for (int i = 0; i < typeInfo.getArity(); i++) {
                        statement.setObject(i + 1, row.getField(
                                typeInfo.getFieldNames()[i]
                        ));
                    }
                },
                JdbcExecutionOptions
                        .builder()
                        .withBatchSize(1000)
                        .withBatchIntervalMs(200)
                        .withMaxRetries(5)
                        .build(),
                new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                        .withUrl(String.format(
                                        "jdbc:postgresql://%s:%s/%s",
                                        dbConf.dbHost,
                                        dbConf.dbPort,
                                        dbConf.dbName
                                )
                        )
                        .withDriverName("org.postgresql.Driver")
                        .withUsername(dbConf.userName)
                        .withPassword(dbConf.password)
                        .build()
        );
    }

    /**
     * 构造Insert SQL，冲突以最新的为准
     */
    private String buildInsertSql() {
        String fieldStr = String.join(",", typeInfo.getFieldNames());
        String valueStr = Arrays.stream(typeInfo.getFieldNames()).map(a -> "?").collect(Collectors.joining(","));
        String conflictStr = Arrays.stream(typeInfo.getFieldNames()).map(a -> String.format(" %s = EXCLUDED.%s", a, a))
                .collect(Collectors.joining(","));

        return String.format(
                "INSERT INTO %s (%s) values (%s) ON CONFLICT (pk) DO UPDATE SET %s;",
                rdbTableName,
                fieldStr,
                valueStr,
                conflictStr
        );
    }

    public static class TicketPriorityUpgradeMsg {

        public long TicketId;
        public String Priority;

        public TicketPriorityUpgradeMsg(long ticketId, String priority) {
            this.TicketId = ticketId;
            this.Priority = priority;
        }
    }

    public static class TicketPriorityUpgradeFilter implements FilterFunction<Row> {

        @Override
        public boolean filter(Row row) throws Exception {
            // select * from ticket_priority_agent_judge where is_rise_up = 1 and is_mention = '是' and status != '已结单'
            Integer isRiseUp = (Integer) row.getField("is_rise_up");
            String isMention = (String) row.getField("is_mention");
            String status = (String) row.getField("status");
            return isRiseUp.equals(1)
                    && "是".equals(isMention)
                    && !"已结单".equals(status);
        }
    }

    public static class RowToTicketPriorityUpgradeMsgMapper implements MapFunction<Row, TicketPriorityUpgradeMsg> {

        private static final String L2_PRIORITY = "L2";
        private static final String L3_PRIORITY = "L3";
        private static final String DEFAULT_PRIORITY = "0"; // 默认设置为 L2级

        private static final Map<String, String> priorityMap = new HashMap<>();

        static {
            priorityMap.put(L2_PRIORITY, "0");
            priorityMap.put(L3_PRIORITY, "1");
        }

        @Override
        public TicketPriorityUpgradeMsg map(Row row) throws Exception {
            long ticketId = ((Number) row.getField("ticket_id")).longValue();
            String reflectionTypeClassify = (String) row.getField("reflection_type_classify");

            // 根据业务逻辑确定 Priority 的值
            String priority = priorityMap.getOrDefault(reflectionTypeClassify, DEFAULT_PRIORITY);

            return new TicketPriorityUpgradeMsg(ticketId, priority);
        }
    }
}