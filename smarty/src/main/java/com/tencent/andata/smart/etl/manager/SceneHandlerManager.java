package com.tencent.andata.smart.etl.manager;

import com.tencent.andata.smart.etl.handler.C2000SceneHandler;
import com.tencent.andata.smart.etl.handler.GroupSceneHandler;
import com.tencent.andata.smart.etl.handler.SceneHandler;
import com.tencent.andata.smart.etl.handler.TicketSceneHandler;
import com.tencent.andata.smart.etl.handler.WebIMSceneHandler;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.enums.Scene;
import java.io.Serializable;
import java.util.EnumMap;
import java.util.Map;

/**
 * 场景处理器管理器
 * 负责初始化和提供不同场景的处理器
 */
public class SceneHandlerManager implements Serializable {

    private static final long serialVersionUID = 1L;

    private final transient Map<Scene, SceneHandler> sceneHandlers;

    /**
     * 构造函数，初始化所有场景处理器
     */
    public SceneHandlerManager(CustomerRepository customerRepository,
            TicketRepository ticketRepository,
            DutyRepository dutyRepository) {
        this.sceneHandlers = initSceneHandlers(customerRepository, ticketRepository, dutyRepository);
    }

    /**
     * 初始化场景处理器
     */
    private Map<Scene, SceneHandler> initSceneHandlers(CustomerRepository customerRepository,
            TicketRepository ticketRepository,
            DutyRepository dutyRepository) {
        Map<Scene, SceneHandler> handlers = new EnumMap<>(Scene.class);
        // Group场景不需要Repository
        handlers.put(Scene.Group, new GroupSceneHandler());
        handlers.put(Scene.WebIM, new WebIMSceneHandler(dutyRepository, ticketRepository, customerRepository));
        handlers.put(Scene.C2000, new C2000SceneHandler(dutyRepository, ticketRepository, customerRepository));
        handlers.put(Scene.Ticket, new TicketSceneHandler(dutyRepository, ticketRepository, customerRepository));
        return handlers;
    }

    /**
     * 获取指定场景的处理器
     *
     * @param scene 场景
     * @return 场景处理器
     */
    public SceneHandler getHandler(Scene scene) {
        return sceneHandlers.get(scene);
    }
}