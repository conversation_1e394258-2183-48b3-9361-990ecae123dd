package com.tencent.andata.smart.access.sink;

import com.tencent.andata.smart.access.config.HBaseConfig;
import com.tencent.andata.utils.HBaseSinkFunction;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class HBaseSinkFactory {
    private final HBaseConfig hBaseConfig;

    public HBaseSinkFunction createSink(String tableName) {
        return new HBaseSinkFunction(
                tableName,
                hBaseConfig.getHBaseConfiguration(),
                hBaseConfig.getBufferFlushMaxSizeInBytes(),
                hBaseConfig.getBufferFlushMaxRows(),
                hBaseConfig.getBufferFlushIntervalMillis()
        );
    }
}