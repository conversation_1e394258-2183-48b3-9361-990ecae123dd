package com.tencent.andata.smart.etl.process;

import com.tencent.andata.smart.strategy.model.Strategy;

/**
 * Strategy专用的滑动窗口限流处理器
 * 为了保持向后兼容性，继承自泛型版本
 *
 * @deprecated 建议使用 GenericSlidingWindowRateLimitingProcess<Strategy> 替代
 */
public class SlidingWindowRateLimitingProcess extends GenericSlidingWindowRateLimitingProcess<Strategy> {

    /**
     * 构造函数，保持向后兼容性
     *
     * @param rateLimit 限流阈值，窗口时间内允许的最大事件数
     * @param windowSize 窗口大小（毫秒）
     * @param slideStep 滑动步长，定时器触发间隔（毫秒）
     * @param batchSize 每次定时器触发时输出的最大条数
     */
    public SlidingWindowRateLimitingProcess(long rateLimit, long windowSize, long slideStep, int batchSize) {
        super(rateLimit, windowSize, slideStep, batchSize, Strategy.class);
    }
}