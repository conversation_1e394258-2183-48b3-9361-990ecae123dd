package com.tencent.andata.smart.access.operators;

import static com.tencent.andata.smart.utils.util.GropuMsgClean.getDisplayContent;
import static com.tencent.andata.utils.TimeUtil.getEpochMilli;

import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import com.tencent.andata.utils.HBaseSinkFunction.OperationType;
import java.util.concurrent.atomic.AtomicLong;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GroupMsgDataProcess extends ProcessFunction<JsonNode, HbaseRow> {
    private static final Logger log = LoggerFactory.getLogger(GroupMsgDataProcess.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final String MSG_TIME = "msg_time";
    private static final String MSG_TYPE = "msg_type";
    private static final String GROUP_ID = "group_id";
    private static final String CONTENT = "content";
    private static final String DATA_TYPE = "data_type";
    private static final String DISPLAY_CONTENT = "display_content";
    private static final String DATA = "data";
    private static final String CF = "cf";

    // 性能监控计数器
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private long lastLogTime = System.currentTimeMillis();
    private static final long LOG_INTERVAL_MS = 60000; // 1分钟

    private final OutputTag<JsonNode> groupOutputTag;

    public GroupMsgDataProcess(OutputTag<JsonNode> groupOutputTag) {
        this.groupOutputTag = groupOutputTag;
    }

    @Override
    public void processElement(JsonNode json, Context ctx, Collector<HbaseRow> out) {
        try {
            if (!validateFields(json)) {
                log.warn("Invalid message format: {}", json);
                enrichJsonWithDefaults(json);
                ctx.output(groupOutputTag, json);
                return;
            }

            processGroupMessage(json, ctx, out);
            processedCount.incrementAndGet();
            logMetrics();
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("Error processing group message: {}", json, e);
            try {
                enrichJsonWithDefaults(json);
                ctx.output(groupOutputTag, json);
            } catch (Exception ex) {
                log.error("Error handling fallback for group message: {}", json, ex);
            }
        }
    }

    private boolean validateFields(JsonNode json) {
        return json != null &&
               validateField(json, MSG_TIME) &&
               validateField(json, MSG_TYPE) &&
               validateField(json, GROUP_ID) &&
               validateField(json, CONTENT);
    }

    private boolean validateField(JsonNode json, String fieldName) {
        JsonNode field = json.get(fieldName);
        return field != null && !field.isNull()
                && (!field.isTextual() || StringUtils.isNotBlank(field.asText()));
    }

    private void processGroupMessage(JsonNode json, Context ctx, Collector<HbaseRow> out) throws Exception {
        String rowKey = buildRowKey(json);
        if (StringUtils.isEmpty(rowKey)) {
            log.warn("Empty row key generated for message: {}", json);
            return;
        }

        String displayContent = extractDisplayContent(json);
        enrichJson(json, displayContent);

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put(DATA, objectMapper.writeValueAsString(json));
        out.collect(new HbaseRow(OperationType.INSERT, rowKey, CF, rootNode));
        ctx.output(groupOutputTag, json);
    }

    private String buildRowKey(JsonNode json) {
        try {
            String msgTime = json.get(MSG_TIME).asText();
            String groupId = json.get(GROUP_ID).asText();
            if (StringUtils.isAnyBlank(msgTime, groupId)) {
                return "";
            }
            return groupId + "-" + getEpochMilli(msgTime);
        } catch (Exception e) {
            log.error("Error building row key: {}", json, e);
            return "";
        }
    }

    private String extractDisplayContent(JsonNode json) {
        try {
            String content = json.get(CONTENT).toString();
            String msgType = json.get(MSG_TYPE).asText();
            return getDisplayContent(content, msgType);
        } catch (Exception e) {
            log.error("Error extracting display content: {}", json, e);
            return "";
        }
    }

    private void enrichJson(JsonNode json, String displayContent) {
        ObjectNode node = (ObjectNode) json;
        node.put(DATA_TYPE, "group");
        node.put(DISPLAY_CONTENT, displayContent);
    }

    private void enrichJsonWithDefaults(JsonNode json) {
        ObjectNode node = (ObjectNode) json;
        node.put(DATA_TYPE, "group");
        node.put(DISPLAY_CONTENT, "");
    }

    private void logMetrics() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            log.info("Processing metrics - Processed: {}, Errors: {}", processedCount.get(), errorCount.get());
            lastLogTime = currentTime;
        }
    }
}