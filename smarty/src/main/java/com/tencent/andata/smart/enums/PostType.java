package com.tencent.andata.smart.enums;

import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.Getter;

/**
 * 岗位类型枚举
 */
@Getter
public enum PostType {
    UNKNOWN("0", "其它"),
    SUPPLIER("1", "供应商"),
    FIRST_LINE("2", "一线"),
    SECOND_LINE("3", "1.5线"),
    OPERATION("4", "运维"),
    PRODUCTION_RESEARCH("5", "产研"),
    P_OPERATION("8", "售后运维"),
    P_TCE_PRODUCTION_RESEARCH("9", "TCE产研"),
    P_TBDS_PRODUCTION_RESEARCH("10", "TBDS产研"),
    P_VERTICAL_PRODUCTION_RESEARCH("11", "垂直产研"),
    P_TCE_ERXIAN("12", "二线");

    private final String code;
    private final String desc;

    PostType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PostType of(String code) {
        return List.of(values())
                   .filter(t -> t.code.equals(code))
                   .getOrNull();
    }

    public static Option<String> getDesc(String code) {
        return Option.of(List.of(values())
                             .filter(t -> t.code.equals(code))
                             .map(PostType::getDesc)
                             .getOrNull());
    }
} 