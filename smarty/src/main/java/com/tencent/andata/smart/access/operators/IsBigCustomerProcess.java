package com.tencent.andata.smart.access.operators;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.Tuple;
import io.vavr.Tuple7;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class IsBigCustomerProcess extends ProcessFunction<JsonNode, JsonNode> {
    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final String TABLE_NAME = "dwd_rt_key_customer_information";

    // 缓存配置
    private static final int CACHE_SIZE = 10000;
    private static final int CACHE_EXPIRE_MINUTES = 240;

    private final DatabaseConf pgDBConf;
    private HashMapJDBCLookupQuery customerQuery;
    private Cache<String, Tuple7<Boolean, String, String, String, String, String, String>> customerCache;

    // 性能监控计数器
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private long lastLogTime = System.currentTimeMillis();

    private static final long LOG_INTERVAL_MS = 60000; // 1分钟

    public IsBigCustomerProcess(DatabaseConf pgDBConf) {
        this.pgDBConf = pgDBConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        initializeCache();
        initializeQuery();
        logger.info("[IsBigCustomerProcess] initialized successfully");
    }

    private void initializeCache() {
        customerCache = CacheBuilder.newBuilder()
                .maximumSize(CACHE_SIZE)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build();
    }

    private void initializeQuery() throws Exception {
        JDBCSqlBuilderImpl sqlBuilder = JDBCSqlBuilderImpl.builder()
                .tableName(TABLE_NAME)
                .selectField(Lists.newArrayList("channel_id", "sales_supportor", "group_name",
                    "aftersales_type", "sales_channel", "sales_backup", "service_providers"))
                .conditionKeyList(Lists.newArrayList("channel_id"))
                .limit(1);

        customerQuery = new HashMapJDBCLookupQuery(DatabaseEnum.PGSQL, pgDBConf, sqlBuilder);
        customerQuery.open();
    }

    @Override
    public void processElement(JsonNode jsonNode, Context context, Collector<JsonNode> collector) throws Exception {
        try {
            processedCount.incrementAndGet();
            String groupId = jsonNode.get("group_id").asText();
            Tuple7<Boolean, String, String, String, String, String, String> result = checkIsBigCustomer(groupId);
            boolean isBigCustomer = result._1;
            String salesSupportor = result._2;
            String groupName = result._3;
            String aftersalesType = result._4;
            String salesChannel = result._5;
            String salesBackup = result._6;
            String serviceProviders = result._7;

            ((ObjectNode) jsonNode).put("sales_supportor", salesSupportor);
            ((ObjectNode) jsonNode).put("group_name", groupName);
            ((ObjectNode) jsonNode).put("aftersales_type", aftersalesType);
            ((ObjectNode) jsonNode).put("sales_channel", salesChannel);
            ((ObjectNode) jsonNode).put("sales_backup", salesBackup);
            ((ObjectNode) jsonNode).put("service_providers", serviceProviders);
            ((ObjectNode) jsonNode).put("is_big_customer", isBigCustomer ? 1 : 0);
        } catch (Exception e) {
            errorCount.incrementAndGet();
            ((ObjectNode) jsonNode).put("is_big_customer", 0);
            logger.error(String.format("[IsBigCustomerProcess] Error processing "
                    + "customer check for node: %s\n err_msg: %s", jsonNode, e));
        }
        collector.collect(jsonNode);
        logMetrics();
    }

    private Tuple7<Boolean, String, String, String, String, String, String> checkIsBigCustomer(String groupId) throws Exception {
        // 先从缓存中查询
        Tuple7<Boolean, String, String, String, String, String, String> cachedResult = customerCache.getIfPresent(groupId);
        if (cachedResult != null) {
            cacheHitCount.incrementAndGet();
            return cachedResult;
        }

        // 缓存未命中，查询数据库
        Tuple7<Boolean, String, String, String, String, String, String> result = queryCustomerFromDB(groupId);
        customerCache.put(groupId, result);
        return result;
    }

    private Tuple7<Boolean, String, String, String, String, String, String> queryCustomerFromDB(String groupId) throws Exception {
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("channel_id", groupId);

        List<HashMap<String, Object>> results = customerQuery.query(params);
        if (results.isEmpty()) {
            return Tuple.of(false, "", "", "", "", "", "");
        }

        Object channelId = results.get(0).get("channel_id");
        boolean isBigCustomer = channelId != null && !channelId.toString().isEmpty();
        String salesSupportor = results.get(0).get("sales_supportor").toString();
        String groupName = results.get(0).get("group_name").toString();
        String aftersalesType = results.get(0).get("aftersales_type").toString();
        String salesChannel = results.get(0).get("sales_channel").toString();
        String salesBackup = results.get(0).get("sales_backup").toString();
        String serviceProviders = results.get(0).get("service_providers").toString();
        return Tuple.of(isBigCustomer, salesSupportor, groupName, aftersalesType, salesChannel, salesBackup, serviceProviders);
    }


    private void logMetrics() {
        long errorCnt = errorCount.get();
        long processCnt = processedCount.get();
        long cacheHitCnt = cacheHitCount.get();
        long currentTime = System.currentTimeMillis();

        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            lastLogTime = currentTime;
            logger.info(String.format("[IsBigCustomerProcess] Processing metrics - Processed: "
                            + "%s, Errors: %s, Cache Hits: %s", processCnt, errorCnt, cacheHitCnt));
        }
    }

    @Override
    public void close() throws Exception {
        if (customerQuery != null) {
            try {
                customerQuery.close();
            } catch (Exception e) {
                logger.error("Error closing customerQuery" + e);
            }
        }
        if (customerCache != null) {
            customerCache.invalidateAll();
        }
    }
}