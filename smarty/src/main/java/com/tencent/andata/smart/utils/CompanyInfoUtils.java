package com.tencent.andata.smart.utils;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.lookup.CompanyInfoQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.Properties;

public class CompanyInfoUtils {

    private static final String ERROR_MSG = "CompanyInfoUtils get info from db error: %s, id: %s";
    public static FlinkLog logger = FlinkLog.getInstance();
    public static HashMap<String, String> companyInfoMap = new HashMap<String, String>();
    public static CompanyInfoQuery dbQuery;

    static {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        DatabaseConf datawareDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.pgsql.dataware_r")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        dbQuery = Try.of(() -> {
            CompanyInfoQuery query = new CompanyInfoQuery(DatabaseEnum.PGSQL, datawareDbConf);
            query.open();
            return query;
        }).getOrElseThrow(e -> new RuntimeException(e));
    }

    public static String getCompanyName(String companyId) {
        return Option.of(companyInfoMap.get(companyId))
                .getOrElse(() -> Try
                        .of(() -> {
                            String userName = dbQuery.query(companyId);
                            companyInfoMap.put(companyId, userName);
                            return userName;
                        }).onFailure(e -> logger.error(String.format(ERROR_MSG, e, companyId)))
                        .getOrElse(companyId));
    }
}