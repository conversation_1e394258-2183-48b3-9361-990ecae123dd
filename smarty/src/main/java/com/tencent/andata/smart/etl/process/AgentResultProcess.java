package com.tencent.andata.smart.etl.process;

import static net.minidev.json.JSONValue.isValidJson;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

@Slf4j
public class AgentResultProcess extends ProcessFunction<Strategy, Tuple2<Integer, String>> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final ObjectMapper objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
    private final DatabaseConf dataWareConf;
    private final HashMap<Scene, String> scenePkKeyMap;
    // 工单信息查询
    private transient HashMapJDBCLookupQuery ticketInfoQuery;
    // 队列负责人查询
    private transient HashMapJDBCLookupQuery dutyResponsibeQuery;
    // 当前处理人智能组人员查询
    private transient HashMapJDBCLookupQuery staffIdrQuery;
    // 校验的策略名称
    private String strategyIds;


    public AgentResultProcess(RainbowUtils rainbowUtils, String reCheckStrategyIds) throws Exception {
        // 获取数仓DB参数
        dataWareConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName("cdc.database.pgsql.dataware_r")
                .build();

        this.strategyIds = reCheckStrategyIds;
        scenePkKeyMap = new HashMap<Scene, String>() {{
            put(Scene.Ticket, "operation_id");
            put(Scene.WebIM, "value_of_primary_key");
            put(Scene.Group, "msg_id");
        }};
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 工单信息查询的Query
        ticketInfoQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .tableName("dwm_ticket_statistic")
                        .selectFieldWithAlias(new HashMap<String, String>() {{
                            put("service_scene_level1_name", "service_scene_level1_name");
                            put("service_scene_level2_name", "service_scene_level2_name");
                            put("service_scene_level3_name", "service_scene_level3_name");
                            put("service_scene_level4_name", "service_scene_level4_name");
                            put("service_channel", "service_channel");
                            put("current_operator", "current_operator");
                            put("customer_name", "customer_name");
                            put("fact_assign", "fact_assign");
                            put("url", "url");
                            put("status", "status");
                            put("create_time", "create_time");
                            put("responsible", "responsible");
                            put("priority", "priority");
                            put("title", "title");
                            put("question", "question");
                            put("uin", "uin");
                        }})
                        .conditionKeyList(new ArrayList<String>() {{
                            add("ticket_id");
                        }})
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .limit(1)
        );
        // Query初始化
        ticketInfoQuery.open();
        // 队列信息查询的Query
        dutyResponsibeQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .tableName("dim_incident_duty")
                        .selectFieldWithAlias(new HashMap<String, String>() {{
                            put("responsible", "responsible");
                        }})
                        .conditionKeyList(new ArrayList<String>() {{
                            add("duty_id");
                        }})
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .limit(1)
        );
        dutyResponsibeQuery.open();
        // 当前处理人智能组人员查询
        staffIdrQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .tableName("dim_customer_staff_info")
                        .selectFieldWithAlias(new HashMap<String, String>() {{
                            put("q_idr1", "q_idr1");
                        }})
                        .conditionKeyList(new ArrayList<String>() {{
                            add("uid");
                        }})
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .limit(1)
        );
        staffIdrQuery.open();
    }

    @Override
    public void processElement(Strategy strategy, Context ctx, Collector<Tuple2<Integer, String>> collector)
            throws Exception {
        // 获取工单ID
        Integer ticketId = getTicketId(strategy);
        if (ticketId == null) {
            return;
        }
        // 获取工单信息
        HashMap<String, Object> ticketInfo;
        try {
            ticketInfo = getTicketInfo(ticketId);
        } catch (Exception e) {
            logger.error(String.format(
                    "public opinion get ticket: %s info error:%s",
                    ticketId,
                    e.getMessage()
            ));
            // 给一个默认的数据
            ticketInfo = new HashMap<String, Object>() {{
                put("service_scene_level1_name", "");
                put("service_scene_level2_name", "");
                put("service_scene_level3_name", "");
                put("service_scene_level4_name", "");
                put("service_channel", "");
                put("current_operator", "");
                put("customer_name", "");
                put("fact_assign", "");
                put("url", "");
                put("responsible", "");
                put("priority", "");
                put("title", "");
                put("question", "");
                put("uin", 0);
            }};
        }

        List<Integer> reCheckStrategys = Arrays.stream(strategyIds.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        if (reCheckStrategys.contains(strategy.id)) {
            // 处理模型结果数据
            for (Analyze analyze : strategy.analyzes) {
                HashMap<String, Object> resultInfo = new HashMap<>();
                try {
                    if (isValidJson(analyze.res)) {
                        JsonNode res = objectMapper.readTree(analyze.res);
                        Iterator<String> name = res.fieldNames();
                        while (name.hasNext()) {
                            String nodeName = name.next();
                            resultInfo.put(nodeName, res.get(nodeName));
                        }
                    }
                } catch (Exception e) {
                    log.error("[SplitPreStrategyProcess] analyzes res is not json. Strategy: {}", strategy);
                }
                // 数据发往下游
                emitRiskData(ticketId, ticketInfo, resultInfo, strategy, collector);
            }
        }
    }


    /**
     * 获取工单ID
     *
     * @param strategy 策略数据
     * @return 工单ID
     */
    private Integer getTicketId(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        switch (strategy.scene) {
            case Ticket:
                return Integer.valueOf(strategy.sceneIdentify);
            case WebIM:
                return data.get("conversation_ticket_ids").asInt();
            // TODO 后续通过查DB获取群对应的工单
            case Group:
            default:
                throw new RuntimeException(String.format("Not Support scene: %s", strategy.scene.name()));
        }
    }


    /**
     * 获取结果数据主键
     *
     * @param strategy 策略数据
     * @return 主键
     */
    private String getResPk(Strategy strategy) {
        // 遍历每个trigger的数据，获取数据对应的唯一键进行拼接
        String pks = Arrays.stream(strategy.trigger.data).map(data -> {
            switch (strategy.scene) {
                case Ticket:
                    return String.valueOf(
                            data.get(this.scenePkKeyMap.get(strategy.scene)).longValue());
                case WebIM:
                case Group:
                    return data.get(this.scenePkKeyMap.get(strategy.scene)).asText();
                default:
                    return "";
            }
        }).collect(Collectors.joining("-"));
        return String.format("%s-%s", strategy.sceneIdentify, pks);
    }

    /**
     * 获取流水ID
     */
    private String getOperationId(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        switch (strategy.scene) {
            case Ticket:
                return String.valueOf(data.get(this.scenePkKeyMap.get(strategy.scene)).longValue());
            case WebIM:
            case Group:
                return data.get(this.scenePkKeyMap.get(strategy.scene)).asText();
            default:
                return "";
        }
    }

    /**
     * 获取队列负责人
     *
     * @param strategy 策略数据
     * @return 队列负责人
     */
    private String getDutyResponsible(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        try {
            switch (strategy.scene) {
                case Ticket:
                case WebIM:
                    // 先获取队列ID
                    Integer factAssign = data.get("fact_assign").intValue();
                    // 查询队列信息
                    HashMap<String, Object> dutyData = this.dutyResponsibeQuery.query(new HashMap<String, Object>() {{
                        put("duty_id", factAssign);
                    }}).get(0);
                    // 解析队列负责人信息，是个字符串数组
                    ArrayList<String> responsibleList = new ArrayList<>();
                    for (JsonNode jsonNode : objectMapper.readTree((String) dutyData.get("responsible"))) {
                        responsibleList.add(jsonNode.asText());
                    }
                    // 逗号分割返回
                    return String.join(",", responsibleList);
                // TODO 后续补充
                case Group:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    /**
     * 获取事件触发的时间
     *
     * @param strategy 策略数据
     * @return 事件触发时间
     */
    private long getEventTime(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        String timeStr = "";
        Date parsedDate;
        try {
            switch (strategy.scene) {
                case Ticket:
                    timeStr = data.get("operate_time").asText().replace("T", " ").replace("+08:00", "");
                    break;
                case WebIM:
                    timeStr = data.get("record_update_time").asText();
                    break;
                case Group:
                    timeStr = data.get("msg_time").asText();
            }
        } catch (Exception e) {
            return 0L;
        }
        try {
            parsedDate = dateFormat.parse(timeStr);
        } catch (Exception e) {
            System.out.println("[getEventTime Error]. Strategy: " + strategy);
            return 0L;
        }
        if (parsedDate.getTime() < 0) {
            System.out.println("[getEventTime Error]. Strategy: " + strategy);
        }
        return parsedDate.getTime();
    }

    /**
     * 获取当前处理人职能组人员
     *
     * @param strategy 策略数据
     * @return 当前处理人职能组人员
     */
    private String getCurrentStaffIdr1(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        String staffUId = "";
        try {
            switch (strategy.scene) {
                case Ticket:
                    staffUId = data.get("current_operator").asText();
                    break;
                case WebIM:
                    staffUId = data.get("current_staff").asText();
                    break;
                // TODO 后续补充
                case Group:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
        HashMap<String, Object> param = new HashMap<>();
        param.put("uid", staffUId);
        try {
            HashMap<String, Object> idrData = staffIdrQuery.query(param).get(0);
            return idrData.get("q_idr1").toString();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取当前岗位
     */
    private String getCurrentPost(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        String post = "";
        try {
            switch (strategy.scene) {
                case Ticket:
                case WebIM:
                    post = data.get("post").asText();
                    break;
                // TODO 后续补充
                case Group:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
        return post;
    }

    /**
     * 发送数据
     *
     * @param ticketId 工单ID
     * @param ticketInfo 工单信息
     * @param resultInfo 模型结果信息
     * @param collector 发送器
     * @throws JsonProcessingException 异常
     */
    private void emitRiskData(
            int ticketId,
            HashMap<String, Object> ticketInfo,
            HashMap<String, Object> resultInfo,
            Strategy strategy,
            Collector<Tuple2<Integer, String>> collector
    ) throws JsonProcessingException {
        HashMap<String, Object> res = new HashMap<>();
        // 设置风险主键
        res.put("pk", getResPk(strategy));
        // 设置工单ID
        res.put("ticket_id", ticketId);
        // 设置工单属性
        res.putAll(ticketInfo);
        // 设置大模型结果信息
        res.putAll(resultInfo);
        res.putAll(getPreLLMRes(strategy));
        // 设置触发场景
        res.put("scene", strategy.scene.name());
        // 设置场景ID
        res.put("scene_identify", strategy.sceneIdentify);
        // 设置风控时间
        res.put("trigger_time", strategy.trigger.triggerTimestamp);
        // 流水ID
        res.put("operation_id", getOperationId(strategy));
        // 队列负责人
        res.put("duty_responsible", getDutyResponsible(strategy));
        // 虚拟智能组
        res.put("idr1", getCurrentStaffIdr1(strategy));
        // 事件时间
        res.put("event_time", getEventTime(strategy));
        // 当前岗位
        res.put("post", getCurrentPost(strategy));
        // 服务场景
        res.putAll(getServiceScenes(strategy));
        collector.collect(Tuple2.of(ticketId, objectMapper.writeValueAsString(res)));
    }

    /**
     * lookup join query获取工单数据
     *
     * @param ticketId 工单ID
     * @return 工单数据
     * @throws Exception 异常
     */
    private HashMap<String, Object> getTicketInfo(long ticketId) throws Exception {
        HashMap<String, Object> param = new HashMap<String, Object>() {{
            put("ticket_id", ticketId);
        }};
        return ticketInfoQuery.query(param).get(0);
    }

    private HashMap<String, Object> getPreLLMRes(Strategy strategy) {
        HashMap<String, Object> result = new HashMap<>();
        for (JsonNode data : strategy.trigger.data) {
            try {
                if (isValidJson(data.toString())) {
                    JsonNode res = data.get("preLlmRes");
                    Iterator<String> name = res.fieldNames();
                    while (name.hasNext()) {
                        String nodeName = name.next();
                        result.put(nodeName, res.get(nodeName));
                    }
                }
            } catch (Exception e) {
                System.out.println("[getPreLLMRes Error]. Strategy: " + strategy);
            }
        }
        return result;
    }

    private HashMap<String, Object> getServiceScenes(Strategy strategy) {
        HashMap<String, Object> result = new HashMap<>();
        List<String> keys = Arrays
                .asList("service_scene_level1_name",
                        "service_scene_level2_name",
                        "service_scene_level3_name",
                        "service_scene_level4_name");

        for (JsonNode data : strategy.trigger.data) {
            try {
                if (isValidJson(data.toString())) {
                    keys.forEach(key -> result.put(key, data.get(key)));
                }
            } catch (Exception e) {
                System.out.println("[getPreLLMRes Error]. Strategy: " + strategy);
            }
        }
        return result;
    }
}