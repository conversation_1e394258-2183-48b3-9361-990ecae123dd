package com.tencent.andata.smart.strategy.model;

import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import java.io.Serializable;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设计：Strategy统一使用基本类(不带任何方法)+枚举
 * 目的：这里只做定义操作，没有具体实现。可序列化，能在DataStream中流转，
 * 从上游触发后下发给下游，下游算子（ScanHbase，调大模型）不用再依赖外部，逻辑更加内敛
 * 后续也可以通过序列化成json发往其他地方。
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Strategy implements Serializable {

    // 策略唯一标识
    public int id;
    // 策略名称
    public String name;
    // 策略场景
    public Scene scene;
    // 策略条件
    public Condition condition;
    // Trigger
    public Trigger trigger;
    // 上下文分块
    public Chunk chunk;
    // 需要进行的分析列表
    public Analyze[] analyzes;
    // 场景标识，如工单ID，会话ID，群ID
    public String sceneIdentify;

    @Override
    public String toString() {
        return "Strategy{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", scene=" + scene +
                ", condition=" + condition +
                ", trigger=" + trigger +
                ", chunk=" + chunk +
                ", analyzes=" + Arrays.toString(analyzes) +
                ", sceneIdentify='" + sceneIdentify + '\'' +
                '}';
    }
}