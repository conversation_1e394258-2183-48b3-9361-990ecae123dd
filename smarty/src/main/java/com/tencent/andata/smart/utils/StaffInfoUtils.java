package com.tencent.andata.smart.utils;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.lookup.CustomerStaffQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.Properties;

public class StaffInfoUtils {

    private static final String ERROR_MSG = "StaffInfoUtils get info from db error: %s, id: %s";
    public static FlinkLog logger = FlinkLog.getInstance();
    public static HashMap<String, Tuple2<String, Integer>> userInfoMap = new HashMap<>();
    public static CustomerStaffQuery dbQuery;

    static {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        DatabaseConf datawareDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.pgsql.dataware_r")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        dbQuery = Try.of(() -> {
            CustomerStaffQuery query = new CustomerStaffQuery(DatabaseEnum.PGSQL, datawareDbConf);
            query.open();
            return query;
        }).getOrElseThrow(e -> new RuntimeException(e));
    }

    /**
     * 根据用户ID获取用户名
     * 1. 首先从本地缓存userInfoMap中查找
     * 2. 如果缓存中不存在，则从数据库查询
     * 3. 将查询结果存入缓存
     * 4. 如果查询失败，记录错误日志并返回原始ID
     *
     * @param id 用户ID
     * @return 用户名，如果查询失败则返回原始ID
     */
    public static String getUserName(String id) {
        return Option.of(userInfoMap.get(id))
                .map(t -> t._1)
                .getOrElse(() -> Try
                        .of(() -> {
                            Tuple2<String, Integer> rst = dbQuery.query(id);
                            userInfoMap.put(id, rst);
                            return rst._1;
                        }).onFailure(e -> logger.error(String.format(ERROR_MSG, e, id)))
                        .getOrElse(id));
    }


    /**
     * 根据用户ID获取用户所属公司ID
     * 1. 首先从本地缓存userInfoMap中查找
     * 2. 如果缓存中不存在，则从数据库查询
     * 3. 将查询结果存入缓存
     * 4. 如果查询失败，记录错误日志并返回NULL
     *
     * @param id 用户ID
     * @return 用户所属公司ID，如果查询失败则返回NULL
     */
    public static int getAssignCompanyId(String id) {
        return Option.of(userInfoMap.get(id))
                .map(t -> t._2)
                .getOrElse(() -> Try
                        .of(() -> {
                            Tuple2<String, Integer> rst = dbQuery.query(id);
                            userInfoMap.put(id, rst);
                            return rst._2;
                        }).onFailure(e -> logger.error(String.format(ERROR_MSG, e, id)))
                        .getOrElse(-1));
    }
}