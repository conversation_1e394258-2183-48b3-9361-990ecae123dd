package com.tencent.andata.smart.access.config;


import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;

@Getter
@Builder(builderMethodName = "internalBuilder")
public class AppConfig {

    private final Map<String, String> hbaseTableNames;
    private final Map<String, MqConf> kafkaConfigs;
    private final DatabaseConf workDBConf;
    private final DatabaseConf DWDBConf;
    private final HBaseConfig hbaseConfig;
    private final ArrayNode devGroupList;
    private final String smallModelUrl;

    public static AppConfigBuilder builder() {
        return internalBuilder()
                .kafkaConfigs(new HashMap<>())
                .hbaseTableNames(new HashMap<>());
    }

    public static class AppConfigBuilder {

        public AppConfigBuilder withRainbowUtils(RainbowUtils rainbowUtils) throws Exception {
            // 初始化Source Kafka配置
            this.kafkaConfigs.put("pcloudGroupStream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.private_cloud_group_msg"));
            this.kafkaConfigs.put("ticketOperationStream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.ticket_operation"));
            this.kafkaConfigs.put("webimSourceStream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.webim_online"));
            this.kafkaConfigs.put("knockGroupStream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.knock_group"));
            this.kafkaConfigs.put("slackGroupStream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.slack_group"));
            this.kafkaConfigs.put("qqGroupStream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.qqgroup"));
            this.kafkaConfigs.put("C2000Stream", MqConf.fromRainbow(rainbowUtils, "mq.kafka.c2000"));

            // sink MQ 配置
            this.kafkaConfigs.put("ansmartQuality", MqConf.fromRainbow(rainbowUtils, "mq.kafka.ansmart_quality"));

            final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                    .setRainbowUtils(rainbowUtils);

            // 初始化数据库配置
            this.workDBConf = kvConfBuilder
                    .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                    .build();

            this.DWDBConf = kvConfBuilder
                    .setGroupName(String.format("%s.%s.%s", "cdc.database", "postgresql", "dataware_r"))
                    .build();

            // 初始化HBase配置
            this.hbaseConfig = HBaseConfig
                    .builder()
                    .zkQuorum(rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM"))
                    .zkNodeParent(rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT"))
                    .build();

            // 初始化小模型URL
            this.smallModelUrl = rainbowUtils.getStringValue("ansmart.smart_assist.small_model_url", "url");

            // 初始化devGroupList
            this.devGroupList = rainbowUtils.getArrayNode("mq.kafka.private_cloud_group_msg", "DEV_GROUP_LIST");

            // 初始化HBase表名
            this.hbaseTableNames.put("Group_msg", rainbowUtils.getStringValue("cdc.database.hbase", "GROUP_MSG"));
            this.hbaseTableNames.put("Webim_operation", rainbowUtils.getStringValue("cdc.database.hbase", "WEBIM_OPERATION"));

            return this;
        }
    }
}