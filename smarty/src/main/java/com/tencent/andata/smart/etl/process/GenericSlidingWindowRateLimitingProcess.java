package com.tencent.andata.smart.etl.process;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

/**
 * 泛型滑动窗口限流处理器
 * 支持任意类型T的数据限流处理
 *
 * @param <T> 处理的数据类型
 */
public class GenericSlidingWindowRateLimitingProcess<T> extends KeyedProcessFunction<String, T, T> {

    private final long rateLimit;
    private final long windowSize;
    private final long slideStep;
    private final int batchSize; // 每次定时器触发时输出的最大条数
    private final Class<T> typeClass; // 用于序列化/反序列化的类型信息

    // 状态：计数窗口（保留用于兼容性，但不再使用）
    private transient ValueState<Long> countState;
    // 状态：窗口开始时间（保留用于兼容性，但不再使用）
    private transient ValueState<Long> windowStartState;
    // 状态：是否限流
    private transient ValueState<Boolean> isRateLimited;
    // 状态：缓存被限流的数据
    private transient ListState<byte[]> bufferedEvents;
    // 新增状态：事件时间戳队列，用于真正的滑动窗口
    private transient ListState<Long> timestampState;
    // 新增状态：限流开始时间，用于避免频繁切换
    private transient ValueState<Long> rateLimitStartTime;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 构造函数
     *
     * @param rateLimit 限流阈值，窗口时间内允许的最大事件数
     * @param windowSize 窗口大小（毫秒）
     * @param slideStep 滑动步长，定时器触发间隔（毫秒）
     * @param batchSize 每次定时器触发时输出的最大条数
     * @param typeClass 处理的数据类型Class，用于序列化/反序列化
     */
    public GenericSlidingWindowRateLimitingProcess(long rateLimit, long windowSize, long slideStep, int batchSize, Class<T> typeClass) {
        this.rateLimit = rateLimit;
        this.windowSize = windowSize;
        this.slideStep = slideStep;
        this.batchSize = batchSize;
        this.typeClass = typeClass;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        countState = getRuntimeContext().getState(new ValueStateDescriptor<>("count", Long.class));
        windowStartState = getRuntimeContext().getState(new ValueStateDescriptor<>("windowStart", Long.class));
        isRateLimited = getRuntimeContext().getState(new ValueStateDescriptor<>("isRateLimited", Boolean.class));
        bufferedEvents = getRuntimeContext().getListState(new ListStateDescriptor<>("bufferedEvents", byte[].class));
        // 初始化新增的状态
        timestampState = getRuntimeContext().getListState(new ListStateDescriptor<>("timestamps", Long.class));
        rateLimitStartTime = getRuntimeContext().getState(new ValueStateDescriptor<>("rateLimitStartTime", Long.class));
    }

    @Override
    public void processElement(T value, Context ctx, Collector<T> out) throws Exception {
        long currentTime = ctx.timerService().currentProcessingTime();

        // 清理过期的时间戳，实现真正的滑动窗口
        cleanExpiredTimestamps(currentTime);

        Boolean rateLimited = isRateLimited.value();

        // 如果当前处于限流状态，直接缓存数据
        if (rateLimited != null && rateLimited) {
            byte[] compressValue = objectMapper.writeValueAsBytes(value);
            bufferedEvents.add(compress(compressValue));
            // 确保定时器已注册，用于后续处理缓存事件
            ensureTimerRegistered(ctx, currentTime);
            return;
        }

        // 获取当前窗口内的事件数量
        int currentWindowCount = getTimestampCount();

        // 判断是否需要限流
        if (currentWindowCount >= rateLimit) {
            // 达到限流阈值，进入限流状态
            isRateLimited.update(true);
            rateLimitStartTime.update(currentTime);
            byte[] compressValue = objectMapper.writeValueAsBytes(value);
            bufferedEvents.add(compress(compressValue)); // 当前数据也缓存
            // 注册定时器处理缓存事件
            ensureTimerRegistered(ctx, currentTime);
        } else {
            // 正常输出数据，并记录时间戳
            timestampState.add(currentTime);
            out.collect(value);
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<T> out) throws Exception {
        long currentTime = ctx.timerService().currentProcessingTime();

        // 清理过期的时间戳
        cleanExpiredTimestamps(currentTime);

        // 检查并更新限流状态（使用新的智能逻辑）
        updateRateLimitStatus(currentTime);

        // 输出缓存的数据并更新剩余事件
        List<byte[]> remainingEvents = outputBufferedEvents(out, currentTime);

        // 如果仍有缓存数据，注册下一次定时器
        if (!remainingEvents.isEmpty()) {
            ctx.timerService().registerProcessingTimeTimer(currentTime + slideStep);
        }

        // 清理过期状态
        clearExpiredState(currentTime);
    }

    /**
     * 清理过期的时间戳，实现真正的滑动窗口
     */
    private void cleanExpiredTimestamps(long currentTime) throws Exception {
        List<Long> validTimestamps = new ArrayList<>();

        for (Long timestamp : timestampState.get()) {
            if (currentTime - timestamp < windowSize) {
                validTimestamps.add(timestamp);
            }
        }

        timestampState.update(validTimestamps);
    }

    /**
     * 获取当前窗口内的事件数量
     */
    private int getTimestampCount() throws Exception {
        int count = 0;
        for (Long ignored : timestampState.get()) {
            count++;
        }
        return count;
    }

    /**
     * 确保定时器已注册
     */
    private void ensureTimerRegistered(Context ctx, long currentTime) {
        ctx.timerService().registerProcessingTimeTimer(currentTime + slideStep);
    }

    /**
     * 更新限流状态，添加智能解除机制
     */
    private void updateRateLimitStatus(long currentTime) throws Exception {
        Boolean rateLimited = isRateLimited.value();
        if (rateLimited != null && rateLimited) {
            Long rateLimitStart = rateLimitStartTime.value();
            int currentWindowCount = getTimestampCount();
            int bufferedCount = getBufferedEventCount();

            // 智能限流解除条件：
            // 1. 当前窗口内事件数量明显低于限流阈值（留有缓冲）
            // 2. 限流已持续一定时间（避免频繁切换）
            // 3. 缓存事件数量不太多（避免突发输出）
            boolean shouldRelease = currentWindowCount <= (rateLimit * 0.7) && // 70%阈值缓冲
                    (rateLimitStart == null || currentTime - rateLimitStart >= slideStep) && // 最小持续时间
                    bufferedCount <= batchSize * 2; // 缓存事件不太多

            if (shouldRelease) {
                isRateLimited.update(false);
                rateLimitStartTime.clear();
            }
        }
    }

    /**
     * 获取缓存事件数量
     */
    private int getBufferedEventCount() throws Exception {
        int count = 0;
        for (byte[] ignored : bufferedEvents.get()) {
            count++;
        }
        return count;
    }

    private List<byte[]> outputBufferedEvents(Collector<T> out, long currentTime) throws Exception {
        Iterable<byte[]> buffered = bufferedEvents.get();
        List<byte[]> remainingEvents = new ArrayList<>();
        int outputCount = 0;

        for (byte[] event : buffered) {
            // 检查是否还能输出更多事件
            int currentWindowCount = getTimestampCount();

            // 修改条件：如果当前不处于限流状态，或者窗口有容量，就可以输出
            boolean canOutput = outputCount < batchSize &&
                    (isRateLimited.value() == null || !isRateLimited.value() ||
                            currentWindowCount + outputCount < rateLimit);

            if (canOutput) {
                // 输出事件并记录时间戳
                out.collect(objectMapper.readValue(decompress(event), typeClass));
                timestampState.add(currentTime);
                outputCount++;
            } else {
                // 保留未输出的事件
                remainingEvents.add(event);
            }
        }

        bufferedEvents.update(remainingEvents);
        return remainingEvents;
    }

    /**
     * 清理过期状态，改进版本
     */
    private void clearExpiredState(long currentTime) throws Exception {
        // 检查是否需要清理状态
        boolean shouldClearState = false;

        // 条件1：时间戳队列为空且没有缓存事件
        int timestampCount = getTimestampCount();
        int bufferedCount = getBufferedEventCount();

        if (timestampCount == 0 && bufferedCount == 0) {
            shouldClearState = true;
        }

        // 条件2：长时间没有活动（超过窗口大小的2倍）
        Long windowStart = windowStartState.value();
        if (windowStart != null && currentTime - windowStart >= windowSize * 2) {
            shouldClearState = true;
        }

        if (shouldClearState) {
            // 清理所有相关状态
            countState.clear();
            windowStartState.clear();
            isRateLimited.clear();
            rateLimitStartTime.clear();

            // 如果没有缓存事件，也清理时间戳状态
            if (bufferedCount == 0) {
                timestampState.clear();
            }
        }
    }

    private byte[] compress(byte[] data) throws IOException {
        // 检查输入数据是否为空
        if (data == null || data.length == 0) {
            throw new IllegalArgumentException("Input data cannot be null or empty");
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(data);
            gzipOutputStream.finish(); // 确保所有数据都被写入
        }
        return byteArrayOutputStream.toByteArray(); // 返回压缩后的字节数组
    }

    private byte[] decompress(byte[] compressedData) throws IOException {
        // 检查输入数据是否为空
        if (compressedData == null || compressedData.length == 0) {
            throw new IllegalArgumentException("Input data cannot be null or empty");
        }

        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData);
                GZIPInputStream gzipInputStream = new GZIPInputStream(byteArrayInputStream)) {
            // 使用 ByteArrayOutputStream 来收集解压缩后的数据
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            // 逐块读取解压缩的数据
            while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }

            // 返回解压缩后的字节数组
            return byteArrayOutputStream.toByteArray();
        }
    }
}