package com.tencent.andata.smart.etl.filter;

import com.tencent.andata.smart.config.AppConfig;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.functions.FilterFunction;

import java.util.List;
import java.util.stream.Collectors;

public class ModelResultFilter implements FilterFunction<Strategy> {

    private final List<Integer> filterStrategyIdList;

    public ModelResultFilter(AppConfig appConfig) {
        filterStrategyIdList = appConfig.strategies.stream().map(a -> a.id).collect(Collectors.toList());
    }

    @Override
    public boolean filter(Strategy strategy) throws Exception {
        return filterStrategyIdList.contains(strategy.id);
    }
}