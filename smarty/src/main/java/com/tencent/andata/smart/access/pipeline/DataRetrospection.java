package com.tencent.andata.smart.access.pipeline;

import static com.tencent.andata.utils.TableUtils.row2Json;
import static org.apache.flink.table.api.Expressions.$;

import com.tencent.andata.smart.access.config.AppConfig;
import com.tencent.andata.smart.access.operators.ServiceSceneProcess;
import com.tencent.andata.smart.access.operators.WebImMsgDataProcess;
import com.tencent.andata.smart.access.sink.HBaseSinkFactory;
import com.tencent.andata.smart.access.sink.KafkaSinkFactory;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import io.vavr.collection.List;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.util.OutputTag;

/*
 * 数据回溯
 */
public class DataRetrospection {

    private static final JSONUtils jsonUtils = new JSONUtils();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    StreamTableEnvironment tEnv;
    private final AppConfig appConfig;
    private static DatabaseConf dwDbConf;

    private final KafkaSinkFactory kafkaSinkFactory;
    private final HBaseSinkFactory hBaseSinkFactory;
    private static IcebergCatalogReader catalog = new IcebergCatalogReader();


    // 定义侧输出流标签为静态常量
    private static final OutputTag<JsonNode> WEBIM_OUTPUT_TAG = new OutputTag<JsonNode>("webim-side-output") {};


    public DataRetrospection(FlinkEnv env, AppConfig appConfig) {
        this.appConfig = appConfig;
        this.tEnv = env.streamTEnv();
        dwDbConf = appConfig.getDWDBConf();
        this.kafkaSinkFactory = new KafkaSinkFactory();
        this.hBaseSinkFactory = new HBaseSinkFactory(appConfig.getHbaseConfig());
    }

    // 注册flink表
    private void registerFlinkTable() throws Exception {

        ArrayNode iceTbl2Ftbl = objectMapper.readValue(
                "[" +
                        "{\"icebergTable\":\"dwd_incident_ticket_operation\",\"fTable\":\"ice_src_t202_ticket_operation\"}," +
                        "{\"icebergTable\":\"ods_distribution_table_p\",\"fTable\":\"ice_src_ods_distribution_table_p\"}" +
                        "]",
                ArrayNode.class);

        // 注册iceberg表
        TableUtils.icebergTable2FlinkTable("andata_rt", iceTbl2Ftbl, tEnv, catalog);

        // 注册hbase表
        TableUtils.hbaseTable2FlinkTable("hbase_ticket_base_info", "Incident_ticket_base_info", "cf", tEnv);
    }

    private Tuple2<Table, Table> getFlinkTable() {
        Table incidentTbl = tEnv.sqlQuery(
                "SELECT\n" +
                        "  t1.*,\n" +
                        "  JSON_VALUE(t2.data, '$.service_channel' RETURNING INTEGER) AS service_channel\n" +
                        "FROM ice_src_t202_ticket_operation /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t1\n" +
                        "INNER JOIN hbase_ticket_base_info FOR SYSTEM_TIME AS OF process_time AS t2\n" +
                        "ON CAST(t1.ticket_id AS STRING) = t2.rowkey\n" +
                        "WHERE t1.operation_type = 16\n" +
                        "  AND t1.operate_time >= '2024-09-30 00:00:00'\n" +
                        "  AND JSON_VALUE(t2.data, '$.service_channel' RETURNING INTEGER) = 3"

        );

        tEnv.createTemporaryView("t202_ticket_operation_view", incidentTbl);

        // 将工单流水数据转成json
        Table OperationJsonTbl = tEnv
                .sqlQuery(row2Json(incidentTbl,
                        "CAST(`ticket_id` AS STRING) || '-' || CAST(`operation_id` AS STRING)",
                        "t202_ticket_operation_view"))
                .dropColumns($("rowkey"));

        Table webimTbl = tEnv.sqlQuery(
                "SELECT\n" +
                        "  t3.message\n" +
                        "FROM ice_src_ods_distribution_table_p /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t3\n" +
                        "WHERE t3.dst_table_name = 'ods_im_online_customer_service_backend_data'\n" +
                        "  AND t3.mq_msg_publish_time > '2024-09-30 00:00:00'");

        return Tuple2.of(OperationJsonTbl, webimTbl);
    }

    // table转流
    public Tuple2<DataStream<JsonNode>, SingleOutputStreamOperator<HbaseRow>> table2Stream(Tuple2<Table, Table> tbls) {

        DataStream<JsonNode> operationJsonStream = tEnv
                .toDataStream(tbls.f0)
                .map(row -> objectMapper.readValue(row.<String>getFieldAs("data"), JsonNode.class));

        SingleOutputStreamOperator<HbaseRow> webimHbaeStream = tEnv
                .toDataStream(tbls.f1)
                .map(row -> jsonUtils.toSnakeCase(jsonUtils.flatten(objectMapper.readTree(row.<String>getFieldAs("message")))))
                .process(new ServiceSceneProcess(dwDbConf))
                .process(new WebImMsgDataProcess(WEBIM_OUTPUT_TAG));

        return Tuple2.of(operationJsonStream, webimHbaeStream);
    }

    public void setupPipeline() throws Exception {
        // 注册flink表
        registerFlinkTable();

        // 获取flink表
        Tuple2<Table, Table> ftables = getFlinkTable();

        // table转流
        Tuple2<DataStream<JsonNode>, SingleOutputStreamOperator<HbaseRow>> stream = table2Stream(ftables);

        // webim-JsonNode类型数据过滤
        DataStream<JsonNode> webimFiltedStream = stream.f1
                .getSideOutput(WEBIM_OUTPUT_TAG)
                .filter(data -> List.of(6, 7, 8, 9, 12).contains(data.get("status").asInt()) &&
                        List.of("PRESALE", "MC", "MP", "MA").contains(data.get("source").asText()) &&
                        List.of("AutoFinishConversation", "FinishUserConversation", "FinishConversation").contains(data.get("rpc_name").asText()));

        // 数据写入kafka和Hbase
        setupSinks(stream.f0.union(webimFiltedStream), stream.f1);
    }


    // 数据写入kafka和Hbase
    private void setupSinks(DataStream<JsonNode> allInOneJsonNodeStream, SingleOutputStreamOperator<HbaseRow> webimHbaeStream) {
        // Kafka sink
        allInOneJsonNodeStream
                .sinkTo(kafkaSinkFactory.createKafkaSink(appConfig, "data_refresh"))
                .setParallelism(4)
                .name("all-in-one-sink-kafka");

        // HBase sinks
        String webimOperationTable = appConfig.getHbaseTableNames().get("Webim_operation");
        webimHbaeStream.addSink(hBaseSinkFactory.createSink(webimOperationTable)).name("webim-msg-sink-hbase");
    }
}