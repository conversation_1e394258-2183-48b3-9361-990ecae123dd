package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;

public class CustomerStaffQuery extends AbstractJDBCLookupQuery<String, Tuple2<String, Integer>> {

    public CustomerStaffQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected Tuple2<String, Integer> executeQuery(Connection connection, String uid) throws Exception {
        // 使用PreparedStatement进行参数化查询
        String sql = "SELECT user_id, assign_company_id FROM dim_customer_staff_info WHERE uid = ?";
        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setString(1, uid);
        final ResultSet resultSet = preparedStatement.executeQuery();
        // 遍历结果
        while (resultSet.next()) {
            // 用户ID
            String userId = resultSet.getString(1);
            // 公司ID
            int companyId = resultSet.getInt(2);
            return Tuple.of(userId, companyId);
        }

        return Tuple.of("", 0);
    }
}