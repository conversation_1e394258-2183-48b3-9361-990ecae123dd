package com.tencent.andata.smart.similar.config;

import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ApplicationConfig implements Serializable {

    private DatabaseConf mysqlWorkDBConf;
    private DatabaseConf dwdDBConf;
    private DatabaseConf aigcDbConf;
    private String modelUrl;
    private String modelToken;

    private String similarTicketPrompt;
    private String similarGenerateNum;

    private HashMapJDBCLookupQuery starRocksQuery;
    private Boolean isGenerate;
    private Boolean isIncrese;


    public Integer getGenerateNum() {
        return Integer.valueOf(similarGenerateNum);
    }
}