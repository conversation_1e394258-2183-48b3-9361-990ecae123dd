package com.tencent.andata.smart.utils.aviatorFuns;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import io.vavr.control.Try;
import java.util.Map;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RegexMatchFunction extends AbstractFunction {

    private static final Logger log = LoggerFactory.getLogger(RegexMatchFunction.class);

    private static String safeConvertToString(AviatorObject arg, Map<String, Object> env) {
        Object value = arg.getValue(env);
        return safeConvertToString(value);
    }

    private static String safeConvertToString(Object obj) {
        return Try.of(() -> String.valueOf(obj))
                  .onFailure(e ->
                          log.warn("Failed to convert object to string. Original data: [{}], Object type: [{}], Exception: {}", obj, obj.getClass().getName(), e.getMessage(), e))
                  .getOrElse("");
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
        String text = safeConvertToString(arg1, env);
        String pattern = FunctionUtils.getStringValue(arg2, env);
        return AviatorBoolean.valueOf(text != null && pattern != null && Pattern.compile(pattern).matcher(text).find());
    }

    @Override
    public String getName() {
        return "regexMatch";
    }
}