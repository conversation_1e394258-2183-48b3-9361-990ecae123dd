package com.tencent.andata.smart.utils.splice.factory;

import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.utils.splice.ItemSplice;
import com.tencent.andata.smart.utils.splice.impl.*;
import io.vavr.control.Option;


/**
 * 默认的Splice工厂实现
 */
public class DefaultSpliceFactory implements SpliceFactory {

    private final GroupMsgRCSplice groupMsgRCSplice = new GroupMsgRCSplice();
    private final WebIMOperationRCSplice webIMRCSplice = new WebIMOperationRCSplice();
    private final WebIMOperationQASplice webIMQASplice = new WebIMOperationQASplice();
    private final TicketOperationRCSplice ticketRCSplice = new TicketOperationRCSplice();
    private final TicketOperationQASplice ticketQASplice = new TicketOperationQASplice();
    private final CallCenterQASplice callCenterQASplice = new CallCenterQASplice();
    private final PcloudInnerReplySplice pcloudInnerReplySplice = new PcloudInnerReplySplice();


    @Override
    public Option<ItemSplice> createSplice(SpliceType spliceType) {
        return Option.of(spliceType)
                .flatMap(type -> {
                    switch (type) {
                        case GroupPublicOpinion: // 群风控
                            return Option.of(groupMsgRCSplice);
                        case C2000PublicOpinion: // C2000风控
                            return Option.of(groupMsgRCSplice);
                        case TicketQualityInspection: // 工单质检
                            return Option.of(ticketQASplice);
                        case TicketPublicOpinion: // 工单风控
                            return Option.of(ticketRCSplice);
                        case WebIMQualityInspection: // WebIM质检
                            return Option.of(webIMQASplice);
                        case WebIMPublicOpinion: // WebIM风控
                            return Option.of(webIMRCSplice);
                        case TicketPriorityAgent: // Pcloud工单内部回复
                            return Option.of(pcloudInnerReplySplice);
                        case CallCenterQualityInspection: // CallCenter质检
                            return Option.of(callCenterQASplice);
                        default:
                            return Option.none();
                    }
                });
    }
} 