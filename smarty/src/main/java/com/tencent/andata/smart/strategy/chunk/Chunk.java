package com.tencent.andata.smart.strategy.chunk;


import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Chunk类型
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Chunk implements Serializable {

    public ChunkType type;
    /**
     * Type为Conversation时，value为空
     */
    public String value;
    // 切块最大大小
    public long maxSize;
    // 切块分隔符
    public String delimiter;
    // 拼接类型
    public SpliceType conversationSpliceType;
    // 区块上下文
    public String conversation;
    // 上下文流水
    public String operations;
    // aviator表达式列表
    public String[] aviatorExpressions;

    @Override
    public String toString() {
        return "Chunk{" +
                "type=" + type +
                ", value='" + value + '\'' +
                ", maxSize=" + maxSize +
                ", delimiter='" + delimiter + '\'' +
                ", conversationSpliceType=" + conversationSpliceType +
                ", conversation='" + conversation + '\'' +
                '}';
    }
}