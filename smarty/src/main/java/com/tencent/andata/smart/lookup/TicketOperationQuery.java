package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.DbResultUtils;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class TicketOperationQuery extends AbstractJDBCLookupQuery<Long, JsonNode> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DbResultUtils dbResultUtils = DbResultUtils.getInstance(); // 获取单例实例

    public TicketOperationQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected JsonNode executeQuery(Connection connection, Long ticketId) throws Exception {
        String sql = "SELECT\n"
                + "    *,\n"
                + "    'ticket_operation' AS data_type\n"
                + "FROM t202_ticket_operation\n"
                + "WHERE ticket_id = ?\n"
                + "    AND (operation_type = 16\n"
                + "         OR (operation_type = 1\n"
                + "             AND target_status = 3))\n"
                + "LIMIT 1";
        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setLong(1, ticketId);
        final ResultSet resultSet = preparedStatement.executeQuery();

        // 遍历结果，因为限制了只返回一行，所以最多只会执行一次，
        // 否则就需要使用while循环，返回的是JsonNode数组（需要创建ArrayNode）
        if (resultSet.next()) {
            return dbResultUtils.convertResultSetRowToJsonNode(resultSet);
        }

        // 没有找到结果，返回空的JsonNode
        return objectMapper.createObjectNode();
    }
}