package com.tencent.andata.smart.etl.repository.imp;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.tencent.andata.smart.etl.domain.CustomerStaffInfo;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.tools.ValidationException;

@Slf4j
public class JdbcCustomerRepository implements CustomerRepository, Serializable {

    // 缓存配置
    private static final int CACHE_SIZE = 30000;
    private static final int CACHE_EXPIRE_MINUTES = 240;
    private final HashMapJDBCLookupQuery customerQuery;
    private Cache<String, CustomerStaffInfo> customerResponsibeCache;

    public JdbcCustomerRepository(DatabaseConf dataWareConf) throws ValidationException {
        // 初始化缓存
        initializeCache();
        this.customerQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .tableName("dim_customer_staff_info")
                        .selectFieldWithAlias(getFieldMappings())
                        .conditionKeyList(Arrays.asList("uid"))
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .limit(1)
        );
    }

    private void initializeCache() {
        customerResponsibeCache = CacheBuilder.newBuilder()
                .maximumSize(CACHE_SIZE)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build();
    }

    @Override
    public Option<CustomerStaffInfo> findByUid(String uid) {
        return Option.of(uid)
                .flatMap(id -> Option.of(customerResponsibeCache.getIfPresent(id))
                        .orElse(() -> queryFromDatabase(id)));
    }

    private Option<CustomerStaffInfo> queryFromDatabase(String uid) {
        return Try.of(() -> {
            HashMap<String, Object> params = new HashMap<>();
            params.put("uid", uid);

            return Option.of(customerQuery.query(params))
                    .filter(results -> !results.isEmpty())
                    .map(results -> results.get(0))
                    .map(result ->
                            CustomerStaffInfo.builder()
                                    .userId((String) result.get("user_id"))
                                    .userName((String) result.get("user_name"))
                                    .companyId((Long) result.get("company_id"))
                                    .assignCompanyId((Long) result.get("assign_company_id"))
                                    .groupName((String) result.get("group_name"))
                                    .qIdr1((String) result.get("q_idr1"))
                                    .qIdr2((String) result.get("q_idr2"))
                                    .qIdr3((String) result.get("q_idr3"))
                                    .qIdr4((String) result.get("q_idr4"))
                                    .build())
                    .map(customerStaffResult -> {
                        customerResponsibeCache.put(uid, customerStaffResult);
                        return customerStaffResult;
                    })
                    .getOrElse(CustomerStaffInfo.builder().build());
        }).toOption();
    }

    private Map<String, String> getFieldMappings() {
        return new HashMap<String, String>() {{
            put("user_id", "userId");
            put("user_name", "userName");
            put("company_id", "companyId");
            put("assign_company_id", "assignCompanyId");
            put("group_name", "groupName");
            put("q_idr1", "qIdr1");
            put("q_idr2", "qIdr2");
            put("q_idr3", "qIdr3");
            put("q_idr4", "qIdr4");
        }};
    }

    public void open() {
        Try.run(customerQuery::open)
                .onFailure(e -> log.error("Failed to open duty query", e));
    }

    public void close() {
        Try.run(customerQuery::close)
                .onFailure(e -> log.error("Failed to close duty query", e));
    }
}