package com.tencent.andata.smart.utils.util;

import com.tencent.andata.utils.JSONUtils;
import java.util.HashMap;
import java.util.Iterator;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.json.JSONObject;

public class GropuMsgClean {

    private static final JSONUtils jsonUtils = new JSONUtils();
    private static final HashMap<String, String> MSG_TRANS_MAP = new HashMap<>();

    static {
        MSG_TRANS_MAP.put("image", "[图片]");
        MSG_TRANS_MAP.put("voice", "[语音]");
        MSG_TRANS_MAP.put("video", "[视频]");
        MSG_TRANS_MAP.put("file", "[文件]");
        MSG_TRANS_MAP.put("markdown", "[markdown]");
        MSG_TRANS_MAP.put("chatrecord", "[聊天记录]");
    }

    public static String getDisplayContent(String msgContent, String msgType) throws JsonProcessingException {

        StringBuilder content = new StringBuilder();
        ObjectNode msgData = jsonUtils.getJSONObjectNodeByString(msgContent);
        JsonNode statement = msgData.get(msgType) == null ? msgData.get("info") : msgData.get(msgType);
        return parseMsg(statement, msgType, content).toString();

    }

    /**
     * 解析群消息
     *
     * @param statement 群消息
     * @param msgType 消息类型
     * @param result 解析结果
     * @return 解析结果
     */
    private static StringBuilder parseMsg(JsonNode statement, String msgType, StringBuilder result) {
        switch (msgType) {
            case "text":
            case "markdown":
                parseTextMessage(statement, result);
                break;
            case "mixed":
                JsonNode node = statement.get("item");
                Iterator<JsonNode> elements = node.elements();
                while (elements.hasNext()) {
                    ObjectNode item = (ObjectNode) elements.next();
                    parseMsg(item.get("content"), item.get("type").asText(), result);
                }
                break;
            default:
                result.append(MSG_TRANS_MAP.getOrDefault(msgType, "[其他类型消息]"));
                break;
        }
        return result;
    }

    private static void parseTextMessage(JsonNode statement, StringBuilder result) {
        try {
            result.append(statement.get("content").asText());
        } catch (Exception e) {
            handleTextMessageException(statement, result);
        }
    }

    private static void handleTextMessageException(JsonNode statement, StringBuilder result) {
        try {
            result.append(new JSONObject(statement.asText()).get("content"));
        } catch (Exception e) {
            result.append(statement.toString());
        }
    }
}