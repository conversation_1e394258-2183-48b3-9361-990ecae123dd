package com.tencent.andata.smart.etl.handler;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.enums.Scene;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GroupSceneHandler extends AbstractSceneHandler {

    private static final FlinkLog logger = FlinkLog.getInstance();

    public GroupSceneHandler() {
        super(null, null, null); // Group场景不需要ticketRepository
    }

    @Override
    public Option<Long> getTicketId(Strategy strategy) {
        return Option.of(strategy)
                .flatMap(this::getFirstData)
                .flatMap(data -> Option.of(data.get("group_id")))
                .flatMap(node -> Try.of(node::asText).toOption())
                .flatMap(groupId -> Try.of(() -> getGroupIdHashCode(groupId)).toOption())
                .onEmpty(() -> logger.error("[GroupSceneHandler] Failed to get groupId, strategy: " + strategy));
    }

    @Override
    public Option<String> getOperationId(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("msg_id").asText());
    }

    @Override
    public Option<String> getDutyResponsible(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("sales_supportor").asText());
    }

    @Override
    public Option<Long> getEventTime(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("msg_time").asLong());
    }

    @Override
    public Option<String> getCurrentStaffIdr1(Strategy strategy) {
        return Option.none(); // Group场景不需要此字段
    }

    @Override
    public Option<String> getCurrentPost(Strategy strategy) {
        return Option.none(); // Group场景不需要此字段
    }

    @Override
    public Option<String> getServiceChannel(Strategy strategy) {
        return getFirstData(strategy)
                .flatMap(data -> Option.of(data.get("is_big_customer")))
                .flatMap(node -> Try.of(node::asInt).toOption())
                .map(isBigCustomer -> strategy.scene == Scene.Group && isBigCustomer == 1 ? "大客户群" : null);
    }

    @Override
    public Option<String> getCurrentCustomerName(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> {
                    String company = data.get("company").asText();
                    String groupName = data.get("group_name").asText();
                    return company.isEmpty() ? groupName : company;
                });
    }

    private long getGroupIdHashCode(String groupId) {
        return Arrays.stream(groupId.codePoints().toArray())
                .reduce(0, Integer::sum);
    }
}