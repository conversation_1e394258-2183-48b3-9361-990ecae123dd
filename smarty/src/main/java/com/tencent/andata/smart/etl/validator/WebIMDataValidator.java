package com.tencent.andata.smart.etl.validator;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.etl.domain.RiskResult;
import java.io.Serializable;
import org.apache.commons.lang3.StringUtils;

/**
 * WebIM数据验证器
 * 负责验证WebIM场景数据的完整性
 */
public class WebIMDataValidator implements Serializable {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final long serialVersionUID = 1L;

    /**
     * 判断WebIM场景数据是否不完整
     * 检查ticketId、serviceChannel以及serviceSceneLevel2Name是否缺失
     *
     * @param res 风险结果对象
     * @return 是否数据不完整需要重试
     */
    public boolean isDataIncomplete(RiskResult res) {
        // 只处理WebIM场景
        if (!"WebIM".equals(res.getScene())) {
            return false;
        }

        // 检查关键字段是否为空
        boolean isServiceChannelEmpty = StringUtils.isBlank(res.getServiceChannel());
        boolean isTicketIdEmpty = res.getTicketId() == null || 0 == res.getTicketId();
        boolean isSceneLevel1NameEmpty = StringUtils.isBlank(res.getServiceSceneLevel1Name());

        if (isTicketIdEmpty || isServiceChannelEmpty || isSceneLevel1NameEmpty) {
            // 记录日志
            logger.warn(String.format("[WebIMDataValidator]validateWebIMFields: pk: %s," +
                            " ticketId: %s,  sceneLevel1Name: %s, serviceChannel: %s ",
                    res.getPk(),
                    res.getTicketId(),
                    res.getServiceSceneLevel1Name(),
                    res.getServiceChannel()));
            return true;
        }

        return false;
    }
}