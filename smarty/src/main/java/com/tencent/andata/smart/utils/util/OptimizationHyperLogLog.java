package com.tencent.andata.smart.utils.util;

import com.clearspring.analytics.hash.MurmurHash;
import com.clearspring.analytics.stream.cardinality.HyperLogLog;
import java.util.HashSet;
import java.util.Set;

public class OptimizationHyperLogLog {

    //hyperloglog结构
    private HyperLogLog hyperLogLog;
    //初始的一个set
    private Set<Integer> set;

    private double rsd;

    //hyperloglog的桶个数，主要内存占用
    private int bucket;

    public OptimizationHyperLogLog(double rsd) {
        this.rsd = rsd;
        set = new HashSet<>();
        this.bucket = 1 << (int) (Math.log(1.106 / rsd * (1.106 / rsd)) / Math.log(2.0));
    }

    //插入一条数据
    public void offer(Object object) {
        final int x = MurmurHash.hash(object);
        int currSize = set.size();
        if (hyperLogLog == null && currSize + 1 > bucket) {
            //升级为hyperloglog
            hyperLogLog = new HyperLogLog(rsd);
            for (int d : set) {
                hyperLogLog.offerHashed(d);
            }
            set.clear();
        }

        if (hyperLogLog != null) {
            hyperLogLog.offerHashed(x);
        } else {
            set.add(x);
        }
    }

    //获取大小
    public long cardinality() {
        if (hyperLogLog != null) {return hyperLogLog.cardinality();}
        return set.size();
    }
}