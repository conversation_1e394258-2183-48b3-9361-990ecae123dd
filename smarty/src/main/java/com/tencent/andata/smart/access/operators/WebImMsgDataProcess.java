package com.tencent.andata.smart.access.operators;

import static com.tencent.andata.utils.ExceptionWrapperUtil.function;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import com.tencent.andata.utils.HBaseSinkFunction.OperationType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import io.vavr.control.Try;
import io.vavr.control.Option;

public class WebImMsgDataProcess extends ProcessFunction<JsonNode, HbaseRow> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private OutputTag<JsonNode> webimOutputTag;

    public WebImMsgDataProcess(OutputTag<JsonNode> webimOutputTag) {
        this.webimOutputTag = webimOutputTag;
    }

    public static String extractWebImMsgContent(JsonNode msgData) {
        return extractMsgCtx(msgData);
    }

    /*
     * 根据外层的MsgType提取消息内容
     */
    private static String extractMsgCtx(JsonNode msgData) {
        return parseMessageData(msgData)
                .map(WebImMsgDataProcess::extractMessageContent)
                .getOrElse("");
    }

    private static Option<JsonNode> parseMessageData(JsonNode msgData) {
        return Try.of(() -> Option.of(msgData)
                        .map(JsonNode::asText)
                        .map(function(objectMapper::readTree))
                        .filter(node -> node.has("MsgType"))
                        .getOrNull())
                .onFailure(e -> logger.warn("Failed to parse message data，msgData：" + msgData + "errorMsg: " + e))
                .map(Option::of)
                .getOrElse(Option.none());
    }


    private static String extractMessageContent(JsonNode node) {
        int msgType = node.get("MsgType").asInt();
        return Match(msgType).of(
                Case($(4), () -> extractRichMessage(node)),
                Case($(8), () -> extractRobotMessage(node)),
                Case($(), () -> "")
        );
    }

    private static String extractRichMessage(JsonNode node) {
        return Option.of(node.get("Rich"))
                .filter(JsonNode::isArray)
                .map(rich -> {
                    StringBuilder content = new StringBuilder();
                    rich.forEach(r -> content.append(webImMsgClean(r)));
                    return content.toString();
                })
                .getOrElse("");
    }

    private static String extractRobotMessage(JsonNode node) {
        return Option.of(node.get("RobotRaw"))
                .map(JsonNode::asText)
                .map(text ->
                        Try.of(() -> objectMapper.readTree(text))
                                .onFailure(e -> logger.warn("Failed to extract robot message，node：" + node + "errorMsg: " + e))
                                .getOrElse(objectMapper.createObjectNode()))
                .filter(JsonNode::isArray)
                .map(WebImMsgDataProcess::handleRobotMessage)
                .getOrElse("");
    }

    /*
     * 处理机器人消息
     */
    private static String handleRobotMessage(JsonNode robotRaw) {
        StringBuilder content = new StringBuilder();
        robotRaw.forEach(node -> {
            String rawanswerType = node.get("answerType").asText();
            String markedAnswerType = StringUtils.equals(rawanswerType, "customer") ? "客户" : "坐席";
            content.append(markedAnswerType).append(": ");
            if (StringUtils.equals(rawanswerType, "welcome")) {
                StringBuilder hotQuestion = new StringBuilder();
                hotQuestion.append("猜您想了解：\n");
                node.get("hotQuestion").forEach(
                        hotItem -> hotQuestion
                                .append("      ")
                                .append(hotItem.asText())
                                .append("\n")
                );
                content.append(hotQuestion);
            }
            String html = node.get("html").asText().trim();
            content.append(html).append("\n");
        });
        return content.toString().trim();
    }


    /*
     * 根据内层MsgType提取消息内容
     */
    private static String webImMsgClean(JsonNode msgData) {
        return Try.of(() -> msgData.get("MsgType").asInt())
                .map(msgType -> Match(msgType).of(
                        Case($(1), () -> msgData.get("Content").asText()),
                        Case($(2), () -> "[图片]"),
                        Case($(3), () -> "[附件]"),
                        Case($(), () -> "")
                ))
                .onFailure(e -> logger.warn("Failed to extract webim message from msgData：" + msgData + " errorMsg: " + e))
                .getOrElse("");
    }

    @Override
    public void processElement(JsonNode json, Context ctx, Collector<HbaseRow> out) throws JsonProcessingException {
        JsonNode table = ObjectUtils.firstNonNull(json.get("table_name"), json.get("table"));
        if (table == null || !table.asText().equals("ods_im_online_customer_service_backend_data")) {
            return;
        }

        // webim流水数据写入hbase
        String rowKey;
        String getDisplayContent;

        String fTime = json.get("value_of_primary_key").asText().split("\\|")[0];
        String conversationId = json.get("conversation_id").asText();
        rowKey = conversationId + "-" + fTime;
        JsonNode msgData = json.get("msgdata");
        getDisplayContent = extractWebImMsgContent(msgData);
        ((ObjectNode) json).put("data_type", "webim");
        ((ObjectNode) json).put("display_content", getDisplayContent);

        // webim消息数据写入主流
        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put("data", objectMapper.writeValueAsString(json));
        out.collect(new HbaseRow(OperationType.INSERT, rowKey, "cf", rootNode));

        // 发送数据到旁路输出
        ctx.output(webimOutputTag, json);
    }
}