package com.tencent.andata.smart.utils;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import com.tencent.andata.smart.utils.splice.ItemSplice;
import com.tencent.andata.smart.utils.splice.factory.DefaultSpliceFactory;
import com.tencent.andata.smart.utils.splice.factory.SpliceFactory;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * Splice工具类
 */
public class SpliceUtils {

    private static final SpliceFactory spliceFactory = new DefaultSpliceFactory();

    public static Option<ItemSplice> getSplice(SpliceType spliceType) {
        return spliceFactory.createSplice(spliceType);
    }

    public String execute(ElementContext<JsonNode> item, Strategy strategy) {
        return getSplice(strategy.chunk.conversationSpliceType)
                .map(splice -> splice.splice(Option.of(item), Option.of(strategy)))
                .filter(spliced -> !spliced.isEmpty())
                .map(Option::get)
                .getOrNull();
    }
}