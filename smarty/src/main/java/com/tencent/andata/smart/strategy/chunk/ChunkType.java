package com.tencent.andata.smart.strategy.chunk;

/**
 * 分块类型
 */
public enum ChunkType {
    // 整个会话
    Conversation {
        @Override
        public String toString() {
            return "conversation";
        }
    },
    // 范围
    Range {
        @Override
        public String toString() {
            return "range";
        }
    },
    // 流水和会话组合
    Composite {
        @Override
        public String toString() {
            return "composite";
        }
    },
}