package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.processor.ConversationProcessor;
import com.tencent.andata.smart.strategy.chunk.provider.ChunkContextProvider;
import com.tencent.andata.smart.strategy.chunk.provider.ChunkContextProviderFactory;
import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.vavr.Function1;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.ConnectionFactory;


/**
 * 策略上下文构建异步处理类
 * <p>
 * 【重要设计说明】
 * 当前实现只处理第一个匹配的provider，不支持多个provider并行处理。
 * 如果未来需要支持多个provider并行处理，请参考以下注意事项：
 * <p>
 * 1. ResultFuture.complete 只能被调用一次：
 * - 参考 StrategyAnalyzeLLMProcess 中的并发处理bug修复
 * - 需要等待所有provider处理完成后，再调用一次resultFuture.complete
 * - 不能在每个provider处理完成后都调用resultFuture.complete
 * <p>
 * 2. 并行处理设计：
 * - 使用 CompletableFuture.allOf 等待所有provider处理完成
 * - 在 whenComplete 回调中统一调用一次 resultFuture.complete
 * - 确保所有provider的结果都被正确处理和合并
 * <p>
 * 3. 错误处理：
 * - 需要处理部分provider成功、部分失败的情况
 * - 可能需要实现结果合并策略，处理多个provider的结果
 * - 考虑是否需要设置处理状态标记
 * <p>
 * 4. 性能考虑：
 * - 评估并行处理对系统资源的影响
 * - 考虑是否需要限制并行处理的provider数量
 * - 可能需要实现provider优先级或选择策略
 */
@Slf4j
public class StrategyChunkBuildConversationAsyncProcess extends RichAsyncFunction<Strategy, Strategy> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private final String hbaseZookeeperQuorum;
    private final String zookeeperZnodeParent;

    private AsyncConnection asyncConnection;
    private ScheduledExecutorService executorService;
    private List<ChunkContextProvider> providers;
    private ConversationProcessor conversationProcessor;
    private transient RetryConfig retryConf;

    public StrategyChunkBuildConversationAsyncProcess(
            String hbaseZookeeperQuorum,
            String zookeeperZnodeParent) {
        this.hbaseZookeeperQuorum = hbaseZookeeperQuorum;
        this.zookeeperZnodeParent = zookeeperZnodeParent;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        initHBaseConnection();
        initProviders();

        // 初始化相关组件
        this.conversationProcessor = new ConversationProcessor();

        // 使用指数退避重试策略
        this.retryConf = RetryConfig
                .custom()
                .maxAttempts(5)
                .intervalFunction(IntervalFunction.ofExponentialBackoff(20000, 2.0))
                .build();
    }

    private void initHBaseConnection() throws Exception {
        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("hbase.zookeeper.quorum", hbaseZookeeperQuorum);
        conf.set("zookeeper.znode.parent", zookeeperZnodeParent);

        asyncConnection = ConnectionFactory.createAsyncConnection(conf).get();
        executorService = Executors.newScheduledThreadPool(8);
    }

    private void initProviders() {
        ChunkContextProviderFactory factory = new ChunkContextProviderFactory(asyncConnection);
        this.providers = factory.getProviders();
    }

    @Override
    public void asyncInvoke(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        // CompletableFuture.runAsync(() -> processStrategy(strategy, resultFuture), executorService);
        // 创建异步任务
        CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                processStrategyWithTimeout(strategy, resultFuture), executorService);

        // 添加超时处理
        scheduleTimeout(future, 5);

        // 添加异常处理
        future.exceptionally(handleAsyncException(resultFuture));
    }


    /**
     * 处理策略并添加超时
     * <p>
     * 【注意】当前实现只处理第一个匹配的provider。
     * 如果未来需要支持多个provider并行处理，需要修改为：
     * 1. 查找所有匹配的providers
     * 2. 并行处理所有providers
     * 3. 等待所有处理完成后，再调用一次resultFuture.complete
     */
    private void processStrategyWithTimeout(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        Try.run(() -> processStrategy(strategy, resultFuture))
                .onFailure(e -> {
                    logger.error(String.format("[StrategyChunk] Unexpected error in processStrategy: %s", e));
                    resultFuture.completeExceptionally(e);
                });
    }

    // 超时处理函数
    private void scheduleTimeout(CompletableFuture<Void> future, long timeoutMin) {
        executorService.schedule(() -> {
            if (!future.isDone()) {
                future.completeExceptionally(new TimeoutException("Operation timed out after " + timeoutMin + " min"));
            }
        }, timeoutMin, TimeUnit.MINUTES);
    }

    // 异常处理函数
    private Function1<Throwable, Void> handleAsyncException(ResultFuture<Strategy> resultFuture) {
        return e -> {
            logger.error(String.format("[StrategyChunk] Async execution failed: %s", e));
            resultFuture.completeExceptionally(e);
            return null;
        };
    }


    /**
     * 处理策略的主函数
     * <p>
     * 【重要说明】当前实现只处理第一个匹配的provider。
     * 如果未来需要支持多个provider并行处理，需要修改为：
     * <p>
     * 1. 查找所有匹配的providers：
     * List<ChunkContextProvider> supportedProviders = findSupportedProviders(strategy);
     * <p>
     * 2. 并行处理所有providers：
     * CompletableFuture<?>[] futures = supportedProviders.stream()
     * .map(provider -> executeWithRetry(strategy, provider))
     * .toArray(CompletableFuture[]::new);
     * <p>
     * 3. 等待所有处理完成：
     * CompletableFuture.allOf(futures)
     * .whenComplete((result, throwable) -> {
     * // 处理异常
     * // 合并所有provider的结果
     * // 只调用一次resultFuture.complete
     * });
     * <p>
     * 4. 注意：不能在executeWithRetry中直接调用resultFuture.complete，
     * 否则会导致只有第一个完成的provider的结果被返回
     */
    private void processStrategy(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        Option.of(findSupportedProvider(strategy))
                // 如果找到了支持的provider，执行异步处理
                .map(provider -> executeWithRetry(strategy, provider, resultFuture))
                // 如果没有找到支持的provider，直接返回原始strategy
                .getOrElse(() -> {resultFuture.complete(Collections.singletonList(strategy)); return null;});
    }

    /**
     * 查找支持的ChunkContextProvider
     * <p>
     * 【注意】当前实现只返回第一个匹配的provider。
     * 如果未来需要支持多个provider并行处理，需要修改为：
     * <p>
     * private List<ChunkContextProvider> findSupportedProviders(Strategy strategy) {
     * return providers.stream()
     * .filter(p -> p.supports(strategy))
     * .collect(Collectors.toList());
     * }
     */
    private ChunkContextProvider findSupportedProvider(Strategy strategy) {
        return providers.stream()
                .filter(p -> p.supports(strategy))
                .findFirst()
                .orElse(null);
    }

    /**
     * 使用重试机制执行处理
     * <p>
     * 【重要说明】当前实现在成功和失败时都会调用resultFuture.complete。
     * 如果未来需要支持多个provider并行处理，需要修改为：
     * <p>
     * 1. 移除参数列表中的resultFuture
     * 2. 不再直接调用resultFuture.complete
     * 3. 在异常处理中只更新strategy对象，不调用resultFuture.complete
     * 4. 返回CompletableFuture，由上层方法统一处理完成
     * <p>
     * 参考 StrategyAnalyzeLLMProcess 中的修复方案
     */
    private CompletableFuture<Void> executeWithRetry(Strategy strategy, ChunkContextProvider provider, ResultFuture<Strategy> resultFuture) {
        Retry retry = Retry.of("ConversationProcessRetry", this.retryConf);

        // 重试失败日志
        retry.getEventPublisher().onRetry(event ->
                logger.error(String.format("[StrategyChunk] Retry attempt %s failed. Reason: %s",
                        event.getNumberOfRetryAttempts(), event.getLastThrowable())));

        return retry.executeCompletionStage(executorService, () -> processStrategyWithRetry(strategy, provider))
                .toCompletableFuture()
                .thenApply(result -> {
                    // 成功时记录日志并完成resultFuture
                    // logger.info(String.format("[StrategyChunk] Successfully retry processed strategy for scene: %s", strategy.sceneIdentify));
                    resultFuture.complete(Collections.singletonList(strategy));
                    return result;
                })
                .exceptionally(throwable -> {
                    // 重试失败后，执行fallback并获取返回的Strategy
                    fallback(strategy, throwable)
                            .thenAccept(fallbackStrategy -> {
                                // 使用fallback返回的Strategy完成resultFuture
                                logger.info(String.format("[StrategyChunk] Completing with fallback strategy for scene: %s", fallbackStrategy.sceneIdentify));
                                resultFuture.complete(Collections.singletonList(fallbackStrategy));
                            });
                    return null;
                });
    }

    /**
     * 处理策略并构建会话，支持重试机制
     * <p>
     * 【注意】如果未来支持多个provider并行处理，需要确保：
     * 1. 每个provider的处理结果都能正确更新到strategy对象
     * 2. 不同provider的结果不会相互覆盖
     * 3. 可能需要实现结果合并策略
     * 处理流程：
     * 1. 调用会话处理器处理策略
     * 2. 验证会话结果是否有效
     * 3. 更新策略对象
     * 4. 完成异步操作
     *
     * @param strategy 需要处理的策略对象
     * @param provider 上下文提供者
     * @return 处理结果的CompletionStage
     */
    private CompletionStage<Void> processStrategyWithRetry(Strategy strategy, ChunkContextProvider provider) {
        CompletableFuture<Void> resultFuture = new CompletableFuture<>();

        /************************************************************************************************
         * 注意：这里使用了函数式编程的链式调用模式，确保异步操作能正确执行
         * <p>
         * 1. 函数式编程中的map操作:
         *    - map是一个转换操作，它返回一个新的对象而不修改原对象
         *    - 如果map的返回值未被使用，其内部的操作可能不会执行
         * <p>
         * 2. 正确的使用方式:
         *    - 使用链式调用: Try.of(...).map(...).onFailure(...) - map的结果被后续操作使用
         *    - 或使用消费操作: processTry.forEach(...) / processTry.peek(...) - 专门用于执行副作用
         * <p>
         * 3. 错误的使用方式:
         *    - 独立调用map且不使用返回值: processTry.map(...); - 可能导致内部操作不执行
         *    - 这会导致异步操作链断裂，resultFuture永远不会完成，最终导致超时
         * <p>
         * 相关案例对比: 参见StrategyAnalyzeLLMProcess.requestWithRetry方法中的链式调用模式
         ************************************************************************************************/

        Try.of(() -> this.conversationProcessor.process(strategy, provider))
                .map(ctx -> ctx.thenAccept(c -> handleCtxRst(c, strategy, resultFuture)))
                .onFailure(resultFuture::completeExceptionally);

        return resultFuture;
    }

    /**
     * 处理会话结果
     * <p>
     * 【注意】如果未来支持多个provider并行处理，需要：
     * 1. 考虑不同provider的结果如何合并
     * 2. 可能需要添加provider标识，区分不同provider的结果
     * 3. 可能需要实现结果优先级策略
     *
     * @param conversation 会话结果
     * @param strategy 策略对象
     * @param resultFuture 结果Future
     */
    private void handleCtxRst(String conversation, Strategy strategy, CompletableFuture<Void> resultFuture) {
        Option.of(conversation)
                .filter(c -> isValidConversation(c, strategy))
                .peek(result -> updateStrategy(strategy, result))
                .peek(result -> resultFuture.complete(null))
                .onEmpty(() -> resultFuture.completeExceptionally(new RuntimeException(String.format("strategyId: %s conversation is empty!", strategy.sceneIdentify))));
    }

    /**
     * 更新策略对象
     * <p>
     * 【注意】如果未来支持多个provider并行处理，需要：
     * 1. 考虑如何合并多个provider的结果
     * 2. 可能需要添加provider标识
     * 3. 可能需要实现结果优先级策略
     *
     * @param strategy 策略对象
     * @param conversation 会话结果
     */
    private void updateStrategy(Strategy strategy, String conversation) {
        strategy.chunk.conversation = conversation;
        strategy.chunk.operations = null;
    }


    // 验证会话是否有效
    private boolean isValidConversation(String conversation, Strategy strategy) {
        if (strategy.name.equals("WebIM结单质检")) {return true;}
        return !StringUtils.isBlank(conversation)
                && !StringUtils.equalsIgnoreCase(conversation, "null");
    }


    /**
     * 失败后的回退处理
     * <p>
     * 【注意】如果未来支持多个provider并行处理，需要：
     * 1. 考虑部分provider失败的情况
     * 2. 可能需要实现更复杂的fallback策略
     * 3. 可能需要记录哪些provider失败
     */
    private CompletableFuture<Strategy> fallback(Strategy strategy, Throwable e) {
        logger.error(String.format("[StrategyChunk] 场景id：%s " +
                "Failed to process strategy after %s retries:" +
                " %s", strategy.sceneIdentify, retryConf.getMaxAttempts(), e));
        return CompletableFuture.completedFuture(strategy);
    }

    @Override
    public void close() throws Exception {
        Try.run(() -> {
            if (asyncConnection != null) {asyncConnection.close();}
            if (executorService != null) {executorService.shutdown();}
        }).onFailure(e -> logger.error("[StrategyChunk] Failed to close resources: " + e));
        super.close();
    }

    /**
     * 处理超时情况
     * <p>
     * 【注意】如果未来支持多个provider并行处理，需要：
     * 1. 考虑部分provider超时的情况
     * 2. 可能需要实现超时后的结果合并策略
     * 3. 可能需要记录哪些provider超时
     */
    @Override
    public void timeout(Strategy strategy, ResultFuture<Strategy> resultFuture) {
        String sceneIdentify = strategy.sceneIdentify;
        logger.error(String.format("场景id：%s 获取上下文信息超时！", sceneIdentify));

        // 超时时直接返回原始strategy
        resultFuture.complete(Collections.singletonList(strategy));
    }
}