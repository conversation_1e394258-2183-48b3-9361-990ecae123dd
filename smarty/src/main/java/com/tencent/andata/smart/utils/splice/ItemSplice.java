package com.tencent.andata.smart.utils.splice;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 拼接接口
 */
public interface ItemSplice {

    /**
     * 拼接内容
     *
     * @param item 待处理的JsonNode
     * @return 拼接结果, 使用Option包装处理空值
     */
    Option<String> splice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy);
}