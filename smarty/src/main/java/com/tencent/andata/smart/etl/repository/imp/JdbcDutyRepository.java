package com.tencent.andata.smart.etl.repository.imp;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.tencent.andata.smart.etl.domain.DutyInfo;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.tools.ValidationException;

@Slf4j
public class JdbcDutyRepository implements DutyRepository, Serializable {

    // 缓存配置
    private static final int CACHE_SIZE = 30000;
    private static final int CACHE_EXPIRE_MINUTES = 720;
    private final HashMapJDBCLookupQuery dutyQuery;
    private Cache<Integer, DutyInfo> dutyResponsibeCache;

    public JdbcDutyRepository(DatabaseConf dataWareConf) throws ValidationException {
        // 初始化缓存
        initializeCache();
        this.dutyQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                JDBCSqlBuilderImpl.builder()
                        .tableName("dim_incident_duty")
                        .selectFieldWithAlias(getFieldMappings())
                        .conditionKeyList(Arrays.asList("duty_id"))
                        .databaseEnum(DatabaseEnum.PGSQL)
                        .limit(1)
        );
    }

    private void initializeCache() {
        dutyResponsibeCache = CacheBuilder.newBuilder()
                .maximumSize(CACHE_SIZE)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build();
    }

    @Override
    public Option<DutyInfo> findById(Integer dutyId) {
        return Option.of(dutyId)
                .flatMap(id -> Option.of(dutyResponsibeCache.getIfPresent(id))
                        .orElse(() -> queryFromDatabase(id)));
    }


    private Option<DutyInfo> queryFromDatabase(Integer dutyId) {
        return Try.of(() -> {
            HashMap<String, Object> params = new HashMap<>();
            params.put("duty_id", dutyId);

            return Option.of(dutyQuery.query(params))
                    .filter(results -> !results.isEmpty())
                    .map(results -> results.get(0))
                    .map(result ->
                            DutyInfo.builder()
                                    .responsible((String) result.get("responsible"))
                                    .dutyName((String) result.get("dutyName"))
                                    .build())
                    .map(dutyInfoResult -> {
                        dutyResponsibeCache.put(dutyId, dutyInfoResult);
                        return dutyInfoResult;
                    })
                    .getOrElse(DutyInfo.builder().build());
        }).toOption();
    }


    private Map<String, String> getFieldMappings() {
        return new HashMap<String, String>() {{
            put("responsible", "responsible");
            put("duty_name", "dutyName");
        }};
    }

    public void open() {
        Try.run(dutyQuery::open)
                .onFailure(e -> log.error("Failed to open duty query", e));
    }

    public void close() {
        Try.run(dutyQuery::close)
                .onFailure(e -> log.error("Failed to close duty query", e));
    }
}