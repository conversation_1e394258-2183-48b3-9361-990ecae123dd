package com.tencent.andata.smart.etl.process;

import static com.tencent.andata.utils.JSONUtils.isNestedJson;
import static net.minidev.json.JSONValue.isValidJson;
import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

@Slf4j
public class SplitPreStrategyProcess extends ProcessFunction<Strategy, Strategy> {

    private final String preStrategyIds;
    private final OutputTag<JsonNode> L1AgentStreamTag;
    private static final ObjectMapper objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);

    public SplitPreStrategyProcess(OutputTag<JsonNode> preAgentTag, String preStrategyIds) {
        this.L1AgentStreamTag = preAgentTag;
        this.preStrategyIds = preStrategyIds;
    }

    @Override
    public void processElement(Strategy strategy, Context ctx, Collector<Strategy> collector) throws Exception {
        List<Integer> reTryStrategys = Arrays.stream(preStrategyIds.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        // Object转成JsonNode
        JsonNode data = strategy.trigger.data[0];
        if (reTryStrategys.contains(strategy.id)) {
            // 将重试策略中的上下文信息写入到原数据中
            ((ObjectNode) data).put("context", strategy.chunk.conversation);
            ((ObjectNode) data).put("data_type", "retry_strategy");
            ObjectNode resNode = objectMapper.createObjectNode();
            // 将所有重试策略中的res合并到原数据中
            // TODO analyzes如果有多条，并却每条analyzes的res都是json，如果json的key一样，这里会相互覆盖
            for (Analyze analyze : strategy.analyzes) {
                try {
                    if (isValidJson(analyze.res)) {
                        JsonNode res = objectMapper.readTree(analyze.res);
                        Iterator<String> name = res.fieldNames();
                        while (name.hasNext()) {
                            String nodeName = name.next();
                            resNode.set(nodeName, res.get(nodeName));
                            ((ObjectNode) data).set(nodeName, res.get(nodeName));
                        }
                    }
                } catch (Exception e) {
                    log.error("[SplitPreStrategyProcess] analyzes res is not json. Strategy: {}", strategy);
                    continue;
                }
                // 将模型所有结果写入到原数据
                ((ObjectNode) data).set("preLlmRes", resNode);
            }
            ctx.output(L1AgentStreamTag, data);
            return;
        }
        for (Analyze analyze : strategy.analyzes) {
            // 如果模型结果是嵌套json，直接进行重试
            if (isNestedJson(analyze.res)) {
                ctx.output(L1AgentStreamTag, data);
                return;
            }
        }

        collector.collect(strategy);
    }
}