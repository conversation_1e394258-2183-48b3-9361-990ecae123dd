package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.struct.regnine.MessagePack;
import com.tencent.andata.struct.regnine.REngineMessage;
import com.tencent.andata.struct.regnine.builder.MessageDataBuilder;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class OpinionDataToREngineMessageProcess extends ProcessFunction<String, REngineMessage> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    public static String tableName = "smarty_public_opinion";

    @Override
    public void processElement(String s, Context context, Collector<REngineMessage> collector) throws Exception {
        collector.collect(covertToRengineMessage(tableName, convertToMessageRowData(s)));
    }

    /**
     * 构造REngineMessage
     *
     * @param tableName 表名
     * @param rowData 行数据
     * @return REngineMessage
     */
    private REngineMessage covertToRengineMessage(String tableName, MessageRowData rowData) {
        // 创建 REngineMessage
        REngineMessage message = new REngineMessage();
        // 订阅表名
        message.setSubScribeName(tableName);
        message.setMessagePack(
                MessagePack.builder()
                        .addMessageData(new MessageDataBuilder().setDstTableName(tableName).addRowData(rowData).build())
                        .build()
        );

        logger.info("REngineMessage: " + message);
        return message;
    }

    /**
     * 先转成RowData
     *
     * @param str 源数据
     * @return MessageRowData
     */
    private MessageRowData convertToMessageRowData(String str) {
        // 按照REngine的数据格式构造数据
        return MessageRowData.of("data_change", "insert", "{}", str, System.currentTimeMillis());
    }
}