package com.tencent.andata.smart.enums;

import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.Getter;

/**
 * 操作者类型枚举
 */
@Getter
public enum OperatorType {
    STAFF("2", "坐席"),
    CUSTOMER("1", "客户"),
    SYSTEM("3", "系统"),
    XINGYUN("4", "星云系统"),
    FAULT("5", "故障通知"),
    EVENT_MANAGER("6", "事件经理"),
    TOOL("7", "工具");

    private final String code;
    private final String desc;

    OperatorType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OperatorType fromCode(String code) {
        return List.of(values())
                   .filter(t -> t.code.equals(code))
                   .getOrNull();
    }

    public static String getDesc(String code) {
        return List.of(values())
                   .filter(t -> t.code.equals(code))
                   .map(OperatorType::getDesc)
                   .getOrNull();
    }
} 