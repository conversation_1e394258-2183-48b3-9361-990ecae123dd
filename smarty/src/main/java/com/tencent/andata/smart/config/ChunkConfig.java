package com.tencent.andata.smart.config;

import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import java.util.Arrays;

public class ChunkConfig {

    // 工单回复
    public static Chunk TicketExternalReplyChunk = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.TicketQualityInspection)
            .aviatorExpressions(Arrays
                    .asList(
                            "scene == 'Ticket' && getNested('trigger.data[0].service_channel') == 27 ? putNested('chunk.type', 'Composite') : returnEnv()",
                            "scene == 'Ticket' && getNested('trigger.data[0].service_channel') == 27 ? putNested('trigger.data[0].start_time', getNested('trigger.data[0].start_time') - 604800000) : returnEnv()")
                    .toArray(new String[0]))
            .build();

    // WebIM回复
    public static Chunk WebIMReply = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.WebIMQualityInspection)
            .build();

    // 工单风控
    public static Chunk TicketPublicOpinionReply = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.TicketPublicOpinion)
            .build();

    public static Chunk WebIMPublicOpinionReply = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.WebIMPublicOpinion)
            .build();

    public static Chunk TicketPriorityAgentReply = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.TicketPriorityAgent)
            .build();


    public static Chunk C2000PublicOpinionReply = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.C2000PublicOpinion)
            .build();

    public static Chunk KAGroupOpinionReply = Chunk
            .builder()
            .type(ChunkType.Range)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.GroupPublicOpinion)
            .aviatorExpressions(Arrays
                    .asList(
                            "putNested('trigger.data[0].start_time', getNested('trigger.data[0].msg_time') - 28800000)",
                            "putNested('trigger.data[0].end_time', getNested('trigger.data[0].msg_time') + 7200000)")
                    .toArray(new String[0]))
            .build();

    public static Chunk CallCenterReply = Chunk
            .builder()
            .type(ChunkType.Conversation)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.CallCenterQualityInspection)
            .build();
}