package com.tencent.andata.smart.etl.serializer;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class KafkaStrategyStringSerializer implements SerializationSchema<Strategy> {

    private final ObjectMapper objectMapper;

    public KafkaStrategyStringSerializer() {
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public byte[] serialize(Strategy strategy) {
        try {
            String res = this.objectMapper.writeValueAsString(strategy);
            return res.getBytes();
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}