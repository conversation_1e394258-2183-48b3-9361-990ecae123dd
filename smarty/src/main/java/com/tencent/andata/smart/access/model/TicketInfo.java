package com.tencent.andata.smart.access.model;

import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.math.BigInteger;

/**
 * 工单信息数据传输对象
 * 用于在系统各层之间传递工单信息
 */
public class TicketInfo {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final long ticketId;
    private final Integer serviceSceneChecked;
    private final Integer serviceScene;
    private final Long serviceChannel;
    private final String currentOperator;
    private final Integer factAssign;
    private final Timestamp createTime;
    private final String responsible;
    private final Integer companyId;
    private final BigInteger ownerUin;
    private final String groupId;
    private final Integer priority;
    private final String question;
    private final Integer status;
    private final Integer post;
    private final String name;
    private final BigInteger uin;
    private final String title;

    // 转换后的值
    private final long longUin;
    private final long longOwnerUin;
    private final String formattedCreateTime;
    private final int effectiveServiceScene;

    private TicketInfo(Builder builder) {
        this.ticketId = builder.ticketId;
        this.serviceSceneChecked = builder.serviceSceneChecked;
        this.serviceScene = builder.serviceScene;
        this.serviceChannel = builder.serviceChannel;
        this.currentOperator = builder.currentOperator;
        this.factAssign = builder.factAssign;
        this.createTime = builder.createTime;
        this.responsible = builder.responsible;
        this.companyId = builder.companyId;
        this.ownerUin = builder.ownerUin;
        this.groupId = builder.groupId;
        this.priority = builder.priority;
        this.question = builder.question;
        this.status = builder.status;
        this.post = builder.post;
        this.name = builder.name;
        this.uin = builder.uin;
        this.title = builder.title;

        // 计算转换后的值
        this.longUin = this.uin != null ? this.uin.longValue() : 0L;
        this.longOwnerUin = this.ownerUin != null ? this.ownerUin.longValue() : 0L;
        this.formattedCreateTime = this.createTime != null ?
                this.createTime.toLocalDateTime().format(FORMATTER) : "";
        this.effectiveServiceScene = (this.serviceSceneChecked != null && this.serviceSceneChecked > 0) ?
                this.serviceSceneChecked : (this.serviceScene != null ? this.serviceScene : -1);
    }

    public long getTicketId() {
        return ticketId;
    }

    public Integer getServiceSceneChecked() {
        return serviceSceneChecked;
    }

    public Integer getServiceScene() {
        return serviceScene;
    }

    public Long getServiceChannel() {
        return serviceChannel;
    }

    public String getCurrentOperator() {
        return currentOperator;
    }

    public Integer getFactAssign() {
        return factAssign;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public String getResponsible() {
        return responsible;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public BigInteger getOwnerUin() {
        return ownerUin;
    }

    public String getGroupId() {
        return groupId;
    }

    public Integer getPriority() {
        return priority;
    }

    public String getQuestion() {
        return question;
    }

    public Integer getStatus() {
        return status;
    }

    public Integer getPost() {
        return post;
    }

    public String getName() {
        return name;
    }

    public BigInteger getUin() {
        return uin;
    }

    public String getTitle() {
        return title;
    }

    public long getLongUin() {
        return longUin;
    }

    public long getLongOwnerUin() {
        return longOwnerUin;
    }

    public String getFormattedCreateTime() {
        return formattedCreateTime;
    }

    public int getEffectiveServiceScene() {
        return effectiveServiceScene;
    }

    /**
     * Builder模式实现，用于构建TicketInfo对象
     */
    public static class Builder {

        private long ticketId;
        private Integer serviceSceneChecked;
        private Integer serviceScene;
        private Long serviceChannel;
        private String currentOperator;
        private Integer factAssign;
        private Timestamp createTime;
        private String responsible;
        private Integer companyId;
        private BigInteger ownerUin;
        private String groupId;
        private Integer priority;
        private String question;
        private Integer status;
        private Integer post;
        private String name;
        private BigInteger uin;
        private String title;

        public Builder(long ticketId) {
            this.ticketId = ticketId;
        }

        public Builder serviceSceneChecked(Integer val) {
            serviceSceneChecked = val;
            return this;
        }

        public Builder serviceScene(Integer val) {
            serviceScene = val;
            return this;
        }

        public Builder serviceChannel(Long val) {
            serviceChannel = val;
            return this;
        }

        public Builder currentOperator(String val) {
            currentOperator = val;
            return this;
        }

        public Builder factAssign(Integer val) {
            factAssign = val;
            return this;
        }

        public Builder createTime(Timestamp val) {
            createTime = val;
            return this;
        }

        public Builder responsible(String val) {
            responsible = val;
            return this;
        }

        public Builder companyId(Integer val) {
            companyId = val;
            return this;
        }

        public Builder ownerUin(BigInteger val) {
            ownerUin = val;
            return this;
        }

        public Builder groupId(String val) {
            groupId = val;
            return this;
        }

        public Builder priority(Integer val) {
            priority = val;
            return this;
        }

        public Builder question(String val) {
            question = val;
            return this;
        }

        public Builder status(Integer val) {
            status = val;
            return this;
        }

        public Builder post(Integer val) {
            post = val;
            return this;
        }

        public Builder name(String val) {
            name = val;
            return this;
        }

        public Builder uin(BigInteger val) {
            uin = val;
            return this;
        }

        public Builder title(String val) {
            title = val;
            return this;
        }

        public TicketInfo build() {
            return new TicketInfo(this);
        }
    }
}