package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.utils.splice.base.AbstractItemSplice;
import com.tencent.andata.smart.enums.WebIMOperationType;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class WebIMOperationRCSplice extends AbstractItemSplice {

    protected static final String CUSTOMER_REPLY_TEMPLATE = "客户回复：%s\n";
    protected static final String STAFF_REPLY_TEMPLATE = "坐席回复：%s\n";

    private static final Map<WebIMOperationType, String> OPERATION_TEMPLATES = HashMap.of(
            // 客户回复
            WebIMOperationType.SEND_USER_MSG, CUSTOMER_REPLY_TEMPLATE,
            WebIMOperationType.SEND_CHAT_CALLBACK_MSG, CUSTOMER_REPLY_TEMPLATE,

            // 坐席回复
            WebIMOperationType.SEND_ZX_MSG, STAFF_REPLY_TEMPLATE,

            // 创建会话
            WebIMOperationType.CREATE, "%s\n"
    );


    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item
                .filter(i -> isBeforeTriggeredTimestamp(item, strategy))
                .map(ElementContext::getCurrent)
                .flatMap(this::extractReplyContent)
                .flatMap(content ->
                        getOperationType(item.get().getCurrent())
                                .flatMap(WebIMOperationType::fromCode)
                                .map(type -> formatReply(type, content))
                )
                .getOrElse("");
    }

    protected Option<String> extractReplyContent(JsonNode node) {
        return Option.of(node)
                .map(n -> n.get("display_content"))
                .map(JsonNode::asText)
                .map(this::cleanContent)
                .filter(content -> !isNullContent(content));
    }

    private String formatReply(WebIMOperationType type, String content) {
        // 然后使用模板格式化，兼容提单模板数据
        return OPERATION_TEMPLATES
                .get(type)
                .map(template -> String.format(template, handleQuestionTemplate(type, content)))
                .getOrElse("");
    }

    /*
     * 处理webim提单模板数据
     */
    private String handleQuestionTemplate(WebIMOperationType type, String content) {
        return Option.when(
                type == WebIMOperationType.CREATE && content.contains("提单信息："),
                () -> content.replace("提单信息：", "客户回复：")
        ).getOrElse(content);
    }
}