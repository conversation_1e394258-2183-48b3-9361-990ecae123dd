package com.tencent.andata.smart.config;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;


public class StrategyConfig {

    // TODO 后续看怎么拆成配置化
    // 质检策略
    public static List<Strategy> QUALITY_STRATEGY_LIST;
    // 风控策略
    public static List<Strategy> OPINION_STRATEGY_LIST;

    public static List<Strategy> AGENT_STRATEGY_LIST;


    static {
        Trigger trigger = Trigger.builder()
                .type(TriggerType.Immediately)
                .needPersist(true)
                .build();

        OPINION_STRATEGY_LIST = new ArrayList<Strategy>() {{
            add(
                    Strategy.builder()
                            .id(3)
                            .name("MC工单风控识别")
                            .scene(Scene.Ticket)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'ticket_operation' && " +
                                                    "service_channel == 3 && " +
                                                    "ticket_status != 3 && " +
                                                    "operation_type == 9 && " +
                                                    "regexMatch(risk_type, '内部投诉|舆情监管|已诉讼|一般催单|业务疑似影响|业务影响|负面情绪|紧急催单|复现|赔偿问题|情绪激动|威胁切量|自残行为|数据丢失|计费问题|微信业务受影响|证书问题')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.TicketPublicOpinionReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.Public_Opinion_Risk).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(4)
                            .name("WebIM工单风控识别")
                            .scene(Scene.WebIM)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'webim' && " +
                                                    "!include(seq.list(6, 12), status) && " +
                                                    "regexMatch(source, '^(PRESALE|MC|MP|MA|QDSAAS|78)$') && " +
                                                    "(rpc_name == 'SendUserMsg' || rpc_name == 'SendChatCallbackMsg' || rpc_name == 'CreateConversation') && " +
                                                    "regexMatch(risk_type, '内部投诉|舆情监管|已诉讼|一般催单|业务疑似影响|业务影响|负面情绪|紧急催单|复现|赔偿问题|情绪激动|威胁切量|自残行为|数据丢失|计费问题|微信业务受影响|证书问题')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.WebIMPublicOpinionReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.Public_Opinion_Risk).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(8)
                            .name("C2000内部服务台工单风控识别")
                            .scene(Scene.C2000)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'c2000' && " +
                                                    "ticket_status != 3 && " +
                                                    "sender_type == '客户' && " +
                                                    "regexMatch(risk_type, '内部投诉|舆情监管|已诉讼|一般催单|业务疑似影响|业务影响|负面情绪|紧急催单|复现|赔偿问题|情绪激动|威胁切量|自残行为|数据丢失|计费问题|微信业务受影响|证书问题')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.C2000PublicOpinionReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.Public_Opinion_Risk).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(9)
                            .name("KA大客户群风控识别")
                            .scene(Scene.Group)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'group' && " +
                                                    "is_big_customer == 1 && " +
                                                    "regexMatch(risk_type, '内部投诉|舆情监管|已诉讼|一般催单|业务疑似影响|业务影响|负面情绪|紧急催单|复现|赔偿问题|情绪激动|威胁切量|自残行为|数据丢失|计费问题|微信业务受影响|证书问题')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.KAGroupOpinionReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.Public_Opinion_Risk).toArray(new Analyze[0]))
                            .build()
            );
        }};
        QUALITY_STRATEGY_LIST = new ArrayList<Strategy>() {{
            add(
                    Strategy.builder()
                            .id(1)
                            .name("MC工单结单质检")
                            .scene(Scene.Ticket)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'ticket_operation' && " +
                                                    "operation_type == 16 && " +
                                                    "regexMatch(service_channel, '^(3)$')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.TicketExternalReplyChunk)
                            .analyzes(Arrays.asList(
                                    AnalyzeConfig.Professional_Skills,
                                    AnalyzeConfig.Service_Awareness,
                                    AnalyzeConfig.Communication_Skills,
                                    AnalyzeConfig.Ticket_Service_Timeliness,
                                    AnalyzeConfig.Service_Specifications).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(2)
                            .name("WebIM结单质检")
                            .scene(Scene.WebIM)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'webim' && " +
                                                    //"regexMatch(status, '^(6|7|8|9|12)$') && " +
                                                    "regexMatch(source, '^(PRESALE|MC|MP|MA|78)$') && " +
                                                    "regexMatch(rpc_name, '^(AutoFinishConversation|FinishUserConversation|FinishConversation)$')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.WebIMReply)
                            .analyzes(Arrays.asList(
                                    AnalyzeConfig.Professional_Skills,
                                    AnalyzeConfig.Service_Awareness,
                                    AnalyzeConfig.Communication_Skills,
                                    AnalyzeConfig.WebIM_Service_Timeliness,
                                    AnalyzeConfig.Service_Specifications).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(10)
                            .name("KA大客户群结单质检")
                            .scene(Scene.Ticket) // 通过工单流水触发，所以还是Ticket
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'ticket_operation' && " +
                                                    "operation_type == 16 && " +
                                                    "regexMatch(service_channel, '^(27)$')")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.TicketExternalReplyChunk)
                            .analyzes(Arrays.asList(
                                    AnalyzeConfig.KA_Service_Specifications,
                                    AnalyzeConfig.KA_Communication_Skills,
                                    AnalyzeConfig.KA_Service_Timeliness,
                                    AnalyzeConfig.KA_Service_Awareness,
                                    AnalyzeConfig.KA_Professional_Skills).toArray(new Analyze[0]))
                            .build()
            );
        }};

        AGENT_STRATEGY_LIST = new ArrayList<Strategy>() {{
            add(
                    Strategy.builder()
                            .id(5)
                            .name("TDSQL工单优先级初步识别")
                            .scene(Scene.Ticket)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'ticket_operation' && " +
                                                    "service_channel == 28 && " +
                                                    "status != 3 && " +
                                                    "(operation_type == 1 || operation_type == 5 || operation_type == 8 || operation_type == 10) && " +
                                                    "(service_scene_level1_name == '云产品一部/国产数据库产品中心' && " +
                                                    "regexMatch(service_scene_level2_name, '^(腾讯云TDSQL|腾讯云TDSQL-PG版)$') || " +
                                                    "service_scene_level1_name == '云产品一部/TCE产品中心' && " +
                                                    "service_scene_level2_name == 'TCE授权' && " +
                                                    "regexMatch(service_scene_level3_name, '^(分布式数据库\\\\(TDSQL\\\\)|平台支撑-TDSQL|云服务器\\\\(CVM\\\\))$'))")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.TicketPriorityAgentReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.TDSQL_Agent_Ticket_Priority_Pre).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(6)
                            .name("TDSQL工单优先级L2二次校验")
                            .scene(Scene.Ticket)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'retry_strategy' && reflection_type_classify == 'L2'")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.TicketPriorityAgentReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.TDSQL_Agent_Ticket_Priority_L2).toArray(new Analyze[0]))
                            .build()
            );
            add(
                    Strategy.builder()
                            .id(7)
                            .name("TDSQL工单优先级L3二次校验")
                            .scene(Scene.Ticket)
                            .condition(Condition
                                    .builder()
                                    .expression(
                                            "data_type == 'retry_strategy' && reflection_type_classify == 'L3'")
                                    .build())
                            .trigger(trigger)
                            .chunk(ChunkConfig.TicketPriorityAgentReply)
                            .analyzes(Collections.singletonList(AnalyzeConfig.TDSQL_Agent_Ticket_Priority_L3).toArray(new Analyze[0]))
                            .build()
            );
        }};
    }

    /**
     * 检查策略 id 是否有重复
     * 需要保证策略 id 不重复
     */
    public static void main(String[] args) {
        List<Strategy> strategies = new ArrayList<>();
        strategies.addAll(QUALITY_STRATEGY_LIST);
        strategies.addAll(OPINION_STRATEGY_LIST);
        strategies.addAll(AGENT_STRATEGY_LIST);

        // 按照id排序
        strategies.sort(Comparator.comparingInt(o -> o.id));
        System.out.println(Arrays.toString(strategies.stream().map(x -> x.id).toArray()));
    }
}