package com.tencent.andata.smart.etl.serializer;

import com.tencent.andata.struct.regnine.REngineMessage;
import org.apache.flink.api.common.serialization.SerializationSchema;

import java.io.IOException;

public class KafkaREngineMessageSerializer implements SerializationSchema<REngineMessage> {
    @Override
    public byte[] serialize(REngineMessage rEngineMessage) {
        byte[] encode = null;
        try {
            encode = rEngineMessage.encode();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return encode;
    }
}