package com.tencent.andata.smart.utils;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.Options;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.utils.aviatorFuns.AddAttributeFunction;
import com.tencent.andata.smart.utils.aviatorFuns.GetCurrentTimeStampFunction;
import com.tencent.andata.smart.utils.aviatorFuns.GetNestedValueFunction;
import com.tencent.andata.smart.utils.aviatorFuns.PutNestedFunction;
import com.tencent.andata.smart.utils.aviatorFuns.RegexMatchFunction;
import com.tencent.andata.smart.utils.aviatorFuns.ReturnOriginValueFunction;
import com.tencent.andata.smart.utils.util.ArrayNodeConverter;
import com.tencent.andata.smart.utils.util.BooleanNodeConverter;
import com.tencent.andata.smart.utils.util.JsonNodeConverter;
import com.tencent.andata.smart.utils.util.NumberNodeConverter;
import com.tencent.andata.smart.utils.util.ObjectNodeConverter;
import com.tencent.andata.smart.utils.util.TextualNodeConverter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AviatorMatchUtils {

    private static final Logger log = LoggerFactory.getLogger(AviatorMatchUtils.class);

    // 创建一个独立的Aviator实例，避免与其他使用者冲突
    private static final AviatorEvaluatorInstance aviator = AviatorEvaluator.newInstance();


    // 使用ConcurrentHashMap缓存编译后的表达式，设置初始容量为1000
    private static final ConcurrentHashMap<String, Expression> expressionCache = new ConcurrentHashMap<>(1000);

    static {
        // 开启编译缓存
        aviator.setCachedExpressionByDefault(true);
        // 注册自定义函数
        aviator.addFunction(new PutNestedFunction());
        aviator.addFunction(new RegexMatchFunction());
        aviator.addFunction(new AddAttributeFunction());
        aviator.addFunction(new GetNestedValueFunction());
        aviator.addFunction(new ReturnOriginValueFunction());
        aviator.addFunction(new GetCurrentTimeStampFunction());
        aviator.setOption(Options.NIL_WHEN_PROPERTY_NOT_FOUND, true);
    }

    private static final List<JsonNodeConverter> converters = Arrays.asList(
            new TextualNodeConverter(),
            new NumberNodeConverter(),
            new BooleanNodeConverter(),
            new ArrayNodeConverter(),
            new ObjectNodeConverter()
    );

    /**
     * 预编译表达式并缓存
     *
     * @param expression Aviator表达式
     * @return 编译后的表达式对象
     */
    public static Expression preCompileExpression(String expression) {
        return expressionCache.computeIfAbsent(expression, key -> {
            try {
                return aviator.compile(key, true);
            } catch (Exception e) {
                log.error("表达式编译错误: {}, expression: {}", e, key);
                throw e;
            }
        });
    }

    /**
     * 判断数据是否满足条件
     *
     * @param data JSON数据
     * @param condition Aviator条件表达式
     * @return 是否满足条件
     */
    public static boolean isMatch(JsonNode data, Condition condition) {
        try {
            Map<String, Object> env = convertJsonToMap(data);
            Expression compiledExp = preCompileExpression(condition.getExpression());
            return (Boolean) compiledExp.execute(env);
        } catch (Exception e) {
            log.error("条件匹配发生错误: {}, expression: {}", e, condition.getExpression());
            return false;
        }
    }

    public static JsonNode executeExpression(JsonNode data, String expression) {
        try {
            Map<String, Object> env = convertJsonToMap(data);
            Expression compiledExp = getCompiledExpression(expression);
            return (JsonNode) compiledExp.execute(env);
        } catch (Exception e) {
            log.error("表达式执行错误: {}, expression: {}, data: {}", e, expression, data);
            return null;
        }
    }

    /**
     * 获取编译后的表达式，优先从缓存获取
     */
    private static Expression getCompiledExpression(String expression) {
        return expressionCache.computeIfAbsent(expression, aviator::compile);
    }

    /**
     * 将JsonNode转换为Map，支持更多的JSON数据类型
     */
    public static Map<String, Object> convertJsonToMap(JsonNode node) {
        Map<String, Object> map = new HashMap<>();
        Iterator<String> fieldNames = node.fieldNames();

        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            JsonNode fieldValue = node.get(fieldName);
            map.put(fieldName, convertJsonNodeToObject(fieldValue));
        }
        return map;
    }

    /**
     * 将JsonNode转换为对应的Java对象
     */
    public static Object convertJsonNodeToObject(JsonNode node) {
        if (node == null || node.isNull()) {
            return null;
        }

        for (JsonNodeConverter converter : converters) {
            if (converter.canConvert(node)) {
                return converter.convert(node);
            }
        }

        // 对于其他类型，转换为字符串
        log.debug("未知的JSON节点类型: {}, 转换为字符串处理", node.getNodeType());
        return node.toString();
    }

    /**
     * 清除表达式缓存
     */
    public static void clearExpressionCache() {
        expressionCache.clear();
        aviator.clearExpressionCache();
    }
}