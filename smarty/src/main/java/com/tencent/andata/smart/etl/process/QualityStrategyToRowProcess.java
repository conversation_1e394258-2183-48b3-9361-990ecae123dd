package com.tencent.andata.smart.etl.process;

import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;
import static io.vavr.Predicates.is;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.enums.Scene;
import io.vavr.collection.Array;
import io.vavr.collection.Iterator;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

/**
 * 将质检策略转换为Row对象的处理函数
 * 负责处理不同场景下的质检数据，提取工单ID，并将分析结果转换为标准格式
 */
public class QualityStrategyToRowProcess extends ProcessFunction<Strategy, Row> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper MAPPER = new ObjectMapper();

    // 不通过的结果标识
    private static final String[] NOT_PASSED_MARKERS = {"不通过", "未通过", "不合格"};
    // 错误消息
    private static final String WEBIM_GET_TICKET_ERROR = "webim conversation_id: %s get ticket_id error:%s";

    /**
     * 构造函数
     */
    public QualityStrategyToRowProcess() {
        // 无需额外初始化
    }

    /**
     * 兼容原有构造函数，但不再使用RainbowUtils参数
     *
     * @param rainbowUtils 原来的工具类参数，现在不再使用
     */
    public QualityStrategyToRowProcess(Object rainbowUtils) {
        // 不再需要ObjectMapper和RainbowUtils
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(Strategy strategy, Context context, Collector<Row> collector) {
        // 获取质检来源
        QaSource qaSource = QaSource.fromScene(strategy.scene);

        // 提取工单ID
        String ticketId = extractTicketId(strategy).getOrElse("");

        // 质检时间
        Timestamp qaTimestamp = Timestamp.from(Instant.ofEpochMilli(strategy.trigger.triggerTimestamp));

        // 处理所有分析结果
        if (strategy.getAnalyzes() != null) {
            Stream.of(strategy.getAnalyzes())
                    .forEach(analyze -> emitAnalyze(
                            strategy,
                            analyze,
                            ticketId,
                            qaSource.getCode(),
                            strategy.sceneIdentify,
                            qaTimestamp,
                            collector
                    ));
        }
    }

    /**
     * 根据场景从策略中提取工单ID
     *
     * @param strategy 策略对象
     * @return 包含工单ID的Option对象
     */
    private Option<String> extractTicketId(Strategy strategy) {
        return Match(strategy.scene).of(
                Case($(is(Scene.Ticket)), () -> Option.of(strategy.sceneIdentify)),
                Case($(is(Scene.WebIM)), () -> extractWebIMTicketId(strategy)),
                Case($(is(Scene.Group)), () -> Option.of("")),
                Case($(), Option::none)
        );
    }

    /**
     * 从WebIM场景中提取工单ID
     *
     * @param strategy 策略对象
     * @return 包含工单ID的Option对象
     */
    private Option<String> extractWebIMTicketId(Strategy strategy) {
        return Try.of(() ->
                        Option.of(strategy.trigger.data[0])
                                .filter(data -> data != null)
                                .map(data -> data.get("conversation_ticket_ids"))
                                .filter(node -> node != null)
                                .map(node -> node.asText())
                )
                .onFailure(e -> logger.error(String.format(WEBIM_GET_TICKET_ERROR, strategy.sceneIdentify, e)))
                .getOrElse(Option.none());
    }

    /**
     * 判断分析结果是否通过
     *
     * @param analyze 分析对象
     * @return 1表示通过，0表示不通过
     */
    private int determineIsPass(Analyze analyze) {
        if (analyze.res == null) {
            return 1;
        }

        List<String> analyzesRes = Try.of(() -> MAPPER.readTree(analyze.res))
                .onFailure(e -> logger.error(String.format("解析质检结果失败，analyze: %s，err_ms: %s", Try.of(() -> MAPPER.writeValueAsString(analyze)), e.getMessage())))
                .map(this::processQualityResults)
                .getOrElse(new ArrayList<>());

        return analyzesRes.stream()
                .anyMatch(result -> Array.of(NOT_PASSED_MARKERS)
                        .exists(marker -> result.contains(marker))) ? 0 : 1;
    }

    /**
     * 下发质检维度
     *
     * @param analyze 分析对象
     * @param ticketId 工单ID
     * @param qaSource 质检来源
     * @param sceneIdentify 场景标识
     * @param qaTimestamp 质检时间
     * @param collector 收集器
     */
    private void emitAnalyze(Strategy strategy, Analyze analyze, String ticketId, int qaSource, String sceneIdentify, Timestamp qaTimestamp, Collector<Row> collector) {

        // 生成数据
        Row row = Row.withNames(RowKind.INSERT);
        // 设置质检来源
        row.setField("qa_source", qaSource);
        // 工单ID
        row.setField("ticket_id", ticketId);
        // 质检维度
        row.setField("qa_name", analyze.name);
        // 质检时间
        row.setField("qa_timestamp", qaTimestamp);
        // 是否通过
        row.setField("is_pass", determineIsPass(analyze));
        // 唯一键
        row.setField("pk", String.format("%s-%s", sceneIdentify, analyze.name));
        // 质检结果
        row.setField("qa_result", strategy.chunk.conversation.equals("群消息未引用建单标题") ? "未质检" : analyze.res);

        collector.collect(row);
    }

    /**
     * 递归搜索JSON节点中的"结果"字段
     *
     * @param node 要搜索的JSON节点
     * @return 找到的"结果"字段节点，如果不存在则返回null
     */
    private JsonNode findResultField(JsonNode node) {
        // 基础情况：当前节点直接包含"结果"字段
        if (node.has("结果")) {
            return node.get("结果");
        }

        // 根据节点类型选择处理策略
        return Match(node).of(
                Case($(JsonNode::isObject), this::processObjectNode),
                Case($(JsonNode::isArray), this::processArrayNode),
                Case($(), () -> null)
        );
    }

    /**
     * 处理JSON对象类型节点，递归查找其中的"结果"字段
     * <p>
     * 处理过程：
     * 1. 将JSON对象的所有字段转换为流(Iterator)
     * 2. 提取每个字段的值(Map.Entry::getValue)
     * 3. 对每个字段值递归调用findResultField方法查找"结果"字段
     * 4. 返回第一个找到的非空"结果"字段节点
     * 5. 如果没有找到则返回null
     * </p>
     *
     * @param node 要处理的JSON对象节点
     * @return 找到的"结果"字段节点，如果不存在则返回null
     */
    private JsonNode processObjectNode(JsonNode node) {
        return Iterator.ofAll(node::fields)
                .map(Map.Entry::getValue)
                .map(this::findResultField)
                .find(Objects::nonNull)
                .getOrNull();
    }


    /**
     * 处理JSON数组类型节点，递归查找其中的"结果"字段
     * <p>
     * 处理过程：
     * 1. 将JSON数组的所有元素转换为流(Stream)
     * 2. 对每个数组元素递归调用findResultField方法查找"结果"字段
     * 3. 返回第一个找到的非空"结果"字段节点
     * 4. 如果数组中没有找到"结果"字段则返回null
     * </p>
     *
     * @param node 要处理的JSON数组节点
     * @return 找到的"结果"字段节点，如果不存在则返回null
     */
    private JsonNode processArrayNode(JsonNode node) {
        return Stream.ofAll(node::elements)
                .map(this::findResultField)
                .find(Objects::nonNull)
                .getOrNull();
    }

    /**
     * 处理质检结果JSON节点
     * 使用vavr的函数式API处理JSON节点：
     * 1. 将根节点转换为流：如果是数组则直接使用，否则包装成单元素数组
     * 2. 对每个节点查找"结果"字段
     * 3. 对找到的结果字段进行匹配处理：
     * - 如果结果字段存在且是文本类型，返回其文本值
     * - 如果结果字段不存在，记录警告并返回"未通过"
     * 4. 将所有处理结果添加到analyzesRes列表中
     *
     * @param rootNode 质检结果JSON根节点
     * @return 处理后的质检结果列表
     */
    private List<String> processQualityResults(JsonNode rootNode) {
        List<String> analyzesRes = new ArrayList<>();
        Stream.ofAll(rootNode.isArray() ? rootNode : Array.of(rootNode))
                .map(this::findResultField)
                .map(resultNode -> Match(resultNode).of(
                        Case($(Objects::nonNull), node -> node.isTextual() ? node.asText() : null),
                        Case($(), () -> {logger.warn("结果字段不存在: " + (rootNode.isArray() ? "数组节点" : "单节点")); return "未通过";})
                ))
                .forEach(analyzesRes::add);
        return analyzesRes;
    }

    /**
     * 质检来源枚举
     */
    private enum QaSource {
        UNKNOWN(-1),
        TICKET(1),
        WEBIM(2),
        GROUP(3);

        private final int code;

        QaSource(int code) {
            this.code = code;
        }

        /**
         * 根据场景获取质检来源
         *
         * @param scene 场景
         * @return 质检来源
         */
        public static QaSource fromScene(Scene scene) {
            return Match(scene).of(
                    Case($(is(Scene.Ticket)), QaSource.TICKET),
                    Case($(is(Scene.WebIM)), QaSource.WEBIM),
                    Case($(is(Scene.Group)), QaSource.GROUP),
                    Case($(), QaSource.UNKNOWN)
            );
        }

        public int getCode() {
            return code;
        }
    }
}