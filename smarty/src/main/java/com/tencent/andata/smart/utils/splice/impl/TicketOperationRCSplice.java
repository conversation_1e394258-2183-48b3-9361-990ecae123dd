package com.tencent.andata.smart.utils.splice.impl;

import static com.tencent.andata.smart.enums.TicketServiceChannel.fromId;

import com.tencent.andata.smart.enums.TicketServiceChannel;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.utils.splice.base.AbstractItemSplice;
import com.tencent.andata.smart.enums.TicketOperationType;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 工单流水风控拼接实现
 */
public class TicketOperationRCSplice extends AbstractItemSplice {

    private static final String CUSTOMER_REPLY_TEMPLATE = "客户回复：%s\n";
    private static final String STAFF_REPLY_TEMPLATE = "坐席回复：%s\n";
    private static final Map<TicketOperationType, String> OPERATION_TEMPLATES = HashMap.of(
            // 客户回复
            TicketOperationType.CUSTOMER_REPLY, CUSTOMER_REPLY_TEMPLATE,
            TicketOperationType.CREATE, CUSTOMER_REPLY_TEMPLATE,
            TicketOperationType.URGE, CUSTOMER_REPLY_TEMPLATE,
            // 坐席回复
            TicketOperationType.WAIT_CUSTOMER_ADD_INFO, STAFF_REPLY_TEMPLATE,
            TicketOperationType.WAIT_CUSTOMER_CLOSE, STAFF_REPLY_TEMPLATE,
            TicketOperationType.REPLY, STAFF_REPLY_TEMPLATE

    );

    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        // 提前验证输入参数
        if (item.isEmpty() || strategy.isEmpty()) {
            return "";
        }

        // 获取当前节点，避免重复调用
        JsonNode currentNode = item.get().getCurrent();

        // 执行业务过滤，排除CallCenter通道的数据（cc通道如果要上风控，应该单独实现自己的上下文逻辑）
        // 以及触发时间之后的数据，只保留触发时间之前的数据
        if (isCallCenterChannel(strategy) || !isBeforeTriggeredTimestamp(item, strategy)) {
            return "";
        }

        // 提取和处理内容
        return extractReplyContent(currentNode)
                .filter(content -> !isNullContent(content))
                .flatMap(content -> formatReplyWithOperationType(currentNode, content))
                .getOrElse("");
    }

    /**
     * 验证通道是否是CallCenter通道
     */
    protected boolean isCallCenterChannel(Option<Strategy> strategy) {
        return strategy
                .map(s -> s.trigger)
                .filter(t -> t.data != null && t.data.length > 0)
                .map(t -> t.data[0])
                .map(d -> d.get("service_channel"))
                .map(JsonNode::asInt)
                .map(channel -> fromId(channel) == TicketServiceChannel.CALLCENTER)
                .getOrElse(false); // 默认允许处理
    }

    /**
     * 根据操作类型格式化回复内容
     */
    private Option<String> formatReplyWithOperationType(JsonNode currentNode, String content) {
        return getOperationType(currentNode)
                .map(TicketOperationType::fromCode)
                .flatMap(type -> formatReply(type, content));
    }

    protected Option<String> extractReplyContent(JsonNode node) {
        return Option.of(node)
                .map(n -> n.get("extern_reply"))
                .map(JsonNode::asText)
                .map(this::cleanContent);
    }

    private Option<String> formatReply(TicketOperationType type, String content) {
        return OPERATION_TEMPLATES
                .get(type)
                .map(template -> String.format(template, content));
    }
}