package com.tencent.andata.smart.etl.handler;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.enums.TicketServiceChannel;
import com.tencent.andata.smart.etl.domain.CustomerStaffInfo;
import com.tencent.andata.smart.etl.domain.TicketInfo;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.strategy.model.Strategy;
import io.vavr.Tuple4;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.text.SimpleDateFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

@Slf4j
public class C2000SceneHandler extends AbstractSceneHandler {

    private static final FlinkLog logger = FlinkLog.getInstance();

    public C2000SceneHandler(
            DutyRepository dutyRepository,
            TicketRepository ticketRepository,
            CustomerRepository customerRepository) {
        super(dutyRepository, ticketRepository, customerRepository);
    }

    @Override
    public Option<Long> getTicketId(Strategy strategy) {
        return getFirstData(strategy)
                .flatMap(data -> Option.of(data.get("ticket_id")))
                .flatMap(node -> Try.of(node::asLong).toOption())
                .onEmpty(() -> logger.error("[C2000SceneHandler] Failed to get ticketId, strategy: " + strategy));
    }


    public Option<String> getOperationId(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("msg_id").asText());
    }

    @Override
    public Option<Long> getEventTime(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("msg_time").asText())
                .flatMap(timeStr -> Try.of(() ->
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeStr).getTime()
                ).toOption());
    }

    @Override
    public Option<String> getCurrentStaffIdr1(Strategy strategy) {
        return getTicketId(strategy)
                .flatMap(ticketRepository::findById)
                .flatMap(ticket -> Option.of(ticket.getCurrentOperator()))
                .flatMap(customerRepository::findByUid)
                .map(CustomerStaffInfo::getQIdr1);
    }

    @Override
    public Option<String> getCurrentPost(Strategy strategy) {
        return getTriggerDataByField(strategy, "post")
                .map(JsonNode::asText)
                .toOption();
    }

    @Override
    public Option<String> getServiceChannel(Strategy strategy) {
        return getTriggerDataByField(strategy, "service_channel")
                .map(JsonNode::asInt)
                .map(TicketServiceChannel::fromId)
                .map(TicketServiceChannel::getDescription);
    }

    @Override
    public Option<String> getCurrentCustomerName(Strategy strategy) {
        return getTriggerDataByField(strategy, "customerName")
                .map(JsonNode::asText);
    }

    @Override
    public Option<Tuple4<String, String, String, String>> getServiceScenesName(Strategy strategy) {
        Option<TicketInfo> ticketInfo = getTicketId(strategy).map(ticketRepository::findById).get();
        return Option.of(new Tuple4<>(
                ticketInfo.get().getServiceSceneLevel1Name(),
                ticketInfo.get().getServiceSceneLevel2Name(),
                ticketInfo.get().getServiceSceneLevel3Name(),
                ticketInfo.get().getServiceSceneLevel4Name()
        ));
    }

    public Option<String> getUrl(Strategy strategy) {
        return Option.none();
    }
}