package com.tencent.andata.smart.etl;

import com.tencent.andata.utils.*;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

@Builder
public class Pg2StarRocksETL {
    private RainbowUtils rainbowUtils;
    private static String pgDbName = "smarty";

    /**
     * run the risk pg2starrocks data
     *
     * @param flinkEnv flink运行环境工具类
     *
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();
        DatabaseConf PgDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "smarty"))
                .build();
        String starRocksTable2FlinkTable = rainbowUtils.getStringValue(
                "smarty.pg2sr", "starRocksTable2FlinkTable");
        String pgTable2FlinkTable = rainbowUtils.getStringValue(
                "smarty.pg2sr", "pgTable2FlinkTable");
        String pgTable2StarrocksTable = rainbowUtils.getStringValue(
                "smarty.pg2sr", "pgTable2StarrocksTable");

        ObjectMapper mapper = new ObjectMapper();

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);
        ArrayNode pgTable2StarrocksTableMap = mapper.readValue(pgTable2StarrocksTable, ArrayNode.class);
        ArrayNode pgTable2FlinkTableMap = mapper.readValue(pgTable2FlinkTable,
                ArrayNode.class);

        // pg mapping to flinkTable
        TableUtils.pgdbTable2FlinkTable(
                PgDbConf,
                pgTable2FlinkTableMap,
                PGSQL, tEnv, "source"
        );

        StatementSet stmtSet = flinkEnv.stmtSet();

        for (JsonNode node : pgTable2StarrocksTableMap) {
            String pgTable = node.get("pgTable").asText();
            String sTable = node.get("sTable").asText();
            String fView = node.get("fView").asText();
            String columns = node.get("columns").asText();

            tEnv.createTemporaryView(
                    fView,
                    tEnv.sqlQuery(String.format("SELECT %s FROM %s", columns, pgTable)));

            stmtSet.addInsertSql(insertIntoSql(fView, sTable, tEnv.from(sTable), ROCKS));
        }
    }
}