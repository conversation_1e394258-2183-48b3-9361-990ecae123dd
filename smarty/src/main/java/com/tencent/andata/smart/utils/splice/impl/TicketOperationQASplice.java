package com.tencent.andata.smart.utils.splice.impl;

import static com.tencent.andata.smart.enums.ActionType.TRANSFER;
import static com.tencent.andata.smart.enums.ActionType.TRANSFER_RESPONSIBLE;
import static com.tencent.andata.smart.enums.OperatorType.CUSTOMER;
import static com.tencent.andata.smart.enums.OperatorType.STAFF;
import static com.tencent.andata.smart.enums.OperatorType.fromCode;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.enums.ActionType;
import com.tencent.andata.smart.enums.OperatorType;
import com.tencent.andata.smart.enums.PostType;
import com.tencent.andata.smart.enums.TicketOperationType;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.utils.DutyInfoUtils;
import com.tencent.andata.smart.utils.StaffInfoUtils;
import com.tencent.andata.smart.utils.util.ContentClean;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.Builder;
import lombok.Value;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

import java.util.function.Function;

/**
 * 工单流水质检拼接实现
 * 支持组合工单流水和群消息流水的组合质检
 */
public class TicketOperationQASplice extends TicketOperationRCSplice {

    private static final FlinkLog logger = FlinkLog.getInstance();

    // jsonPath conf - 配置为静态常量避免重复创建
    private static final Configuration JSON_PATH_CONF = Configuration
            .defaultConfiguration()
            .addOptions(com.jayway.jsonpath.Option.DEFAULT_PATH_LEAF_TO_NULL);

    // 需要排除的操作类型，这些操作类型不需要提取对话内容
    private static final List<ActionType> EXCLUDED_ACTION_TYPES = List.of(
            ActionType.URGE,
            ActionType.CLOSE,
            ActionType.COMPLAINT
    );

    // 使用单例模式避免重复创建
    private static final GroupMsgQASplice GROUP_MSG_SPLICE = new GroupMsgQASplice();

    /**
     * 根据场景类型处理不同的流水项
     *
     * @param item 流水项
     * @param strategy 策略
     * @return 拼接后的字符串
     */
    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        // 如果`data_type`为group，则处理群消息流水
        JsonNode currItem = item.get().getCurrent();
        if (currItem.has("data_type") && currItem.get("data_type").asText().equals("group")) {
            return processGroupItem(item, strategy);
        }

        return processTicketItem(item, strategy);
    }

    /**
     * 从流水中获取场景信息
     *
     * @param strategy 策略
     * @return 场景类型
     */
    private Scene getScene(Option<Strategy> strategy) {
        return strategy.map(Strategy::getScene).getOrNull();
    }

    /**
     * 处理工单流水
     *
     * @param item 流水项
     * @param strategy 策略
     * @return 拼接后的字符串
     */
    private String processTicketItem(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item
                .flatMap(this::filterValidOperation)
                .map(this::createMessageComponents)
                .map(this::formatMessage)
                .getOrNull();
    }

    /**
     * 处理群流水
     *
     * @param item 流水项
     * @param strategy 策略
     * @return 拼接后的字符串
     */
    private String processGroupItem(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return GROUP_MSG_SPLICE.doSplice(item, strategy);
    }

    /**
     * 过滤有效的操作流水
     *
     * @param item 流水项
     * @return 过滤后的流水项
     */
    private Option<ElementContext<JsonNode>> filterValidOperation(ElementContext<JsonNode> item) {
        return Option.of(item)
                .filter(n -> isCustomerOperation(n.getCurrent()) || isNotStaffCloseOperation(n.getCurrent()));
    }

    /**
     * 判断是否是客户操作
     *
     * @param node JSON节点
     * @return 是否是客户操作
     */
    private boolean isCustomerOperation(JsonNode node) {
        return fromCode(node.get("operator_type").asText()) == CUSTOMER
                && !"SYSTEM".equals(node.get("operator").asText());
    }

    /**
     * 判断是否是非客服结单操作
     *
     * @param node JSON节点
     * @return 是否是非客服结单操作
     */
    private boolean isNotStaffCloseOperation(JsonNode node) {
        return fromCode(node.get("operator_type").asText()) == STAFF
                && !"SYSTEM".equals(node.get("operator").asText())
                && getOperationType(node)
                .map(TicketOperationType::fromCode)
                .exists(type -> type != TicketOperationType.CLOSE);
    }

    /**
     * 创建消息组件
     *
     * @param item 流水项上下文
     * @return 当前和下一个消息组件的元组
     */
    private Tuple2<MessageComponent, MessageComponent> createMessageComponents(ElementContext<JsonNode> item) {
        Function<JsonNode, MessageComponent> componentBuilder = this::buildMessageComponent;
        return Tuple.of(componentBuilder.apply(item.getCurrent()), componentBuilder.apply(item.getNext()));
    }

    /**
     * 构建消息组件
     *
     * @param node JSON节点
     * @return 消息组件
     */
    private MessageComponent buildMessageComponent(JsonNode node) {
        return Try.of(() -> {
            OperatorType role = resolveRole(node);
            String staffInfo = resolveStaffInfo(node, role);
            ActionType actionType = ActionType.fromCode(getOperationType(node).get());

            return MessageComponent.builder()
                    .role(role)
                    .staffInfo(staffInfo)
                    .actionType(actionType)
                    .time(formatTime(node, Scene.Ticket))
                    .replyContent(resolveContent(node, actionType))
                    .operationId(node.get("operation_id").asLong())
                    .nextStaffInfo(buildNextStaffInfo(node, "target_post", "next_operator", "next_assign"))
                    .nextResponsibleInfo(buildNextStaffInfo(node, "target_post", "next_responsible", "next_assign"))
                    .build();
        }).getOrNull();
    }

    /**
     * 构建下一个处理人信息
     *
     * @param node JSON节点
     * @param postKey 岗位键
     * @param operatorKey 操作员键
     * @param dutyKey 职责键
     * @return 格式化的处理人信息
     */
    private String buildNextStaffInfo(JsonNode node, String postKey, String operatorKey, String dutyKey) {
        return supplementStaffInfo(getPostInfo(node, postKey, operatorKey), node, dutyKey);
    }

    /**
     * 格式化消息
     *
     * @param comps 消息组件元组
     * @return 格式化后的消息
     */
    String formatMessage(Tuple2<MessageComponent, MessageComponent> comps) {
        String messageContent = buildMessageContent(comps._1);
        String actionDescription = buildActionDescription(comps);

        return List.of(messageContent, actionDescription)
                .filter(content -> !isNullContent(content))
                .mkString();
    }

    /**
     * 构建回复消息内容
     *
     * @param comp 消息组件
     * @return 格式化的回复内容
     */
    private String buildMessageContent(MessageComponent comp) {
        return Option.of(comp.getReplyContent())
                .filter(content -> !isNullContent(content))
                .map(c -> formatReplyByRole(comp, c))
                .getOrNull();
    }

    /**
     * 根据角色格式化回复内容
     *
     * @param comp 消息组件
     * @param content 回复内容
     * @return 格式化的回复内容
     */
    private String formatReplyByRole(MessageComponent comp, String content) {
        switch (comp.getRole()) {
            case CUSTOMER:
                return String.format("\n%s %s回复：%s", comp.getTime(), comp.getRole().getDesc(), content);
            case STAFF:
                return String.format("\n%s %s %s回复：%s", comp.getTime(), comp.getRole().getDesc(), comp.getStaffInfo(), content);
            default:
                return "";
        }
    }

    /**
     * 构建特定动作描述
     *
     * @param comps 消息组件元组
     * @return 格式化的动作描述
     */
    private String buildActionDescription(Tuple2<MessageComponent, MessageComponent> comps) {
        MessageComponent current = comps._1;
        ActionType actionType = current.getActionType();

        // 转单/交接动作特殊处理
        if (actionType == TRANSFER || actionType == TRANSFER_RESPONSIBLE) {
            return handleAssign(comps);
        }

        // 其他动作
        return Option.of(actionType)
                .map(ActionType::getDesc)
                .filter(actionDesc -> !isNullContent(actionDesc))
                .map(actionDesc -> formatActionByRole(current, actionDesc))
                .getOrNull();
    }

    /**
     * 根据角色格式化动作描述
     *
     * @param comp 消息组件
     * @param actionDesc 动作描述
     * @return 格式化的动作描述
     */
    private String formatActionByRole(MessageComponent comp, String actionDesc) {
        switch (comp.getRole()) {
            case CUSTOMER:
                return String.format("\n%s %s触发动作：%s", comp.getTime(), comp.getRole().getDesc(), actionDesc);
            case STAFF:
                return String.format("\n%s %s %s触发动作：%s", comp.getTime(), comp.getRole().getDesc(), comp.getStaffInfo(), actionDesc);
            default:
                return null;
        }
    }

    /**
     * 处理转单/交接动作
     *
     * @param comps 消息组件元组
     * @return 格式化的转单/交接信息
     */
    private String handleAssign(Tuple2<MessageComponent, MessageComponent> comps) {
        MessageComponent curr = comps._1;
        MessageComponent next = comps._2;

        switch (curr.getRole()) {
            case STAFF:
                return formatTransferAction(curr);
            case CUSTOMER:
                logger.error(String.format("当前角色为客户，当前操作类型为：%s，当前操作id为：%s", curr.getActionType().getDesc(), curr.getOperationId()));
                return String.format("\n%s %s 触发动作：%s，将会话转移给 坐席 %s", curr.getTime(), curr.getRole().getDesc(), curr.getActionType().getDesc(), next.getStaffInfo());
            default:
                return null;
        }
    }

    /**
     * 格式化转单/交接动作
     *
     * @param curr 当前消息组件
     * @return 格式化的转单/交接信息
     */
    private String formatTransferAction(MessageComponent curr) {
        switch (curr.getActionType()) {
            case TRANSFER:
                return String.format("\n%s 坐席 %s 触发动作：%s，将会话转移给 坐席 %s", curr.getTime(), curr.getStaffInfo(), curr.getActionType().getDesc(), curr.getNextStaffInfo());
            case TRANSFER_RESPONSIBLE:
                return String.format("\n%s 坐席 %s 触发动作：%s，将会话转移给 坐席 %s", curr.getTime(), curr.getStaffInfo(), curr.getActionType().getDesc(), curr.getNextResponsibleInfo());
            default:
                return null;
        }
    }

    /**
     * 解析回复内容
     *
     * @param node JSON节点
     * @param actionType 动作类型
     * @return 解析后的回复内容
     */
    private String resolveContent(JsonNode node, ActionType actionType) {
        return Option.of(node)
                .filter(n -> !EXCLUDED_ACTION_TYPES.contains(actionType))
                .map(n -> n.get("extern_reply"))
                .map(JsonNode::asText)
                .map(ContentClean::UserContentClean)
                .getOrElse("");
    }

    /**
     * 解析角色信息
     *
     * @param node JSON节点
     * @return 操作员类型
     */
    private OperatorType resolveRole(JsonNode node) {
        return Option.of(fromCode(node.get("operator_type").asText()))
                .filter(r -> r == CUSTOMER || r == STAFF)
                .getOrElse(STAFF);
    }

    /**
     * 获取客服相关信息
     *
     * @param node JSON节点
     * @param role 角色类型
     * @return 客服信息
     */
    private String resolveStaffInfo(JsonNode node, OperatorType role) {
        if (role == CUSTOMER) {
            return "";
        }
        return formatStaffRole(node);
    }

    /**
     * 格式化坐席角色信息
     *
     * @param item JSON节点
     * @return 格式化的坐席角色信息
     */
    private String formatStaffRole(JsonNode item) {
        final String baseInfo = PostType
                .getDesc(getRealOperatorPost(item.get("customer_fields").asText(), item.get("post").asText()))
                .map(post -> String.format("%s %s", post, StaffInfoUtils.getUserName(item.get("operator").asText())))
                .getOrNull();

        return supplementStaffInfo(baseInfo, item, "fact_assign");
    }

    /**
     * 补充坐席信息
     *
     * @param staffInfo 基础坐席信息
     * @param item JSON节点
     * @param dutyKey 职责键
     * @return 补充后的坐席信息
     */
    private String supplementStaffInfo(String staffInfo, JsonNode item, String dutyKey) {
        return Option.of(DutyInfoUtils.getDutyName(item.get(dutyKey).asInt()))
                .filter(StringUtils::isNotEmpty)
                .map(duty -> String.format("%s （%s）", staffInfo, duty))
                .getOrElse(staffInfo);
    }

    /**
     * 获取岗位相关信息
     *
     * @param item JSON节点
     * @param postKey 岗位键
     * @param operatorKey 操作员键
     * @return 岗位信息
     */
    private String getPostInfo(JsonNode item, String postKey, String operatorKey) {
        return PostType
                .getDesc(item.get(postKey).asText())
                .map(postDesc -> String.format("%s %s", postDesc, StaffInfoUtils.getUserName(item.get(operatorKey).asText())))
                .getOrNull();
    }

    /**
     * 获取真实客服岗位
     *
     * @param customerFields 客户字段
     * @param defaultValue 默认值
     * @return 真实客服岗位
     */
    private String getRealOperatorPost(String customerFields, String defaultValue) {
        return Try.of(() -> JsonPath.using(JSON_PATH_CONF).parse(customerFields).read("$.operator_post", String.class))
                .filter(StringUtils::isNotEmpty)
                .recover(e -> {logger.error("[TicketOperationQASplice] Failed to get operatorPost from customer_fields: " + e); return defaultValue;})
                .getOrElse(defaultValue);
    }

    /**
     * 消息组件值对象
     * 使用不可变对象减少状态变化带来的问题
     */
    @Value
    @Builder
    private static class MessageComponent {

        String time;
        Long operationId;
        String staffInfo;
        OperatorType role;
        String replyContent;
        String nextStaffInfo;
        ActionType actionType;
        String nextResponsibleInfo;
    }

    public static void main(String[] args) {
        System.out.println();
    }
}