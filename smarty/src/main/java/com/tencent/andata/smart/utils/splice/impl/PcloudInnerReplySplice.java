package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.smart.enums.OperationType;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.collection.List;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class PcloudInnerReplySplice extends TicketOperationRCSplice {

    private static final List<OperationType> needOperation = List.of(
            OperationType.REPLY, OperationType.CREATE, OperationType.TRANSFER, OperationType.TRANSFER_RESPONSIBLE);


    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item.map(ElementContext::getCurrent)
                .filter(this::isValidOperation)
                .flatMap(this::extractReplyContent)
                .getOrNull();
    }

    @Override
    protected Option<String> extractReplyContent(JsonNode node) {
        return Option.of(node)
                .map(n -> n.get("inner_reply"))
                .map(JsonNode::asText)
                .map(c -> cleanContent(c) + "\n")
                .filter(content -> !isNullContent(content));
    }

    private boolean isValidOperation(JsonNode node) {
        return getOperationType(node)
                .map(Integer::parseInt)
                .map(OperationType::of)
                .map(needOperation::contains)
                .getOrElse(false);

    }
}