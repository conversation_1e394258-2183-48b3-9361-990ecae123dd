package com.tencent.andata.smart;

import com.tencent.andata.smart.etl.QualityInspectionProcessETL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.java.utils.ParameterTool;

public class QualityInspectionApplication {

    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        final QualityInspectionProcessETL etl = QualityInspectionProcessETL.builder()
                .rainbowUtils(rainbowUtils)
                .build();
        etl.run(flinkEnv);
        flinkEnv.env().execute("Quality result process");
    }
}