package com.tencent.andata.smart.access.operators;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.access.model.TicketInfo;
import com.tencent.andata.smart.access.repository.JdbcTicketRepository;
import com.tencent.andata.smart.access.repository.TicketRepository;
import com.tencent.andata.smart.access.service.TicketEnricher;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * 工单服务通道处理器
 * 负责处理工单数据，丰富JSON信息
 */
public class ServiceChannelProcess extends ProcessFunction<JsonNode, JsonNode> {

    private static final FlinkLog logger = FlinkLog.getInstance();

    // 性能监控计数器
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private long lastLogTime = System.currentTimeMillis();
    private static final long LOG_INTERVAL_MS = 60000; // 1分钟

    private final DatabaseConf workDBConf;
    private TicketRepository ticketRepository;
    private TicketEnricher ticketEnricher;

    /**
     * 构造函数
     *
     * @param workDBConf 数据库配置
     */
    public ServiceChannelProcess(DatabaseConf workDBConf) {
        this.workDBConf = workDBConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化仓库和丰富器
        ticketRepository = new JdbcTicketRepository(workDBConf);
        ticketEnricher = new TicketEnricher();
        ticketRepository.open();

        logger.info("[ServiceChannelProcess] Initialized successfully");
    }

    @Override
    public void processElement(JsonNode jsonNode, Context context, Collector<JsonNode> collector) throws Exception {
        try {
            if (!validateInput(jsonNode)) {
                // 无效输入，使用默认值
                collector.collect(ticketEnricher.createDefaultNode(jsonNode));
                updateMetrics(false);
                return;
            }

            // 提取工单ID
            long ticketId = extractTicketId(jsonNode);

            // 查询工单信息
            Optional<TicketInfo> ticketInfoOpt = ticketRepository.findById(ticketId);

            // 处理结果
            JsonNode resultNode;
            if (ticketInfoOpt.isPresent()) {
                // 工单信息存在，进行丰富
                resultNode = ticketEnricher.enrich(jsonNode, ticketInfoOpt.get());
                updateMetrics(true);
            } else {
                // 工单信息不存在，使用默认值
                resultNode = ticketEnricher.createDefaultNode(jsonNode);
                updateMetrics(false);
            }

            collector.collect(resultNode);

        } catch (Exception e) {
            // 发生异常，使用默认值
            logger.error("[ServiceChannelProcess] Error processing: " + e.getMessage());
            collector.collect(ticketEnricher.createDefaultNode(jsonNode));
            updateMetrics(false);
        }

        // 记录性能指标
        logMetrics();
    }

    /**
     * 验证输入是否有效
     *
     * @param jsonNode 输入的JSON节点
     * @return true表示有效，false表示无效
     */
    private boolean validateInput(JsonNode jsonNode) {
        return jsonNode != null &&
                jsonNode.has("ticket_id") &&
                !jsonNode.get("ticket_id").isNull() &&
                (jsonNode.get("ticket_id").isNumber() || jsonNode.get("ticket_id").isTextual());
    }

    /**
     * 从JSON节点中提取工单ID
     *
     * @param jsonNode JSON节点
     * @return 工单ID
     */
    private long extractTicketId(JsonNode jsonNode) {
        JsonNode idNode = jsonNode.get("ticket_id");
        if (idNode.isNumber()) {
            return idNode.asLong();
        } else {
            return Long.parseLong(idNode.asText());
        }
    }

    /**
     * 更新性能计数器
     *
     * @param success 是否成功处理
     */
    private void updateMetrics(boolean success) {
        if (success) {
            processedCount.incrementAndGet();
        } else {
            errorCount.incrementAndGet();
        }
    }

    /**
     * 定期记录性能指标
     */
    private void logMetrics() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            lastLogTime = currentTime;
            logger.info("[ServiceChannelProcess] Processing metrics - Processed: " +
                    processedCount.get() + ", Errors: " + errorCount.get());
        }
    }

    @Override
    public void close() throws Exception {
        if (ticketRepository != null) {
            try {
                ticketRepository.close();
            } catch (Exception e) {
                logger.error("[ServiceChannelProcess] Error closing ticketRepository: " + e.getMessage());
            }
        }
    }
}