package com.tencent.andata.smart.similar.sink;

import com.tencent.andata.smart.similar.model.req.DataItem;
import com.tencent.andata.smart.similar.model.req.ItemFields;
import com.tencent.andata.smart.similar.model.req.SimilarSearchRequest;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 相似问题数据写入函数
 * 支持收集 source_id 并更新 current_batch_similar_questions 和 similar_questions_history 字段
 */
public class EnhancedSinkFunction extends RichSinkFunction<Tuple2<JsonNode, SimilarSearchRequest>> {

    private static final Logger log = LoggerFactory.getLogger(EnhancedSinkFunction.class);


    private final String jdbcUrl;
    private final String username;
    private final String password;
    private final int batchSize;

    private Connection connection;
    private PreparedStatement insertOriginalTicketStatement;
    private PreparedStatement updateJsonbFieldsStatement;
    private PreparedStatement insertSimilarQuestionStatement;
    private int currentBatchSize = 0;

    // 临时存储每个工单的 source_id 集合
    private final Map<String, List<String>> ticketSourceIds = new HashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 定时器相关
    private ScheduledExecutorService scheduler;
    private volatile long lastBatchTime = System.currentTimeMillis();
    private static final long BATCH_TIMEOUT_MS = 10000; // 10秒超时

    public EnhancedSinkFunction(String jdbcUrl, String username, String password, int batchSize) {
        this.jdbcUrl = jdbcUrl;
        this.username = username;
        this.password = password;
        this.batchSize = batchSize;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        Class.forName("org.postgresql.Driver");
        this.connection = DriverManager.getConnection(jdbcUrl, username, password);
        connection.setAutoCommit(false);

        // 原始工单插入SQL（移除 id 字段）
        String insertOriginalTicketSQL = "INSERT INTO dwd_original_tickets " +
                "(ticket_id, question_description, solution, current_process, preparatory_question, " +
                "question_summary, reason, reason_classification, solution_classification, " +
                "solution_process_detail, request_id, llm_res) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?::jsonb) " +
                "ON CONFLICT (ticket_id) DO UPDATE SET " +
                "question_description = EXCLUDED.question_description, " +
                "solution = EXCLUDED.solution, " +
                "current_process = EXCLUDED.current_process, " +
                "preparatory_question = EXCLUDED.preparatory_question, " +
                "question_summary = EXCLUDED.question_summary, " +
                "reason = EXCLUDED.reason, " +
                "reason_classification = EXCLUDED.reason_classification, " +
                "solution_classification = EXCLUDED.solution_classification, " +
                "solution_process_detail = EXCLUDED.solution_process_detail, " +
                "request_id = EXCLUDED.request_id, " +
                "llm_res = EXCLUDED.llm_res, " +
                "updated_at = CURRENT_TIMESTAMP";

        // 新增：更新 JSONB 字段的SQL
        String updateJsonbFieldsSQL = "UPDATE dwd_original_tickets SET " +
                "current_batch_similar_questions = ?::jsonb, " +
                "similar_questions_history = CASE " +
                "  WHEN similar_questions_history IS NULL THEN ?::jsonb " +
                "  ELSE jsonb_set(" +
                "    COALESCE(similar_questions_history, '{\"batches\":[]}'), " +
                "    '{batches}', " +
                "    COALESCE(similar_questions_history->'batches', '[]') || ?::jsonb" +
                "  ) " +
                "END, " +
                "updated_at = CURRENT_TIMESTAMP " +
                "WHERE ticket_id = ?";

        // 相似问题插入SQL（移除 id 字段）
        String insertSimilarQuestionSQL = "INSERT INTO dwd_similar_questions " +
                "(original_ticket_id, parent_ticket_id, parent_ticket_title, similar_question_id, " +
                "similar_question_title, original_solution, knowledge, source_id, source_name, source_type, " +
                "service_scene_level1_id, service_scene_level2_id, service_scene_level3_id, " +
                "service_scene_level4_id, service_scene_checked_id, cloud_type, index_type, visible, " +
                "customer_uin, ticket_create_time, ticket_close_time, request_id, deleted) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                "ON CONFLICT (similar_question_id) DO UPDATE SET " +
                "similar_question_title = EXCLUDED.similar_question_title, " +
                "original_solution = EXCLUDED.original_solution, " +
                "knowledge = EXCLUDED.knowledge, " +
                "updated_at = CURRENT_TIMESTAMP";

        this.insertOriginalTicketStatement = connection.prepareStatement(insertOriginalTicketSQL);
        this.updateJsonbFieldsStatement = connection.prepareStatement(updateJsonbFieldsSQL);
        this.insertSimilarQuestionStatement = connection.prepareStatement(insertSimilarQuestionSQL);

        // 启动定时器，每5秒检查一次是否需要强制执行批量写入
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        this.scheduler.scheduleAtFixedRate(() -> {
            try {
                long currentTime = System.currentTimeMillis();
                if (currentBatchSize > 0 && (currentTime - lastBatchTime) > BATCH_TIMEOUT_MS) {
                    synchronized (this) {
                        if (currentBatchSize > 0) {
                            log.info("Timeout triggered batch execution with {} records", currentBatchSize);
                            executeBatch();
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error in scheduled batch execution", e);
            }
        }, 5, 5, TimeUnit.SECONDS);
    }

    @Override
    public void invoke(Tuple2<JsonNode, SimilarSearchRequest> input, Context context) throws Exception {
        JsonNode originalData = input.f0;
        SimilarSearchRequest searchRequest = input.f1;

        synchronized (this) {
            try {
                String ticketId = getTextValue(originalData, "ticket_id");
                if (ticketId == null || ticketId.isEmpty()) {
                    log.warn("ticket_id is null or empty, skipping record");
                    return;
                }

                // 1. 从 similar_relation 字段获取 source_id 列表
                Set<String> sourceIds = extractSourceIdsFromSimilarRelation(originalData, ticketId);

                // 2. 插入原始工单数据（保持原有逻辑）
                insertOriginalTicket(originalData);

                // 3. 更新 JSONB 字段
                if (!sourceIds.isEmpty()) {
                    updateJsonbFields(ticketId, sourceIds);
                }

                // 4. 插入相似问题数据（保持原有逻辑）
                insertSimilarQuestions(originalData, searchRequest);

                currentBatchSize++;
                lastBatchTime = System.currentTimeMillis(); // 更新时间戳

                // 5. 批量提交
                if (currentBatchSize >= batchSize) {
                    executeBatch();
                }

            } catch (SQLException e) {
                log.error("Failed to insert data into PostgreSQL", e);
                throw new RuntimeException("Database insert failed", e);
            }
        }
    }

    /**
     * 从 similar_relation 字段提取 source_id 列表
     */
    private Set<String> extractSourceIdsFromSimilarRelation(JsonNode jsonNode, String ticketId) {
        Set<String> sourceIds = new HashSet<>();

        try {
            String similarRelationStr = getTextValue(jsonNode, "similar_relation");
            if (similarRelationStr != null && !similarRelationStr.isEmpty()) {
                log.debug("准备解析 similar_relation JSON: {}", similarRelationStr);

                JsonNode similarRelationNode;
                try {
                    similarRelationNode = objectMapper.readTree(similarRelationStr);
                } catch (Exception parseException) {
                    log.error("解析 similar_relation JSON 失败，ticketId: {}, similarRelationStr: [{}], 错误: {}",
                            ticketId, similarRelationStr, parseException.getMessage());
                    throw parseException;
                }

                if (similarRelationNode.isArray()) {
                    for (JsonNode item : similarRelationNode) {
                        if (item.isObject()) {
                            // 遍历每个对象的字段，获取 source_id
                            item.fieldNames().forEachRemaining(sourceId -> {
                                if (!sourceId.equals(ticketId)) { // 排除原始工单自身
                                    sourceIds.add(sourceId);
                                }
                            });
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to parse similar_relation for ticket_id: {}, error: {}", ticketId, e.getMessage());
        }

        return sourceIds;
    }

    /**
     * 更新 JSONB 字段
     */
    private void updateJsonbFields(String ticketId, Set<String> sourceIds) throws SQLException {
        if (sourceIds.isEmpty()) {
            return;
        }

        try {
            // 生成批次ID
            String batchId = generateBatchId();
            List<String> sourceIdsList = new ArrayList<>(sourceIds);

            // 构建当前批次JSON - 只包含当前批次的source_ids
            String currentBatchJson = objectMapper.writeValueAsString(sourceIdsList);

            // 构建历史记录JSON - 包含完整的批次信息用于累加到history
            String historyBatchJson = "{\"batch_id\":\"" + batchId + "\",\"timestamp\":\"" +
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) +
                    "\",\"source_ids\":" + objectMapper.writeValueAsString(sourceIdsList) +
                    ",\"count\":" + sourceIds.size() + "}";

            // 使用预定义的PreparedStatement添加到批处理中
            updateJsonbFieldsStatement.setString(1, currentBatchJson); // current_batch_similar_questions = 当前批次的source_ids数组
            updateJsonbFieldsStatement.setString(2, "{\"batches\":[" + historyBatchJson + "]}"); // 初始化history结构
            updateJsonbFieldsStatement.setString(3, "[" + historyBatchJson + "]"); // 追加到history的batches数组
            updateJsonbFieldsStatement.setString(4, ticketId);
            updateJsonbFieldsStatement.addBatch();

            log.info("Added JSONB fields update to batch for ticket_id: {}, batch_id: {}, source_ids: {}", ticketId, batchId, sourceIds);
        } catch (Exception e) {
            log.error("Failed to update JSONB fields for ticket_id: {}", ticketId, e);
            throw new SQLException("Failed to update JSONB fields", e);
        }
    }

    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        return "BATCH_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + "_" + System.currentTimeMillis();
    }

    /**
     * 插入原始工单数据（移除 id 字段）
     */
    private void insertOriginalTicket(JsonNode originalData) throws SQLException {
        insertOriginalTicketStatement.setString(1, getTextValue(originalData, "ticket_id"));
        insertOriginalTicketStatement.setString(2, getTextValue(originalData, "question_description"));
        insertOriginalTicketStatement.setString(3, getTextValue(originalData, "solution"));
        insertOriginalTicketStatement.setString(4, getTextValue(originalData, "current_process"));
        insertOriginalTicketStatement.setString(5, getTextValue(originalData, "preparatory_question"));
        insertOriginalTicketStatement.setString(6, getTextValue(originalData, "question_summary"));
        insertOriginalTicketStatement.setString(7, getTextValue(originalData, "reason"));
        insertOriginalTicketStatement.setString(8, getTextValue(originalData, "reason_classification"));
        insertOriginalTicketStatement.setString(9, getTextValue(originalData, "solution_classification"));
        insertOriginalTicketStatement.setString(10, getTextValue(originalData, "solution_process_detail"));
        insertOriginalTicketStatement.setString(11, getTextValue(originalData, "request-id"));
        insertOriginalTicketStatement.setString(12, getTextValue(originalData, "LLM_Res"));

        insertOriginalTicketStatement.addBatch();
    }

    /**
     * 插入相似问题数据（保持原有逻辑不变）
     */
    private void insertSimilarQuestions(JsonNode originalData, SimilarSearchRequest searchRequest) throws SQLException {
        String originalTicketId = getTextValue(originalData, "ticket_id");
        String requestId = searchRequest.getRequest_id();

        List<DataItem> dataItems = searchRequest.getData();
        if (dataItems != null) {
            for (DataItem dataItem : dataItems) {
                ItemFields fields = dataItem.getFields();
                if (fields != null) {
                    insertSimilarQuestionStatement.setString(1, originalTicketId); // original_ticket_id
                    insertSimilarQuestionStatement.setString(2, fields.getParent_ticket_id()); // parent_ticket_id
                    insertSimilarQuestionStatement.setString(3, fields.getParent_ticket_title()); // parent_ticket_title
                    insertSimilarQuestionStatement.setString(4, dataItem.getID()); // similar_question_id
                    insertSimilarQuestionStatement.setString(5, fields.getTitle()); // similar_question_title
                    insertSimilarQuestionStatement.setString(6, fields.getContent()); // original_solution
                    insertSimilarQuestionStatement.setString(7, fields.getKnowledge()); // knowledge
                    insertSimilarQuestionStatement.setString(8, fields.getSource_id()); // source_id（已在 collectSourceIds 中设置）
                    insertSimilarQuestionStatement.setString(9, fields.getSource_name()); // source_name
                    insertSimilarQuestionStatement.setString(10, fields.getSource_type()); // source_type
                    insertSimilarQuestionStatement.setString(11, fields.getService_scene_level1_id()); // service_scene_level1_id
                    insertSimilarQuestionStatement.setString(12, fields.getService_scene_level2_id()); // service_scene_level2_id
                    insertSimilarQuestionStatement.setString(13, fields.getService_scene_level3_id()); // service_scene_level3_id
                    insertSimilarQuestionStatement.setString(14, fields.getService_scene_level4_id()); // service_scene_level4_id
                    insertSimilarQuestionStatement.setString(15, fields.getService_scene_checked_id()); // service_scene_checked_id
                    insertSimilarQuestionStatement.setInt(16, fields.getCloud_type()); // cloud_type
                    insertSimilarQuestionStatement.setString(17, fields.getIndex_type()); // index_type
                    insertSimilarQuestionStatement.setInt(18, fields.getVisible()); // visible
                    insertSimilarQuestionStatement.setString(19, fields.getCustomer_uin()); // customer_uin
                    insertSimilarQuestionStatement.setInt(20, fields.getTicket_create_time()); // ticket_create_time
                    insertSimilarQuestionStatement.setInt(21, fields.getTicket_close_time()); // ticket_close_time
                    insertSimilarQuestionStatement.setString(22, requestId); // request_id
                    insertSimilarQuestionStatement.setInt(23, dataItem.getDeleted()); // deleted

                    insertSimilarQuestionStatement.addBatch();
                }
            }
        }
    }

    /**
     * 执行批量操作
     */
    private void executeBatch() throws SQLException {
        if (currentBatchSize > 0) {
            int batchCount = currentBatchSize; // 保存当前批次大小用于日志

            insertOriginalTicketStatement.executeBatch();
            updateJsonbFieldsStatement.executeBatch();
            insertSimilarQuestionStatement.executeBatch();
            connection.commit();

            insertOriginalTicketStatement.clearBatch();
            updateJsonbFieldsStatement.clearBatch();
            insertSimilarQuestionStatement.clearBatch();

            currentBatchSize = 0;
            lastBatchTime = System.currentTimeMillis(); // 更新时间戳
            ticketSourceIds.clear(); // 清理临时数据

            log.info("Executed batch insert for {} records", batchCount);
        }
    }

    /**
     * 获取 JSON 字段值
     */
    private String getTextValue(JsonNode jsonNode, String fieldName) {
        JsonNode field = jsonNode.get(fieldName);
        return field != null && !field.isNull() ? field.asText() : null;
    }

    @Override
    public void close() throws Exception {
        // 关闭定时器
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        try {
            executeBatch();
        } catch (SQLException e) {
            log.error("Failed to execute final batch", e);
        }

        if (insertOriginalTicketStatement != null) {
            insertOriginalTicketStatement.close();
        }
        if (updateJsonbFieldsStatement != null) {
            updateJsonbFieldsStatement.close();
        }
        if (insertSimilarQuestionStatement != null) {
            insertSimilarQuestionStatement.close();
        }
        if (connection != null) {
            connection.close();
        }
        super.close();
    }
}