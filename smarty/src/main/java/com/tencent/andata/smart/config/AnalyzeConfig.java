package com.tencent.andata.smart.config;

import com.google.common.collect.ImmutableMap;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.analyze.AnalyzeType;
import com.tencent.andata.smart.strategy.analyze.ResType;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class AnalyzeConfig {

    private static RainbowUtils rainbow = RainbowAppConfig.getInstance();

    // 将方法转换为静态常量，确保在类加载时初始化
    private static final ImmutableMap<String, String> QUALITY_ITEM_PROMPT_MAP = ImmutableMap.<String, String>builder()
            .put("Service_Awareness", rainbow.getStringValue("ansmart.smart_quality.prompt", "Service_Awareness"))
            .put("Professional_Skills", rainbow.getStringValue("ansmart.smart_quality.prompt", "Professional_Skills"))
            .put("Communication_Skills", rainbow.getStringValue("ansmart.smart_quality.prompt", "Communication_Skills"))
            .put("Service_Specifications", rainbow.getStringValue("ansmart.smart_quality.prompt", "Service_Specifications"))
            .put("WebIM_Service_Timeliness", rainbow.getStringValue("ansmart.smart_quality.prompt", "WebIM_Service_Timeliness"))
            .put("Ticket_Service_Timeliness", rainbow.getStringValue("ansmart.smart_quality.prompt", "Ticket_Service_Timeliness"))
            .put("KA_Service_Specifications", rainbow.getStringValue("ansmart.smart_quality.ka_prompt", "Service_Specifications"))
            .put("KA_Communication_Skills", rainbow.getStringValue("ansmart.smart_quality.ka_prompt", "Communication_Skills"))
            .put("KA_Professional_Skills", rainbow.getStringValue("ansmart.smart_quality.ka_prompt", "Professional_Skills"))
            .put("KA_Service_Timeliness", rainbow.getStringValue("ansmart.smart_quality.ka_prompt", "Service_Timeliness"))
            .put("KA_Service_Awareness", rainbow.getStringValue("ansmart.smart_quality.ka_prompt", "Service_Awareness"))
            .build();

    public static Analyze Professional_Skills = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("专业技能")
            .name("Professional_Skills")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("Professional_Skills"))
            .model("quality_check_json_sft_professional_skills")
            .build();

    public static Analyze Service_Awareness = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务意识")
            .name("Service_Awareness")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("Service_Awareness"))
            .model("quality_check_json_sft_service_awareness")
            .build();

    public static Analyze Communication_Skills = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("沟通技巧")
            .name("Communication_Skills")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("Communication_Skills"))
            .model("quality_check_json_sft_communication_skills")
            .build();

    public static Analyze Ticket_Service_Timeliness = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务时效")
            .name("Service_Timeliness")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("Ticket_Service_Timeliness"))
            .model("quality_check_json_sft_service_timeliness")
            .build();

    public static Analyze WebIM_Service_Timeliness = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务时效")
            .name("Service_Timeliness")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("WebIM_Service_Timeliness"))
            .model("quality_check_json_sft_service_timeliness")
            .build();

    public static Analyze Service_Specifications = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务规范")
            .name("Service_Specifications")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("Service_Specifications"))
            .model("quality_check_json_sft_service_specifications")
            .build();

    public static Analyze KA_Service_Specifications = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务规范")
            .name("Service_Specifications")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("KA_Service_Specifications"))
            .model("quality_check_json_sft_service_specifications")
            .build();

    public static Analyze KA_Communication_Skills = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("沟通技巧")
            .name("Communication_Skills")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("KA_Communication_Skills"))
            .model("quality_check_json_sft_communication_skills")
            .build();

    public static Analyze KA_Professional_Skills = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("专业技能")
            .name("Professional_Skills")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("KA_Professional_Skills"))
            .model("quality_check_json_sft_professional_skills")
            .build();

    public static Analyze KA_Service_Timeliness = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务时效")
            .name("Service_Timeliness")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("KA_Service_Timeliness"))
            .model("quality_check_json_sft_service_timeliness")
            .build();

    public static Analyze KA_Service_Awareness = Analyze
            .builder()
            .type(AnalyzeType.Quality_Inspection)
            .desc("服务意识")
            .name("Service_Awareness")
            .resType(ResType.Json)
            .prompt(QUALITY_ITEM_PROMPT_MAP.get("KA_Service_Awareness"))
            .model("quality_check_json_sft_service_awareness")
            .build();

    private static final ImmutableMap<String, String> OPINION_RISK_PROMPT_MAP = ImmutableMap.<String, String>builder()
            .put("Opinion_Risk", rainbow.getStringValue("ansmart.opinion_risk.prompt", "Opinion_Risk"))
            .build();

    public static Analyze Public_Opinion_Risk = Analyze
            .builder()
            .type(AnalyzeType.Risk_Analyze)
            .desc("风险总结分析")
            .name("Public_Opinion_Risk")
            .resType(ResType.Json)
            .prompt(OPINION_RISK_PROMPT_MAP.get("Opinion_Risk"))
            .model("risk_sft")
            .build();

    private static final ImmutableMap<String, String> TICKET_PRIORITY_PROMPT_MAP = ImmutableMap.<String, String>builder()
            .put("Ticket_Priority_Pre", rainbow.getStringValue("ansmart.ticket_priority.prompt", "Ticket_Priority_Pre"))
            .put("Ticket_Priority_L2", rainbow.getStringValue("ansmart.ticket_priority.prompt", "Ticket_Priority_L2"))
            .put("Ticket_Priority_L3", rainbow.getStringValue("ansmart.ticket_priority.prompt", "Ticket_Priority_L3"))
            .build();

    public static Analyze TDSQL_Agent_Ticket_Priority_Pre = Analyze
            .builder()
            .type(AnalyzeType.Agent_Analyze)
            .desc("TDSQL工单优先级识别")
            .name("TDSQL_Agent_Ticket_Priority_Pre")
            .resType(ResType.Json)
            .prompt(TICKET_PRIORITY_PROMPT_MAP.get("Ticket_Priority_Pre"))
            .model("hunyuan-turbo_risk")
            .messages(new CopyOnWriteArrayList<>(Collections.singletonList(new ConcurrentHashMap<String, Object>() {{
                put("role", "system");
                put("content", "\n***请务必注意***\n你的输出请使用 JSON 格式，不要有多余的文字\n***请务必注意***");
            }})))
            .build();

    public static Analyze TDSQL_Agent_Ticket_Priority_L2 = Analyze
            .builder()
            .type(AnalyzeType.Agent_Analyze)
            .desc("TDSQL工单优先级识别")
            .name("TDSQL_Agent_Ticket_Priority_L2")
            .resType(ResType.Json)
            .prompt(TICKET_PRIORITY_PROMPT_MAP.get("Ticket_Priority_L2"))
            .model("hunyuan-turbo_risk")
            .messages(new CopyOnWriteArrayList<>(Collections.singletonList(new ConcurrentHashMap<String, Object>() {{
                put("role", "system");
                put("content", "\n***请务必注意***\n你的输出请使用 JSON 格式，不要有多余的文字\n***请务必注意***");
            }})))
            .build();

    public static Analyze TDSQL_Agent_Ticket_Priority_L3 = Analyze
            .builder()
            .type(AnalyzeType.Agent_Analyze)
            .desc("TDSQL工单优先级识别")
            .name("TDSQL_Agent_Ticket_Priority_L3")
            .resType(ResType.Json)
            .prompt(TICKET_PRIORITY_PROMPT_MAP.get("Ticket_Priority_L3"))
            .model("hunyuan-turbo_risk")
            .messages(new CopyOnWriteArrayList<>(Collections.singletonList(new ConcurrentHashMap<String, Object>() {{
                put("role", "system");
                put("content", "\n***请务必注意***\n你的输出请使用 JSON 格式，不要有多余的文字\n***请务必注意***");
            }})))
            .build();

    public static void main(String[] args) {
        System.out.println(TDSQL_Agent_Ticket_Priority_L3.prompt);
    }
}