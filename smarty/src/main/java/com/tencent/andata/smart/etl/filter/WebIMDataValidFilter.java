package com.tencent.andata.smart.etl.filter;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.log.FlinkLog;
import java.util.HashMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class WebIMDataValidFilter implements FilterFunction<String> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public boolean filter(String input) throws Exception {
        HashMap<String, Object> data = objectMapper.readValue(input, new TypeReference<HashMap<String, Object>>() {});

        String scene = (String) data.get("scene");

        if (!("WebIM".equals(scene))) {
            return true;
        }

        return validateWebIMFields(data);

    }

    /*
     * 验证WebIM场景数据的完整性
     * 检查ticketId、serviceChannel以及serviceSceneLevel2Name是否缺失
     *
     * @param data 风险结果对象
     * @return 是否数据不完整需要重试
     */
    private boolean validateWebIMFields(HashMap<String, Object> data) {
        // 检查关键字段是否为空
        String sceneLevel1Name = (String) data.get("service_scene_level1_name");
        long ticketId = Long.parseLong(data.get("ticket_id").toString());
        String serviceChannel = (String) data.get("service_channel");
        String pk = (String) data.get("pk");

        boolean isSceneLevel1NameEmpty = StringUtils.isBlank(sceneLevel1Name);
        boolean isServiceChannelEmpty = StringUtils.isBlank(serviceChannel);
        boolean isTicketIdEmpty = 0L == ticketId;

        // 记录日志
        if (isTicketIdEmpty || isServiceChannelEmpty || isSceneLevel1NameEmpty) {
            logger.warn(String.format("[WebIMDataValidFilter]validateWebIMFields: " +
                            "pk: %s, ticketId: %s, sceneLevel1Name: %s, serviceChannel: %s ",
                    pk, ticketId, sceneLevel1Name, serviceChannel));

            return false;
        }

        return true;
    }
}