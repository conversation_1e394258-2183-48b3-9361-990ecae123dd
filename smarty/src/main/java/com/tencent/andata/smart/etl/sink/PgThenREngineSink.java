package com.tencent.andata.smart.etl.sink;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.etl.serializer.KafkaREngineMessageSerializer;
import com.tencent.andata.struct.regnine.REngineMessage;
import com.tencent.andata.utils.sink.pg.ExecutionConfig;
import com.tencent.andata.utils.sink.pg.PgSinkBuilder;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import lombok.Data;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.producer.ProducerConfig;

/**
 * 严格顺序保证的组合式Sink
 * <p>
 * 确保数据先写入PostgreSQL成功后再写入规则引擎
 * 核心设计思想：
 * 1. 使用事务性PG写入 + 状态管理 + 两阶段提交
 * 2. 每条数据分配唯一ID，跟踪其处理状态
 * 3. 当且仅当PG写入成功后，才会将数据发送到Kafka
 * 4. 利用Flink的Checkpoint机制确保故障恢复时的数据一致性
 *
 * @param <T> 输入数据类型
 * <AUTHOR>
 */
public class PgThenREngineSink<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final FlinkLog logger = FlinkLog.getInstance();
    // 组件配置
    private final SinkConfig config;
    // 数据转换函数
    private final Function<T, Row> pgRowConverter;                // 原始数据转Row的转换器
    private final Function<T, REngineMessage> rEngineMessageConverter; // 原始数据转REngineMessage的转换器
    private final Function<T, String> keyExtractor;               // 提取键的函数（可选）

    /**
     * 组合式Sink构造函数
     * <p>
     * 私有构造函数，通过Builder模式创建实例
     *
     * @param config Sink配置
     * @param pgRowConverter 数据到Row的转换器
     * @param rEngineMessageConverter 数据到REngineMessage的转换器
     * @param keyExtractor 键提取器（可选）
     */
    private PgThenREngineSink(
            SinkConfig config,
            Function<T, Row> pgRowConverter,
            Function<T, REngineMessage> rEngineMessageConverter,
            Function<T, String> keyExtractor) {
        this.config = config;
        this.pgRowConverter = pgRowConverter;
        this.rEngineMessageConverter = rEngineMessageConverter;
        this.keyExtractor = keyExtractor;
    }

    /**
     * 创建Builder实例
     *
     * @param <T> 输入数据类型
     * @return Builder实例
     */
    public static <T> Builder<T> builder() {
        return new Builder<>();
    }

    /**
     * 应用Sink到数据流
     * <p>
     * 使用两阶段提交确保先写入PG成功后再写入Kafka
     * 核心流程：
     * 1. 对每条数据分配唯一ID
     * 2. 将数据写入PG，使用事务确保写入成功
     * 3. 当且仅当PG写入成功后，将数据发送到Kafka
     *
     * @param inputStream 输入数据流
     * @return 处理后的数据流
     */
    public DataStream<Tuple2<String, Boolean>> apply(DataStream<T> inputStream) {
        // 创建保证顺序写入的处理器
        SequentialWriteProcessor<T> processor = new SequentialWriteProcessor<>(
                config,
                pgRowConverter,
                rEngineMessageConverter,
                keyExtractor
        );

        // 应用处理器到数据流

        return inputStream.process(processor);
    }

    /**
     * Sink配置类
     * <p>
     * 包含PG和Kafka两部分配置信息，使用不可变对象模式设计
     */
    @Data
    public static class SinkConfig implements Serializable {

        // PostgreSQL配置
        private final DatabaseConf pgDbConf;        // 数据库连接配置
        private final ExecutionConfig execConfig;   // 执行配置（批次大小、重试次数等）
        private final String pgTableName;           // 表名
        private final Set<String> excludeColumns;   // 排除列（不写入数据库）

        // Kafka配置
        private final String kafkaBootstrapServers; // Kafka服务器地址
        private final String kafkaTopic;            // Kafka主题
        private final Properties kafkaProperties;   // Kafka属性
        private final int checkInterval;            // 检查间隔（毫秒）
        private final int maxRetryCount;            // 最大重试次数

        /**
         * 私有构造函数，通过Builder模式创建实例
         *
         * @param builder 构建器
         */
        private SinkConfig(Builder builder) {
            this.pgDbConf = builder.pgDbConf;
            this.execConfig = builder.execConfig;
            this.pgTableName = builder.pgTableName;
            this.excludeColumns = builder.excludeColumns;
            this.kafkaBootstrapServers = builder.kafkaBootstrapServers;
            this.kafkaTopic = builder.kafkaTopic;
            this.kafkaProperties = builder.kafkaProperties;
            this.checkInterval = builder.checkInterval;
            this.maxRetryCount = builder.maxRetryCount;
        }

        /**
         * 创建Builder实例
         *
         * @return Builder实例
         */
        public static Builder builder() {
            return new Builder();
        }

        /**
         * SinkConfig构建器
         * <p>
         * 使用Builder模式创建配置，提供流式API
         */
        public static class Builder {

            private DatabaseConf pgDbConf;            // 数据库连接配置
            private ExecutionConfig execConfig;       // 执行配置
            private String pgTableName;               // 表名
            private Set<String> excludeColumns;       // 排除列
            private String kafkaBootstrapServers;     // Kafka服务器
            private String kafkaTopic;                // Kafka主题
            private Properties kafkaProperties = new Properties(); // Kafka属性
            private int checkInterval = 1000;         // 检查间隔（毫秒）
            private int maxRetryCount = 3;            // 最大重试次数

            /**
             * 设置数据库连接配置
             *
             * @param pgDbConf 数据库连接配置
             * @return Builder实例
             */
            public Builder setPgDbConf(DatabaseConf pgDbConf) {
                this.pgDbConf = pgDbConf;
                return this;
            }

            /**
             * 设置执行配置
             *
             * @param execConfig 执行配置
             * @return Builder实例
             */
            public Builder setExecConfig(ExecutionConfig execConfig) {
                this.execConfig = execConfig;
                return this;
            }

            /**
             * 设置表名
             *
             * @param pgTableName 表名
             * @return Builder实例
             */
            public Builder setPgTableName(String pgTableName) {
                this.pgTableName = pgTableName;
                return this;
            }

            /**
             * 设置排除列
             *
             * @param excludeColumns 排除列集合
             * @return Builder实例
             */
            public Builder setExcludeColumns(Set<String> excludeColumns) {
                this.excludeColumns = excludeColumns;
                return this;
            }

            /**
             * 设置Kafka服务器地址
             *
             * @param kafkaBootstrapServers Kafka服务器地址
             * @return Builder实例
             */
            public Builder setKafkaBootstrapServers(String kafkaBootstrapServers) {
                this.kafkaBootstrapServers = kafkaBootstrapServers;
                return this;
            }

            /**
             * 设置Kafka主题
             *
             * @param kafkaTopic Kafka主题
             * @return Builder实例
             */
            public Builder setKafkaTopic(String kafkaTopic) {
                this.kafkaTopic = kafkaTopic;
                return this;
            }

            /**
             * 设置Kafka属性
             *
             * @param kafkaProperties Kafka属性
             * @return Builder实例
             */
            public Builder setKafkaProperties(Properties kafkaProperties) {
                this.kafkaProperties = kafkaProperties;
                return this;
            }

            /**
             * 设置检查间隔（毫秒）
             *
             * @param checkInterval 检查间隔
             * @return Builder实例
             */
            public Builder setCheckInterval(int checkInterval) {
                this.checkInterval = checkInterval;
                return this;
            }

            /**
             * 设置最大重试次数
             *
             * @param maxRetryCount 最大重试次数
             * @return Builder实例
             */
            public Builder setMaxRetryCount(int maxRetryCount) {
                this.maxRetryCount = maxRetryCount;
                return this;
            }

            /**
             * 构建SinkConfig实例
             *
             * @return SinkConfig实例
             */
            public SinkConfig build() {
                return new SinkConfig(this);
            }
        }

        public Properties getKafkaProperties() {
            return kafkaProperties;
        }

        /**
         * 获取检查间隔（毫秒）
         *
         * @return 检查间隔
         */
        public int getCheckInterval() {
            return checkInterval;
        }

        /**
         * 获取最大重试次数
         *
         * @return 最大重试次数
         */
        public int getMaxRetryCount() {
            return maxRetryCount;
        }
    }

    /**
     * 保证顺序的数据处理器
     * <p>
     * 实现CheckpointedFunction以支持状态管理和故障恢复
     *
     * @param <T> 输入数据类型
     */
    private static class SequentialWriteProcessor<T>
            extends ProcessFunction<T, Tuple2<String, Boolean>>
            implements CheckpointedFunction {

        private static final long serialVersionUID = 1L;

        // 配置
        private final SinkConfig config;
        private final Function<T, Row> pgRowConverter;
        private final Function<T, REngineMessage> rEngineMessageConverter;
        private final Function<T, String> keyExtractor;

        // 状态
        private transient ListState<ProcessingRecord> pendingRecordsState;
        private transient List<ProcessingRecord> pendingRecords;
        private transient ConcurrentHashMap<String, CompletableFuture<Boolean>> runningTasks;
        private transient AtomicLong recordCounter;

        // PG连接
        private transient SinkFunction<Row> pgSink;

        // Kafka连接
        private transient KafkaSink<REngineMessage> kafkaSink;

        /**
         * 构造函数
         */
        public SequentialWriteProcessor(
                SinkConfig config,
                Function<T, Row> pgRowConverter,
                Function<T, REngineMessage> rEngineMessageConverter,
                Function<T, String> keyExtractor) {
            this.config = config;
            this.pgRowConverter = pgRowConverter;
            this.rEngineMessageConverter = rEngineMessageConverter;
            this.keyExtractor = keyExtractor != null ? keyExtractor : obj -> UUID.randomUUID().toString();
        }

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);

            // 初始化状态
            this.pendingRecords = new ArrayList<>();
            this.runningTasks = new ConcurrentHashMap<>();
            this.recordCounter = new AtomicLong(0);

            // 初始化PG Sink
            this.pgSink = createPgSink();

            // 初始化Kafka Sink
            this.kafkaSink = createKafkaSink();
        }

        @Override
        public void processElement(T value, Context ctx, Collector<Tuple2<String, Boolean>> out) {
            // 生成记录ID
            String recordId = generateRecordId(value);

            // 转换数据
            Row pgRow = pgRowConverter.apply(value);
            REngineMessage rEngineMessage = rEngineMessageConverter.apply(value);

            // 创建处理记录
            ProcessingRecord record = new ProcessingRecord(
                    recordId,
                    pgRow,
                    rEngineMessage,
                    System.currentTimeMillis()
            );

            // 添加到待处理列表
            synchronized (pendingRecords) {
                pendingRecords.add(record);
            }

            // 启动异步处理
            CompletableFuture<Boolean> future = processRecordAsync(record);

            // 存储任务引用
            runningTasks.put(recordId, future);

            // 注册回调
            future.thenAccept(success -> {
                // 处理完成后移除任务
                runningTasks.remove(recordId);

                // 如果成功，在下次检查点时会清除状态
                if (success) {
                    synchronized (pendingRecords) {
                        pendingRecords.removeIf(r -> r.getRecordId().equals(recordId));
                    }

                    // 输出成功结果
                    try {
                        out.collect(Tuple2.of(recordId, true));
                    } catch (Exception e) {
                        logger.error("输出结果失败: " + e.getMessage());
                    }
                }
            });
        }

        /**
         * 生成记录ID
         */
        private String generateRecordId(T value) {
            String key = keyExtractor.apply(value);
            long sequence = recordCounter.incrementAndGet();
            return key + "-" + sequence;
        }

        /**
         * 异步处理记录
         * <p>
         * 先写入PG，成功后再写入Kafka
         */
        private CompletableFuture<Boolean> processRecordAsync(ProcessingRecord record) {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    // 1. 写入PG
                    boolean pgSuccess = writeToPg(record.getPgRow());

                    // 2. 如果PG写入成功，再写入Kafka
                    if (pgSuccess) {
                        return writeToKafka(record.getREngineMessage());
                    }

                    // PG写入失败
                    return false;
                } catch (Exception e) {
                    logger.error("处理记录失败: " + e.getMessage());
                    return false;
                }
            });
        }

        /**
         * 写入PG
         */
        private boolean writeToPg(Row row) {
            try {
                // 使用JdbcSink进行写入
                // 这里简化处理，实际应考虑事务和批处理
                pgSink.invoke(row, null);
                return true;
            } catch (Exception e) {
                logger.error("写入PG失败: " + e.getMessage());
                return false;
            }
        }

        /**
         * 写入Kafka
         */
        private boolean writeToKafka(REngineMessage message) {
            try {
                // 使用KafkaSink进行写入
                // 这里简化处理，实际应考虑Kafka的事务
                // kafkaSink.invoke(message, null);
                return true;
            } catch (Exception e) {
                logger.error("写入Kafka失败: " + e.getMessage());
                return false;
            }
        }

        /**
         * 创建PG Sink
         */
        private SinkFunction<Row> createPgSink() {
            // 使用PgSinkBuilder创建PG Sink
            return PgSinkBuilder.builder()
                    .fromDatabaseConf(config.getPgDbConf())
                    .executionConfig(config.getExecConfig())
                    .loadSchemaFromDatabase(config.getPgTableName())
                    .excludeColumns(config.getExcludeColumns().toArray(new String[0]))
                    .build();
        }

        /**
         * 创建Kafka Sink
         */
        private KafkaSink<REngineMessage> createKafkaSink() {
            Properties properties = new Properties();
            properties.putAll(config.getKafkaProperties());

            // 设置默认属性（如果未设置）
            if (!properties.containsKey(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG)) {
                properties.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, 15 * 60 * 1000);
            }
            if (!properties.containsKey("max.request.size")) {
                properties.put("max.request.size", "12582912");
            }

            // 创建KafkaSink
            return KafkaSink.<REngineMessage>builder()
                    .setKafkaProducerConfig(properties)
                    .setBootstrapServers(config.getKafkaBootstrapServers())
                    .setRecordSerializer(
                            KafkaRecordSerializationSchema
                                    .builder()
                                    .setTopic(config.getKafkaTopic())
                                    .setValueSerializationSchema(new KafkaREngineMessageSerializer())
                                    .build()
                    )
                    .setTransactionalIdPrefix("PgThenREngineSink-" + System.currentTimeMillis())
                    .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                    .build();
        }

        @Override
        public void snapshotState(FunctionSnapshotContext context) throws Exception {
            // 清除状态并保存当前待处理记录
            pendingRecordsState.clear();
            synchronized (pendingRecords) {
                for (ProcessingRecord record : pendingRecords) {
                    pendingRecordsState.add(record);
                }
            }
        }

        @Override
        public void initializeState(FunctionInitializationContext context) throws Exception {
            // 初始化状态描述符
            ListStateDescriptor<ProcessingRecord> descriptor =
                    new ListStateDescriptor<>(
                            "pending-records",
                            TypeInformation.of(new TypeHint<ProcessingRecord>() {})
                    );

            // 获取状态句柄
            pendingRecordsState = context.getOperatorStateStore().getListState(descriptor);

            // 如果是恢复，加载之前的记录
            if (context.isRestored()) {
                for (ProcessingRecord record : pendingRecordsState.get()) {
                    pendingRecords.add(record);
                }
            }
        }

        /**
         * 处理记录类
         * <p>
         * 包含PG和Kafka数据，以及处理状态
         */
        public static class ProcessingRecord implements Serializable {

            private static final long serialVersionUID = 1L;

            private final String recordId;      // 记录ID
            private final Row pgRow;            // PG数据
            private final REngineMessage rEngineMessage; // Kafka数据
            private final long timestamp;       // 时间戳

            public ProcessingRecord(
                    String recordId,
                    Row pgRow,
                    REngineMessage rEngineMessage,
                    long timestamp) {
                this.recordId = recordId;
                this.pgRow = pgRow;
                this.rEngineMessage = rEngineMessage;
                this.timestamp = timestamp;
            }

            public String getRecordId() {
                return recordId;
            }

            public Row getPgRow() {
                return pgRow;
            }

            public REngineMessage getREngineMessage() {
                return rEngineMessage;
            }

            public long getTimestamp() {
                return timestamp;
            }
        }
    }

    /**
     * PgThenREngineSink的构建器
     * <p>
     * 提供流式API创建PgThenREngineSink实例
     *
     * @param <T> 输入数据类型
     */
    public static class Builder<T> {

        private SinkConfig config;                              // Sink配置
        private Function<T, Row> pgRowConverter;               // 数据到Row的转换器
        private Function<T, REngineMessage> rEngineMessageConverter; // 数据到REngineMessage的转换器
        private Function<T, String> keyExtractor;              // 键提取器（可选）

        /**
         * 设置Sink配置
         *
         * @param config Sink配置
         * @return Builder实例
         */
        public Builder<T> withConfig(SinkConfig config) {
            this.config = config;
            return this;
        }

        /**
         * 设置数据到Row的转换器
         *
         * @param pgRowConverter 数据到Row的转换器
         * @return Builder实例
         */
        public Builder<T> withPgRowConverter(Function<T, Row> pgRowConverter) {
            this.pgRowConverter = pgRowConverter;
            return this;
        }

        /**
         * 设置数据到REngineMessage的转换器
         *
         * @param rEngineMessageConverter 数据到REngineMessage的转换器
         * @return Builder实例
         */
        public Builder<T> withREngineMessageConverter(Function<T, REngineMessage> rEngineMessageConverter) {
            this.rEngineMessageConverter = rEngineMessageConverter;
            return this;
        }

        /**
         * 设置键提取器（可选）
         *
         * @param keyExtractor 键提取器
         * @return Builder实例
         */
        public Builder<T> withKeyExtractor(Function<T, String> keyExtractor) {
            this.keyExtractor = keyExtractor;
            return this;
        }

        /**
         * 构建PgThenREngineSink实例
         * <p>
         * 验证必要参数并创建实例
         *
         * @return PgThenREngineSink实例
         */
        public PgThenREngineSink<T> build() {
            // 验证必要参数
            if (config == null) {
                throw new IllegalArgumentException("config不能为空");
            }
            if (config.getPgDbConf() == null) {
                throw new IllegalArgumentException("pgDbConf不能为空");
            }
            if (config.getPgTableName() == null) {
                throw new IllegalArgumentException("pgTableName不能为空");
            }
            if (config.getKafkaBootstrapServers() == null) {
                throw new IllegalArgumentException("kafkaBootstrapServers不能为空");
            }
            if (config.getKafkaTopic() == null) {
                throw new IllegalArgumentException("kafkaTopic不能为空");
            }
            if (pgRowConverter == null) {
                throw new IllegalArgumentException("pgRowConverter不能为空");
            }
            if (rEngineMessageConverter == null) {
                throw new IllegalArgumentException("rEngineMessageConverter不能为空");
            }

            return new PgThenREngineSink<>(
                    config,
                    pgRowConverter,
                    rEngineMessageConverter,
                    keyExtractor
            );
        }
    }
}