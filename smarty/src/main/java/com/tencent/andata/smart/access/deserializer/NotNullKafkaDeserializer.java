package com.tencent.andata.smart.access.deserializer;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

public class NotNullKafkaDeserializer implements KafkaRecordDeserializationSchema<String> {
    @Override
    public TypeInformation<String> getProducedType() {
        return TypeInformation.of(String.class);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<String> collector) {
        if (consumerRecord.value() != null) {
            collector.collect(new String(consumerRecord.value()));
        }
    }
}