package com.tencent.andata.smart.access.service;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.access.model.TicketInfo;
import com.tencent.andata.smart.utils.CustomerInfoUtils;
import com.tencent.andata.smart.utils.DutyInfoUtils;
import com.tencent.andata.smart.utils.StaffInfoUtils;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import io.vavr.control.Option;

import static com.tencent.andata.utils.TimeUtil.getEpochMilli;

/**
 * 工单信息丰富器
 * 负责将TicketInfo对象转换为JSON格式，并添加额外的派生字段
 */
public class TicketEnricher {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String PREFIXURL = "https://andon.cloud.tencent.com/ticket/nologin/redirect?id=%s&sign=%s";
    /**
     * 使用工单信息丰富JSON节点
     *
     * @param jsonNode 原始JSON节点
     * @param ticketInfo 工单信息
     * @return 丰富后的JSON节点
     */
    public JsonNode enrich(JsonNode jsonNode, TicketInfo ticketInfo) {
        try {
            ObjectNode node = (ObjectNode) jsonNode;

            // 生成签名和当前时间
            int post = jsonNode.has("post") ? jsonNode.get("post").asInt() : ticketInfo.getPost();
            long uin = jsonNode.has("uin") ? jsonNode.get("uin").asLong() : ticketInfo.getLongUin();
            String sign = DigestUtils.md5Hex(DigestUtils.md5Hex(ticketInfo.getTicketId() + "andontcs"));
            int factAssign = jsonNode.has("fact_assign") ? jsonNode.get("fact_assign").asInt() : ticketInfo.getFactAssign();
            String createTime = jsonNode.has("rpc_name") ? timestampToString(jsonNode.get("create_time").asLong()) : ticketInfo.getFormattedCreateTime();

            // 填充基本字段并获取公司名称
            node.put("customerName", CustomerInfoUtils.getCompanyName(Arrays.asList(uin, ticketInfo.getLongOwnerUin())).getOrElse(""));
            node.put("current_operator_company_id", StaffInfoUtils.getAssignCompanyId(ticketInfo.getCurrentOperator()));
            node.put("current_operator", StaffInfoUtils.getUserName(ticketInfo.getCurrentOperator()));
            node.put("responsible", StaffInfoUtils.getUserName(ticketInfo.getResponsible()));
            node.put("start_time", getEpochMilli(ticketInfo.getFormattedCreateTime()));
            node.put("url", String.format(PREFIXURL, ticketInfo.getTicketId(), sign));
            node.put("fact_assign_duty_name", DutyInfoUtils.getDutyName(factAssign));
            node.put("service_scene", ticketInfo.getEffectiveServiceScene());
            node.put("service_channel", ticketInfo.getServiceChannel());
            node.put("end_time", getEpochMilli(getEndTime(jsonNode)));
            node.put("owner_uin", ticketInfo.getLongOwnerUin());
            node.put("company_id", ticketInfo.getCompanyId());
            node.put("ticket_status", ticketInfo.getStatus());
            node.put("question", ticketInfo.getQuestion());
            node.put("priority", ticketInfo.getPriority());
            node.put("group_id", ticketInfo.getGroupId());
            node.put("title", ticketInfo.getTitle());
            node.put("name", ticketInfo.getName());
            node.put("create_time", createTime);
            node.put("fact_assign", factAssign);
            node.put("post", post);
            node.put("uin", uin);

            return node;
        } catch (Exception e) {
            logger.error("[TicketEnricher] Error enriching ticket: " + e.getMessage());
            return createDefaultNode(jsonNode);
        }
    }

    /**
     * 为JSON节点设置默认值
     * 当无法获取工单信息或发生异常时使用
     *
     * @param jsonNode 原始JSON节点
     * @return 设置了默认值的JSON节点
     */
    public JsonNode createDefaultNode(JsonNode jsonNode) {
        try {
            ObjectNode node = (ObjectNode) jsonNode;
            String endTime = LocalDateTime.now().format(FORMATTER);
            LocalDateTime createTime = LocalDateTime.now().minusSeconds(1800L);
            String createTimeStr = createTime.format(FORMATTER);

            // 设置时间相关字段
            node.put("start_time", getEpochMilli(createTimeStr) - 1800L);
            node.put("end_time", getEpochMilli(endTime));
            node.put("create_time", createTimeStr);

            // 设置工单字段默认值
            node.put("service_channel", -1);
            node.put("service_scene", -1);
            node.put("fact_assign", 0);
            node.put("group_id", "");
            node.put("priority", 0);
            node.put("title", "");
            node.put("status", 0);

            // 设置用户和公司相关字段默认值
            node.put("current_operator_company_id", -1);
            node.put("customerName", "");
            node.put("fact_assign_duty_name", "");
            node.put("current_operator", "");
            node.put("responsible", "");
            node.put("company_id", -1);
            node.put("url", "");
            node.put("question", "");
            node.put("post", 0);
            node.put("name", "");
            node.put("owner_uin", 0L);
            node.put("uin", 0L);

            return node;
        } catch (Exception e) {
            logger.error("[TicketEnricher] Error setting default values: " + e.getMessage());
            return jsonNode;
        }
    }

    /**
     * 获取结束时间
     * webim: 使用record_update_time
     * 群/c2000: 使用msg_time
     * 工单：使用operate_time
     * 其他：使用当前时间
     *
     * @param jsonNode 原始JSON节点
     * @return 结束时间
     */
    public String getEndTime(JsonNode jsonNode) {
        return Option.when(jsonNode.has("record_update_time"), () -> jsonNode.get("record_update_time").asText())
                .orElse(Option.when(jsonNode.has("operate_time"), () -> jsonNode.get("operate_time").asText()))
                .orElse(Option.when(jsonNode.has("msg_time"), () -> jsonNode.get("msg_time").asText()))
                .getOrElse(() -> LocalDateTime.now().format(FORMATTER));
    }

    /**
     * 将时间戳（单位：秒）转换为格式化的字符串。
     *
     * @param timestamp 时间戳，单位为秒
     * @return 格式化后的时间字符串
     */
    private static String timestampToString(long timestamp) {
        // 将long类型的时间戳转换为LocalDateTime后再格式化
        return FORMATTER.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp * 1000L), ZoneId.systemDefault()));
    }
}