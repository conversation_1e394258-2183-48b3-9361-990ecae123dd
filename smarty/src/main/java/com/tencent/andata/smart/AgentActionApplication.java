package com.tencent.andata.smart;

import com.tencent.andata.smart.etl.SmartyAgentProcessETL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.java.utils.ParameterTool;

public class AgentActionApplication {
    public static void main(String[] args) throws Exception {
        FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();

        final SmartyAgentProcessETL etl = SmartyAgentProcessETL.builder()
                .rainbowUtils(rainbowUtils)
                .rdbTableName(parameterTool.get("rdbTableName"))
                .preStrategyIds(parameterTool.get("preStrategyIds"))
                .icebergTableName(parameterTool.get("icebergTableName"))
                .reCheckStrategyIds(parameterTool.get("reCheckStrategyIds"))
                .build();

        etl.run(flinkEnv);

        flinkEnv.env().execute("Smart Agent Process");
    }
}