package com.tencent.andata.smart.strategy.chunk.provider;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.model.HbaseTable;
import com.tencent.andata.smart.strategy.chunk.source.ConversationSource;
import com.tencent.andata.smart.strategy.chunk.source.HbaseGroupMsgSource;
import com.tencent.andata.smart.enums.Scene;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.client.AsyncConnection;

@Slf4j
public class HbaseGroupMsgChunkContextProvider implements ChunkContextProvider {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private final ConversationSource conversationSource;

    public HbaseGroupMsgChunkContextProvider(
            AsyncConnection asyncConnection,
            Map<Scene, HbaseTable> operationTableMap,
            ExecutorService executorService) {

        this.conversationSource = new HbaseGroupMsgSource(asyncConnection, operationTableMap, executorService);
    }

    @Override
    public CompletableFuture<String> getContext(Strategy strategy) {
        return conversationSource.getOperation(strategy);
    }

    @Override
    public boolean supports(Strategy strategy) {
        return (strategy.chunk.type == ChunkType.Range);
    }
}