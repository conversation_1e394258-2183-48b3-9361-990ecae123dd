package com.tencent.andata.smart.etl.domain;

import lombok.Builder;
import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonNaming;

@Value
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RiskResult {

    String pk;
    Long ticketId;
    String riskReason;
    String riskLevel;
    String triggerContent;
    String scene;
    String sceneIdentify;
    Long triggerTime;
    String riskOperationId;
    String triggerRiskType;
    String dutyResponsible;
    String idr1;
    Long eventTime;
    String serviceSceneLevel1Name;
    String serviceSceneLevel2Name;
    String serviceSceneLevel3Name;
    String serviceSceneLevel4Name;
    String serviceChannel;
    String currentOperator;
    String factAssign;
    String url;
    String responsible;
    String priority;
    String title;
    String question;
    Long uin;
    String companyId;
    String customerName;
    String post;
    Integer msgSeq;
    String aftersalesType;
    String salesChannel;
    String salesBackup;
    String serviceProviders;
    Integer currentOperatorCompanyId;
}