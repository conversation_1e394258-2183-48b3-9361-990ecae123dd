package com.tencent.andata.smart.similar.service;

import static com.tencent.andata.smart.similar.util.BusinessUtils.buildKnowledge;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.addStringField;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.getLongValue;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.getTextValue;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.similar.model.DirectLLMResponse;
import com.tencent.andata.smart.similar.model.req.DataItem;
import com.tencent.andata.smart.similar.model.req.ItemFields;
import com.tencent.andata.smart.similar.model.req.SimilarSearchRequest;
import com.tencent.andata.utils.SnowflakeDistributeId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

public class SimilarBuildMap extends
        RichMapFunction<Tuple2<JsonNode, DirectLLMResponse>, Tuple2<JsonNode, SimilarSearchRequest>> {

    private transient FlinkLog zhiyanLog = FlinkLog.getInstance();
    private ObjectMapper mapper;
    private transient SnowflakeDistributeId idWorker;

    public SimilarBuildMap(ObjectMapper objectMapper, SnowflakeDistributeId idWorker) {
        this.mapper = objectMapper;
        this.idWorker = idWorker;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.zhiyanLog = FlinkLog.getInstance();
        // Reinitialize idWorker if it's null after deserialization
        if (this.idWorker == null) {
            this.idWorker = new SnowflakeDistributeId(0, 0);
        }
    }

    @Override
    public Tuple2<JsonNode, SimilarSearchRequest> map(
            Tuple2<JsonNode, DirectLLMResponse> input) throws Exception {

        JsonNode jsonNode = input.f0;
        DirectLLMResponse response = input.f1;

        String ticketId = getTextValue(jsonNode, "ticket_id", "");
        String requestId = getTextValue(jsonNode, "request-id", "");

        List<String> parsedAnswers = response != null ? response.getParsedAnswers() : null;

        String originalSolution = getTextValue(jsonNode, "solution", "");
        String originalQuestionDescription = getTextValue(jsonNode, "question_description",
                "");

        Long ticketCreateTime = getLongValue(jsonNode, "ticket_create_time", 0L);
        Long ticketCloseTime = getLongValue(jsonNode, "ticket_close_time", 0L);
        String customerUin = getTextValue(jsonNode, "uin", "");
        String serviceSceneLevel1 = getTextValue(jsonNode, "service_scene_level1_id", "");
        String serviceSceneLevel2 = getTextValue(jsonNode, "service_scene_level2_id", "");
        String serviceSceneLevel3 = getTextValue(jsonNode, "service_scene_level3_id", "");
        String serviceSceneLevel4 = getTextValue(jsonNode, "service_scene_level4_id", "");
        String serviceSceneChecked = getTextValue(jsonNode, "service_scene_checked", "");

        if (parsedAnswers == null || parsedAnswers.isEmpty()) {
            List<ItemFields> itemFieldsList = new ArrayList<ItemFields>();
            ItemFields ticketFields = ItemFields.builder()
                    .title(originalQuestionDescription)
                    .content(originalSolution)
                    .knowledge(buildKnowledge(originalQuestionDescription, originalSolution))
                    .source_id(ticketId)
                    .source_type("ticket")
                    .source_name("similar_search")
                    .visible(3)
                    .cloud_type(3)
                    .index_type("qa")
                    .service_scene_level1_id(serviceSceneLevel1)
                    .service_scene_level2_id(serviceSceneLevel2)
                    .service_scene_level3_id(serviceSceneLevel3)
                    .service_scene_level4_id(serviceSceneLevel4)
                    .service_scene_checked_id(serviceSceneChecked)
                    .customer_uin(customerUin)
                    .ticket_create_time((int) (ticketCreateTime / 1000))
                    .ticket_close_time((int) (ticketCloseTime / 1000))
                    .parent_ticket_id("")
                    .parent_ticket_title("")
                    .build();
            itemFieldsList.add(ticketFields);

            List<DataItem> dataItemList = itemFieldsList.stream()
                    .map(itemFields -> {
                        if (!itemFields.validateRequiredFields()) {
                            zhiyanLog.warn("ItemFields validation failed for source_id: {}",
                                    itemFields.getSource_id());
                        }

                        return DataItem.builder()
                                .ID(itemFields.getSource_name() + "-" +
                                        itemFields.getSource_type() + "-" +
                                        itemFields.getSource_id())
                                .doc(itemFields.getTitle())
                                .deleted(0)
                                .create_time(itemFields.getCreateTimeAsTimestamp())
                                .fields(itemFields)
                                .build();
                    })
                    .collect(Collectors.toList());

            SimilarSearchRequest searchRequest = SimilarSearchRequest.builder()
                    .dataset("similar_search")
                    .data_type(1)
                    .rebuild(true)
                    .cover(true)
                    .data(dataItemList)
                    .request_id(requestId)
                    .build();

            ArrayNode sourceMapNode = mapper.createArrayNode();

            itemFieldsList.stream().forEach(itemFields -> {
                ObjectNode item = mapper.createObjectNode();
                sourceMapNode.add(item.put(itemFields.getSource_id(), itemFields.getTitle()));
            });

            return Tuple2.of(
                    addStringField(jsonNode, "similar_relation",
                            mapper.writerWithDefaultPrettyPrinter().writeValueAsString(sourceMapNode),
                            mapper),
                    searchRequest);
        }

        List<ItemFields> itemFieldsList = parsedAnswers.stream()
                .map(similarQuestionTitle -> {

                    ItemFields itemFields = ItemFields.builder()
                            .title(similarQuestionTitle)
                            .content(originalSolution)
                            .knowledge(buildKnowledge(similarQuestionTitle, originalSolution))
                            .source_id(String.valueOf(idWorker.nextId()))
                            .source_type("similarity_ticket")
                            .source_name("similar_search")
                            .visible(3)
                            .cloud_type(3)
                            .index_type("qa")
                            .service_scene_level1_id(serviceSceneLevel1)
                            .service_scene_level2_id(serviceSceneLevel2)
                            .service_scene_level3_id(serviceSceneLevel3)
                            .service_scene_level4_id(serviceSceneLevel4)
                            .service_scene_checked_id(serviceSceneChecked)
                            .customer_uin(customerUin)
                            .ticket_create_time((int) (ticketCreateTime / 1000))
                            .ticket_close_time((int) (ticketCloseTime / 1000))
                            .parent_ticket_id(ticketId)
                            .parent_ticket_title(originalQuestionDescription)
                            .build();

                    itemFields.setCurrentTime();
                    return itemFields;
                })
                .collect(Collectors.toList());

        ArrayNode sourceMapNode = mapper.createArrayNode();

        itemFieldsList.stream().forEach(itemFields -> {
            ObjectNode item = mapper.createObjectNode();
            sourceMapNode.add(item.put(itemFields.getSource_id(), itemFields.getTitle()));
        });

        ItemFields ticketFields = ItemFields.builder()
                .title(originalQuestionDescription)
                .content(originalSolution)
                .knowledge(buildKnowledge(originalQuestionDescription, originalSolution))
                .source_id(ticketId)
                .source_type("ticket")
                .source_name("similar_search")
                .visible(3)
                .cloud_type(3)
                .index_type("qa")
                .service_scene_level1_id(serviceSceneLevel1)
                .service_scene_level2_id(serviceSceneLevel2)
                .service_scene_level3_id(serviceSceneLevel3)
                .service_scene_level4_id(serviceSceneLevel4)
                .service_scene_checked_id(serviceSceneChecked)
                .customer_uin(customerUin)
                .ticket_create_time((int) (ticketCreateTime / 1000))
                .ticket_close_time((int) (ticketCloseTime / 1000))
                .parent_ticket_id("")
                .parent_ticket_title("")
                .build();
        itemFieldsList.add(ticketFields);

        List<DataItem> dataItemList = itemFieldsList.stream()
                .map(itemFields -> {
                    if (!itemFields.validateRequiredFields()) {
                        zhiyanLog.warn("ItemFields validation failed for source_id: {}", itemFields.getSource_id());
                    }
                    return DataItem.builder()
                            .ID(itemFields.getSource_name() + "-" +
                                    itemFields.getSource_type() + "-" +
                                    itemFields.getSource_id())
                            .doc(itemFields.getTitle())
                            .deleted(0)
                            .create_time(itemFields.getCreateTimeAsTimestamp())
                            .fields(itemFields)
                            .build();
                })
                .collect(Collectors.toList());

        SimilarSearchRequest searchRequest = SimilarSearchRequest.builder()
                .dataset("similar_search")
                .data_type(1)
                .rebuild(true)
                .cover(true)
                .data(dataItemList)
                .request_id(requestId)
                .build();
        return Tuple2.of(
                addStringField(jsonNode, "similar_relation",
                        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(sourceMapNode),
                        mapper),
                searchRequest);
    }
}