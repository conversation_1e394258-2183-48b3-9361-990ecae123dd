package com.tencent.andata.smart.utils.aviatorFuns;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class PutNestedFunction extends AbstractFunction {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject pathObj, AviatorObject valueObj) {
        String path = FunctionUtils.getStringValue(pathObj, env);
        Object value = valueObj.getValue(env);


        Object current = env;
        Object parent;
        String[] parts = path.split("\\.");
        String lastPart = parts[parts.length - 1];

        // 遍历直到倒数第二个部分
        for (int i = 0; i < parts.length - 1; i++) {
            parent = current;
            String part = parts[i];

            if (part.contains("[") && part.contains("]")) {
                // 处理数组访问
                String[] arrayAccess = part.split("\\[");
                String propertyName = arrayAccess[0];
                String indexStr = arrayAccess[1].replace("]", "");

                if (!propertyName.isEmpty()) {
                    if (current instanceof Map) {
                        current = ((Map<?, ?>) current).get(propertyName);
                        if (current == null) {
                            current = new ArrayList<>();
                            ((Map<String, Object>) parent).put(propertyName, current);
                        }
                    }
                }

                try {
                    int index = Integer.parseInt(indexStr);
                    List<Object> list = (List<Object>) current;
                    while (list.size() <= index) {
                        list.add(new HashMap<>());
                    }
                    current = list.get(index);
                } catch (NumberFormatException e) {
                    return FunctionUtils.wrapReturn(null);
                }
            } else {
                if (current instanceof Map) {
                    Object next = ((Map<?, ?>) current).get(part);
                    if (next == null) {
                        next = new HashMap<String, Object>();
                        ((Map<String, Object>) current).put(part, next);
                    }
                    current = next;
                }
            }
        }

        // 处理最后一个部分
        if (current instanceof Map) {
            ((Map<String, Object>) current).put(lastPart, value);
        }

        // 转换回JsonNode并返回
        JsonNode jsonNode = objectMapper.valueToTree(env);
        return FunctionUtils.wrapReturn(jsonNode);
    }

    @Override
    public String getName() {
        return "putNested";
    }
}