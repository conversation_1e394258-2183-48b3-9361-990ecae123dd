package com.tencent.andata.smart.enums;


import io.vavr.collection.List;
import lombok.Getter;

/**
 * 操作者类型枚举
 */
@Getter
public enum CallDirectionType {
    OUTGOING(1, "呼出"),
    INCOMING(2, "呼入");

    private final int code;
    private final String desc;

    CallDirectionType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CallDirectionType fromCode(int code) {
        return List.of(values())
                .filter(t -> t.code == code)
                .getOrNull();
    }

    public static String getDesc(int code) {
        return List.of(values())
                .filter(t -> t.code == code)
                .map(CallDirectionType::getDesc)
                .getOrNull();
    }
}