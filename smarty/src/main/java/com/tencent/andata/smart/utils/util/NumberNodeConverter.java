package com.tencent.andata.smart.utils.util;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class NumberNodeConverter implements JsonNodeConverter {

    @Override
    public boolean canConvert(JsonNode node) {
        return node.isNumber();
    }

    @Override
    public Object convert(JsonNode node) {
        if (node.isInt()) {
            return node.asInt();
        }
        if (node.isLong()) {
            return node.asLong();
        }
        if (node.isDouble()) {
            return node.asDouble();
        }
        return node.numberValue();
    }
}