package com.tencent.andata.smart.etl;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.access.deserializer.NotNullKafkaDeserializer;
import com.tencent.andata.smart.config.AppConfig;
import com.tencent.andata.smart.etl.filter.StrategyFilter;
import com.tencent.andata.smart.etl.process.SlidingWindowRateLimitingProcess;
import com.tencent.andata.smart.etl.process.StrategyAnalyzeLLMProcess;
import com.tencent.andata.smart.etl.process.StrategyAnalyzeSyncLLMProcess;
import com.tencent.andata.smart.etl.process.StrategyChunkBuildConversationAsyncProcess;
import com.tencent.andata.smart.etl.process.StrategyConditionProcess;
import com.tencent.andata.smart.etl.process.StrategyToMessageProcess;
import com.tencent.andata.smart.etl.process.StrategyTriggerProcess;
import com.tencent.andata.smart.etl.serializer.KafkaStrategyStringSerializer;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.RainbowUtils;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import lombok.Builder;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.formats.avro.AvroSerializationSchema;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/**
 * 统一策略触发->模型处理 发送MQ
 */
@Builder
public class SmartyModelAnalyzeETL {

    public static FlinkLog logger = FlinkLog.getInstance();

    private static String gptGroup = "cprb.gpt.prompt";
    private static String dataSourceMqGroup = "mq.kafka.ansmart_quality";

    private static String hbaseConfGroup = "cdc.database.hbase";
    private static String internalDbusGroup = "mq.kafka.internal_data_bus";
    private static String smartyResultGroup = "mq.kafka.ansmart_model_result";

    private RainbowUtils rainbowUtils;
    private String dbName;
    private Long rateLimit;
    private Long windowSize;
    private Long slideStep;
    private Integer batchSize;
    private AppConfig appConfig;
    private Boolean isSync;

    public void run(FlinkEnvUtils.FlinkEnv flinkEnv) throws Exception {
        // 获取数据源
        DataStream<String> mqSource = getMqSource(flinkEnv);
        // 判断命中策略
        // TODO 一期使用简单Condition判断，二期迁移CEP
        DataStream<Strategy> strategyStream = mqSource
                .process(new StrategyConditionProcess(appConfig.strategies))
                .name("Strategy_Condition")
                .uid("Strategy_Condition")
                .rebalance();

        // 触发器
        // TODO 二期考虑延迟触发
        DataStream<Strategy> triggeredStrategyStream = strategyStream
                .filter(Objects::nonNull)
                .process(new StrategyTriggerProcess())
                .name("Strategy_Trigger")
                .uid("Strategy_Trigger")
                .rebalance();

        // 获取上下文
        String zkQuorum = rainbowUtils.getStringValue(hbaseConfGroup, "ZOOKEEPER_QUORUM");
        String zkNodeParent = rainbowUtils.getStringValue(hbaseConfGroup, "ZOOKEEPER_ZNODE_PARENT");

        // 创建异步处理器
        StrategyChunkBuildConversationAsyncProcess asyncProcessor =
                new StrategyChunkBuildConversationAsyncProcess(zkQuorum, zkNodeParent);

        // 应用异步处理
        DataStream<Strategy> contextStrategyStream = AsyncDataStream
                .unorderedWait(triggeredStrategyStream, asyncProcessor, 10, TimeUnit.MINUTES, 120)
                .name("Strategy_Build_Conversation")
                .uid("Strategy_Build_Conversation")
                .filter(new StrategyFilter())
                .name("Strategy_Conversation_Filter")
                .uid("Strategy_Conversation_Filter");

        SingleOutputStreamOperator<Strategy> rateLimitedStream = contextStrategyStream
                .keyBy(strategy -> strategy.sceneIdentify)
                // 每10秒限流20条
                .process(new SlidingWindowRateLimitingProcess(rateLimit, windowSize, slideStep, batchSize))
                .setParallelism(appConfig.LLMParallelism)
                .name("Sliding_Window_Rate_Limiting")
                .uid("Sliding_Window_Rate_Limiting");

        SingleOutputStreamOperator<Strategy> analyzeResStreasm = processStrategyStream(rateLimitedStream, isSync);

        SingleOutputStreamOperator<Message> resStream = analyzeResStreasm
                .process(new StrategyToMessageProcess(dbName))
                .name("Strategy_To_Message")
                .uid("Strategy_To_Message");

        // 模型结果发送下游MQ
        sinkToResultMQ(analyzeResStreasm);
        // 发往内部数据总线，入库ice
        sinkToInternal(resStream);
    }


    /**
     * 从kafka中获取风控来源数据
     *
     * @param flinkEnv env
     * @return data stream
     */
    private DataStream<String> getMqSource(FlinkEnvUtils.FlinkEnv flinkEnv) {
        String entryPoint = rainbowUtils.getStringValue(dataSourceMqGroup, "BROKERS");
        String topic = rainbowUtils.getStringValue(dataSourceMqGroup, "TOPICS");
        String consumerGroup = rainbowUtils.getStringValue(dataSourceMqGroup, appConfig.MQConsumerKey);

        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers(entryPoint)
                .setTopics(topic)
                // 从消费组提交offset开始消费，不存在则从最新的消费
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setGroupId(consumerGroup)
                .setDeserializer(new NotNullKafkaDeserializer())
                .setProperties(
                        new Properties() {{
                            // 在CK时Commit数据
                            setProperty("commit.offsets.on.checkpoint", "true");
                            setProperty("enable.auto.commit", "false");
                            setProperty("fetch.max.wait.ms", "10000");
                            //setProperty("fetch.min.bytes", "1024");
                            //setProperty("max.poll.records", "100");
                        }})
                .build();

        String uid = "smarty_mq_source";
        return flinkEnv
                .env()
                .fromSource(source, WatermarkStrategy.noWatermarks(), uid)
                .uid(uid)
                .name(uid)
                .setParallelism(4);
    }

    private void sinkToResultMQ(SingleOutputStreamOperator<Strategy> ds) {
        MqConf mq = MqConf.builder()
                .broker(rainbowUtils.getStringValue(smartyResultGroup, "ENTRYPOINT"))
                .topic(rainbowUtils.getStringValue(smartyResultGroup, "TOPICS"))
                .build();

        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", mq.getBroker());
        properties.setProperty("max.request.size", "12582912");

        KafkaSink<Strategy> sink = KafkaSink
                .<Strategy>builder()
                .setKafkaProducerConfig(properties)
                .setBootstrapServers(mq.getBroker())
                .setRecordSerializer(
                        KafkaRecordSerializationSchema
                                .builder()
                                .setTopic(mq.getTopic())
                                // 将Message序列化成Avro格式发送
                                .setValueSerializationSchema(new KafkaStrategyStringSerializer())
                                .build())
                .build();

        ds.sinkTo(sink)
                .name("Sink to result mq")
                .uid("Sink_to_result_mq")
                .setParallelism(4);
    }

    /**
     * 发送结果数据到内部数据总线入库Ice
     *
     * @param ds 数据流
     */
    private void sinkToInternal(SingleOutputStreamOperator<Message> ds) {
        MqConf mq = MqConf
                .builder()
                .broker(rainbowUtils.getStringValue(internalDbusGroup, "ENTRYPOINT"))
                .topic(rainbowUtils.getStringValue(internalDbusGroup, "TOPICS"))
                .build();

        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", mq.getBroker());
        properties.setProperty("max.request.size", "12582912");

        KafkaSink<Message> sink = KafkaSink
                .<Message>builder()
                .setKafkaProducerConfig(properties)
                .setBootstrapServers(mq.getBroker())
                .setRecordSerializer(
                        KafkaRecordSerializationSchema
                                .builder()
                                .setTopic(mq.getTopic())
                                // 将Message序列化成Avro格式发送
                                .setValueSerializationSchema(AvroSerializationSchema.forSpecific(Message.class))
                                .build())
                .build();

        ds.sinkTo(sink)
                .name("Sink to internal")
                .uid("Sink_to_internal")
                .setParallelism(4);
    }


    /**
     * 根据isSync参数处理策略流
     *
     * @param inputStream 输入流
     * @param isSync 是否同步处理
     * @return 处理后的策略流
     */
    private SingleOutputStreamOperator<Strategy> processStrategyStream(DataStream<Strategy> inputStream, boolean isSync) {
        // 调用LLM的方式通过参数决定是同步还是异步
        final String modelUrl = rainbowUtils.getStringValue(gptGroup, "url");
        final String modelToken = rainbowUtils.getStringValue(gptGroup, "token");

        return isSync ? processSyncStream(inputStream, modelUrl, modelToken)
                : processAsyncStream(inputStream, modelUrl, modelToken);
    }

    /**
     * 同步处理策略流
     */
    private SingleOutputStreamOperator<Strategy> processSyncStream(DataStream<Strategy> inputStream, String modelUrl, String modelToken) {
        StrategyAnalyzeSyncLLMProcess syncProcessorLLM = StrategyAnalyzeSyncLLMProcess
                .builder()
                .modelUrl(modelUrl)
                .modelToken(modelToken)
                .build();

        return inputStream
                .process(syncProcessorLLM)
                .setParallelism(4)
                .name("Strategy_Analyze_Sync")
                .uid("Strategy_Analyze_Sync");
    }

    /**
     * 异步处理策略流
     */
    private SingleOutputStreamOperator<Strategy> processAsyncStream(DataStream<Strategy> inputStream, String modelUrl, String modelToken) {
        StrategyAnalyzeLLMProcess asyncProcessorLLM = StrategyAnalyzeLLMProcess
                .builder()
                .modelUrl(modelUrl)
                .modelToken(modelToken)
                .build();

        return AsyncDataStream
                .unorderedWait(inputStream.rebalance(), asyncProcessorLLM, 10, TimeUnit.MINUTES, 2)
                .setParallelism(4)
                .name("Strategy_Analyze_Async")
                .uid("Strategy_Analyze_Async");
    }
}