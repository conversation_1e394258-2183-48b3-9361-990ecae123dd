package com.tencent.andata.smart.etl.process;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.log.FlinkLog;
import java.sql.Timestamp;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

public class AgentDataToRowProcess extends ProcessFunction<String, Row> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private ObjectMapper objectMapper;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public void processElement(String s, Context context, Collector<Row> collector) throws Exception {
        Row res = Row.withNames(RowKind.INSERT);
        try {
            JsonNode jsonData = this.objectMapper.readTree(s);
            res.setField("pk", jsonData.get("pk").asText());
            res.setField("url", jsonData.get("url").asText());
            res.setField("post", jsonData.get("post").asText());
            res.setField("uin", jsonData.get("uin").longValue());
            res.setField("scene", jsonData.get("scene").asText());
            res.setField("reason", jsonData.get("reason").asText());
            res.setField("status", jsonData.get("status").asText());
            res.setField("priority", jsonData.get("priority").asText());
            res.setField("is_rise_up", jsonData.get("is_rise_up").asInt());
            res.setField("is_consult", jsonData.get("is_consult").asText());
            res.setField("is_obvious", jsonData.get("is_obvious").asText());
            res.setField("is_mention", jsonData.get("is_mention").asText());
            res.setField("ticket_id", jsonData.get("ticket_id").longValue());
            res.setField("fact_assign", jsonData.get("fact_assign").asText());
            res.setField("responsible", jsonData.get("responsible").asText());
            res.setField("event_time", jsonData.get("event_time").longValue());
            res.setField("is_first_judge", jsonData.get("is_first_judge").asInt());
            res.setField("operation_id", jsonData.get("operation_id").asText());
            res.setField("customer_name", jsonData.get("customer_name").asText());
            res.setField("scene_identify", jsonData.get("scene_identify").asText());
            res.setField("problem_summary", jsonData.get("problem_summary").asText());
            res.setField("service_channel", jsonData.get("service_channel").asText());
            res.setField("try_to_recovered", jsonData.get("try_to_recovered").asText());
            res.setField("duty_responsible", jsonData.get("duty_responsible").asText());
            res.setField("current_operator", jsonData.get("current_operator").asText());
            res.setField("create_time", new Timestamp(jsonData.get("create_time").longValue()));
            res.setField("trigger_time", new Timestamp(jsonData.get("trigger_time").longValue()));
            res.setField("reflection_type_classify", jsonData.get("reflection_type_classify").asText());
            res.setField("service_scene_level1_name", jsonData.get("service_scene_level1_name").asText());
            res.setField("service_scene_level2_name", jsonData.get("service_scene_level2_name").asText());
            res.setField("service_scene_level3_name", jsonData.get("service_scene_level3_name").asText());
            res.setField("service_scene_level4_name", jsonData.get("service_scene_level4_name").asText());
            res.setField("title", jsonData.get("title").asText().replaceAll("\u0000", ""));
            res.setField("question", jsonData.get("question").asText().replaceAll("\u0000", ""));
            collector.collect(res);
        } catch (Exception e) {
            logger.error("[AgentDataToRowProcess] error_data: " + s + ", error_msg: " + e);
        }
    }
}