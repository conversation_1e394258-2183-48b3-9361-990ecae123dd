package com.tencent.andata.smart.similar.model;


import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import java.util.StringJoiner;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtractFeaturesData {

    private int code;
    private Data data;
    private String msg;

    @JsonProperty("BaseRsp")
    private BaseRsp baseRsp;

    @JsonProperty("AIExtractPrompt")
    private String aiExtractPrompt;

    @JsonProperty("ExtractPrompt")
    private String extractPrompt;


    // Getters and Setters
    public String getAiExtractPrompt() {
        return aiExtractPrompt;
    }

    public void setAiExtractPrompt(String aiExtractPrompt) {
        this.aiExtractPrompt = aiExtractPrompt;
    }

    public BaseRsp getBaseRsp() {
        return baseRsp;
    }

    public void setBaseRsp(BaseRsp baseRsp) {
        this.baseRsp = baseRsp;
    }

    public String getExtractPrompt() {
        return extractPrompt;
    }

    public void setExtractPrompt(String extractPrompt) {
        this.extractPrompt = extractPrompt;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static class BaseRsp {

        @JsonProperty("Code")
        private int code;

        @JsonProperty("Msg")
        private String msg;

        @JsonProperty("RequestId")
        private String requestId;

        @Override
        public String toString() {
            return new StringJoiner(", ", BaseRsp.class.getSimpleName() + "[", "]")
                    .add("code=" + code)
                    .add("msg='" + msg + "'")
                    .add("requestId='" + requestId + "'")
                    .toString();
        }

        // Getters and Setters
        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }
    }

    public static class Data {

        private Answer answer;

        // Getters and Setters
        public Answer getAnswer() {
            return answer;
        }

        public void setAnswer(Answer answer) {
            this.answer = answer;
        }
    }

    public static class Answer {

        private String current_process;
        private String preparatory_question;
        private String question_description;
        private String question_summary;
        private String reason;
        private String reason_classification;
        private String solution;
        private String solution_classification;
        private String solution_process_detail;

        // Getters and Setters
        public String getCurrent_process() {
            return current_process;
        }

        public void setCurrent_process(String current_process) {
            this.current_process = current_process;
        }

        public String getPreparatory_question() {
            return preparatory_question;
        }

        public void setPreparatory_question(String preparatory_question) {
            this.preparatory_question = preparatory_question;
        }

        public String getQuestion_description() {
            return question_description;
        }

        public void setQuestion_description(String question_description) {
            this.question_description = question_description;
        }

        public String getQuestion_summary() {
            return question_summary;
        }

        public void setQuestion_summary(String question_summary) {
            this.question_summary = question_summary;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public String getReason_classification() {
            return reason_classification;
        }

        public void setReason_classification(String reason_classification) {
            this.reason_classification = reason_classification;
        }

        public String getSolution() {
            return solution;
        }

        public void setSolution(String solution) {
            this.solution = solution;
        }

        public String getSolution_classification() {
            return solution_classification;
        }

        public void setSolution_classification(String solution_classification) {
            this.solution_classification = solution_classification;
        }

        public String getSolution_process_detail() {
            return solution_process_detail;
        }

        public void setSolution_process_detail(String solution_process_detail) {
            this.solution_process_detail = solution_process_detail;
        }
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ExtractFeaturesData.class.getSimpleName() + "[", "]")
                .add("code=" + code)
                .add("data=" + data)
                .add("msg='" + msg + "'")
                .add("baseRsp=" + baseRsp)
                .add("aiExtractPrompt='" + aiExtractPrompt + "'")
                .add("extractPrompt='" + extractPrompt + "'")
                .toString();
    }
}