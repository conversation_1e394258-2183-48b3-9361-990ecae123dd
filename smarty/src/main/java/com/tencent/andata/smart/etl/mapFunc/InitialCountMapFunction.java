package com.tencent.andata.smart.etl.mapFunc;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.tuple.Tuple3;

/**
 * 初始化重试计数和时间戳的映射函数
 * 为新输入的策略数据添加重试计数(0)和时间戳(0)
 */
public class InitialCountMapFunction implements MapFunction<Strategy, Tuple3<Strategy, Integer, Long>> {

    /**
     * 将输入的策略转换为带有初始重试计数和时间戳的元组
     *
     * @param strategy 输入的策略对象
     * @return 包含策略、初始重试计数(0)和初始时间戳(0)的元组
     */
    @Override
    public Tuple3<Strategy, Integer, Long> map(Strategy strategy) {
        // 新输入的策略，重试计数为0，时间戳为0（表示可以立即处理）
        return new Tuple3<>(strategy, 0, 0L);
    }
}