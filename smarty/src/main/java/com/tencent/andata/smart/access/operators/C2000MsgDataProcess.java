package com.tencent.andata.smart.access.operators;

import static com.tencent.andata.utils.TimeUtil.getEpochMilli;

import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import com.tencent.andata.utils.HBaseSinkFunction.OperationType;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class C2000MsgDataProcess extends ProcessFunction<JsonNode, HbaseRow> {

    private static final Logger log = LoggerFactory.getLogger(C2000MsgDataProcess.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private OutputTag<JsonNode> c2000OutputTag;

    public C2000MsgDataProcess(OutputTag<JsonNode> c2000OutputTag) {
        this.c2000OutputTag = c2000OutputTag;
    }


    @Override
    public void processElement(JsonNode json, Context ctx, Collector<HbaseRow> out) throws JsonProcessingException {

        // c2000流水数据写入hbase
        String msgTime = json.get("msg_time").asText();
        String getDisplayContent = json.get("content").asText();
        String conversationId = json.get("conversation_id").asText();
        String rowKey = conversationId + "-" + getEpochMilli(msgTime);


        ((ObjectNode) json).put("source", "QYWX");
        ((ObjectNode) json).put("data_type", "c2000");
        ((ObjectNode) json).put("is_big_customer", 0);
        ((ObjectNode) json).put("display_content", getDisplayContent);

        // c2000消息数据写入主流
        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put("data", objectMapper.writeValueAsString(json));
        out.collect(new HbaseRow(OperationType.INSERT, rowKey, "cf", rootNode));

        // 发送数据到旁路输出
        ctx.output(c2000OutputTag, json);
    }
}