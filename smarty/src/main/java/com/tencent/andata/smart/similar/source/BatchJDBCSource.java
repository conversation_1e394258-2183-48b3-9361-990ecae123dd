package com.tencent.andata.smart.similar.source;

import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.common.state.CheckpointListener;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 简化的批量JDBC数据源
 * 每30秒读取100条数据，无需确认机制
 */
public class BatchJDBCSource extends RichSourceFunction<JsonNode>
        implements CheckpointedFunction, CheckpointListener {

    private static final Logger log = LoggerFactory.getLogger(BatchJDBCSource.class);

    // 配置参数
    private final String sql;
    private final DatabaseConf databaseConf;
    private final DatabaseEnum databaseEnum;
    private final int batchSize;
    private final long batchIntervalMs;

    // 运行时状态
    private volatile boolean isRunning = false;
    private transient Connection connection;
    private transient PreparedStatement preparedStatement;
    private transient ObjectMapper objectMapper;

    // 状态管理
    private transient ListState<Long> offsetState;
    private final AtomicLong currentOffset = new AtomicLong(0);
    private final AtomicLong processedRecords = new AtomicLong(0);
    private final AtomicBoolean hasMoreData = new AtomicBoolean(true);
    private long currentBatchId = 0;

    /**
     * 构造函数
     */
    public BatchJDBCSource(String sql,
            DatabaseConf databaseConf,
            DatabaseEnum databaseEnum,
            int batchSize,
            long batchIntervalMs) {
        this.sql = sql;
        this.databaseConf = databaseConf;
        this.databaseEnum = databaseEnum;
        this.batchSize = batchSize > 0 ? batchSize : 100;
        this.batchIntervalMs = batchIntervalMs > 0 ? batchIntervalMs : 60000; // 默认30秒
    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        this.objectMapper = new ObjectMapper();

        // 初始化数据库连接
        initializeConnection();

        log.info("[BatchJDBCSource] 初始化完成，批次大小: {}, 间隔: {}ms", batchSize, batchIntervalMs);
    }

    @Override
    public void run(SourceContext<JsonNode> ctx) throws Exception {
        isRunning = true;

        log.info("[BatchJDBCSource] 开始运行，从偏移量 {} 开始", currentOffset.get());

        while (isRunning && hasMoreData.get()) {
            try {
                // 查询一批数据
                List<JsonNode> batchData = queryBatch(currentOffset.get(), batchSize);

                if (batchData.isEmpty()) {
                    log.info("[BatchJDBCSource] 没有更多数据，停止读取");
                    hasMoreData.set(false);
                    break;
                }

                // 发送数据
                synchronized (ctx.getCheckpointLock()) {
                    for (JsonNode record : batchData) {
                        // 添加批次ID
                        if (record instanceof ObjectNode) {
                            ((ObjectNode) record).put("_batch_id", currentBatchId);
                        }
                        ctx.collect(record);
                    }

                    // 更新偏移量和统计
                    currentOffset.addAndGet(batchData.size());
                    processedRecords.addAndGet(batchData.size());
                    currentBatchId++;
                }

                log.info("[BatchJDBCSource] 批次 {} 完成，处理 {} 条记录，当前偏移量: {}",
                        currentBatchId - 1, batchData.size(), currentOffset.get());

                // 等待指定间隔
                if (isRunning) {
                    Thread.sleep(batchIntervalMs);
                }

            } catch (InterruptedException e) {
                log.info("[BatchJDBCSource] 接收到中断信号，停止运行");
                break;
            } catch (Exception e) {
                log.error("[BatchJDBCSource] 处理批次时发生错误", e);
                // 简单重试机制：等待一段时间后继续
                if (isRunning) {
                    Thread.sleep(5000);
                }
            }
        }

        log.info("[BatchJDBCSource] 运行结束，总共处理 {} 条记录", processedRecords.get());
    }

    @Override
    public void cancel() {
        log.info("[BatchJDBCSource] 取消运行");
        isRunning = false;
    }

    @Override
    public void close() throws Exception {
        log.info("[BatchJDBCSource] 关闭资源");

        if (preparedStatement != null) {
            try {
                preparedStatement.close();
            } catch (SQLException e) {
                log.warn("[BatchJDBCSource] 关闭PreparedStatement失败", e);
            }
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                log.warn("[BatchJDBCSource] 关闭数据库连接失败", e);
            }
        }

        super.close();
    }

    /**
     * 初始化数据库连接
     */
    private void initializeConnection() throws SQLException {
        String jdbcUrl = buildJdbcUrl();
        Properties props = new Properties();
        props.setProperty("user", databaseConf.userName);
        props.setProperty("password", databaseConf.password);

        // 设置连接属性
        props.setProperty("connectTimeout", "30000");
        props.setProperty("socketTimeout", "60000");
        props.setProperty("autoReconnect", "true");

        this.connection = DriverManager.getConnection(jdbcUrl, props);
        this.connection.setAutoCommit(true);

        // 准备SQL语句（添加LIMIT和OFFSET）
        String paginatedSql = addPaginationToSql(sql);
        this.preparedStatement = connection.prepareStatement(paginatedSql);

        log.info("[BatchJDBCSource] 数据库连接建立成功，URL={}", jdbcUrl);
    }

    /**
     * 构建JDBC URL
     */
    private String buildJdbcUrl() {
        switch (databaseEnum) {
            case MYSQL:
                return String.format(
                        "*******************************************************************************" +
                                "&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai",
                        databaseConf.dbHost,
                        databaseConf.dbPort,
                        databaseConf.dbName
                );
            case PGSQL:
                return String.format(
                        "**************************************************************************" +
                                "&serverTimezone=Asia/Shanghai&stringtype=unspecified",
                        databaseConf.dbHost,
                        databaseConf.dbPort,
                        databaseConf.dbName
                );
            default:
                throw new UnsupportedOperationException("不支持的数据库类型: " + databaseEnum);
        }
    }

    /**
     * 为SQL添加分页
     */
    private String addPaginationToSql(String originalSql) {
        // 移除末尾的分号
        String cleanSql = originalSql.trim();
        if (cleanSql.endsWith(";")) {
            cleanSql = cleanSql.substring(0, cleanSql.length() - 1);
        }

        // 根据数据库类型添加分页
        switch (databaseEnum) {
            case MYSQL:
                return cleanSql + " LIMIT ? OFFSET ?";
            case PGSQL:
                return cleanSql + " LIMIT ? OFFSET ?";
            default:
                throw new UnsupportedOperationException("不支持的数据库类型: " + databaseEnum);
        }
    }

    /**
     * 查询一批数据（带重试机制）
     */
    private List<JsonNode> queryBatch(long offset, int limit) throws Exception {
        Exception lastException = null;

        for (int retry = 0; retry <= 3; retry++) {
            try {
                return queryBatchInternal(offset, limit);
            } catch (Exception e) {
                lastException = e;
                log.warn("[BatchJDBCSource] 查询数据失败，重试 {}/{}: offset={}, limit={}, error={}",
                        retry + 1, 3 + 1, offset, limit, e.getMessage());

                if (retry < 3) {
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("查询被中断", ie);
                    }

                    // 重新初始化连接
                    try {
                        if (connection != null && !connection.isClosed()) {
                            connection.close();
                        }
                        initializeConnection();
                    } catch (Exception connEx) {
                        log.error("[BatchJDBCSource] 重新初始化连接失败: {}", connEx.getMessage());
                    }
                }
            }
        }

        throw new RuntimeException("查询数据失败，已达到最大重试次数: " + (3 + 1), lastException);
    }

    /**
     * 内部查询方法
     */
    private List<JsonNode> queryBatchInternal(long offset, int limit) throws SQLException {
        List<JsonNode> results = new ArrayList<>();

        // 设置分页参数
        preparedStatement.setInt(1, limit);
        preparedStatement.setLong(2, offset);

        long startTime = System.currentTimeMillis();

        try (ResultSet rs = preparedStatement.executeQuery()) {
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                ObjectNode record = objectMapper.createObjectNode();

                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);

                    if (value != null) {
                        // 根据数据类型进行转换
                        if (value instanceof String) {
                            record.put(columnName, (String) value);
                        } else if (value instanceof Number) {
                            if (value instanceof Long || value instanceof Integer) {
                                record.put(columnName, ((Number) value).longValue());
                            } else {
                                record.put(columnName, ((Number) value).doubleValue());
                            }
                        } else if (value instanceof Boolean) {
                            record.put(columnName, (Boolean) value);
                        } else if (value instanceof Timestamp) {
                            record.put(columnName, ((Timestamp) value).getTime());
                        } else {
                            record.put(columnName, value.toString());
                        }
                    }
                }

                results.add(record);
            }
        }

        long endTime = System.currentTimeMillis();
        log.debug("[BatchJDBCSource] 查询批次完成，offset={}, limit={}, 结果数={}, 耗时={}ms",
                offset, limit, results.size(), (endTime - startTime));

        return results;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        offsetState.clear();
        offsetState.add(currentOffset.get());

        log.debug("[BatchJDBCSource] 保存状态，偏移量: {}", currentOffset.get());
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        // 初始化偏移量状态
        offsetState = context.getOperatorStateStore().getListState(
                new ListStateDescriptor<>("offset", Long.class));

        // 恢复状态
        if (context.isRestored()) {
            for (Long offset : offsetState.get()) {
                currentOffset.set(offset);
                log.info("[BatchJDBCSource] 从检查点恢复，偏移量: {}", offset);
                break; // 只取第一个值
            }
        }
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws Exception {
        log.debug("[BatchJDBCSource] 检查点 {} 完成", checkpointId);
    }

}