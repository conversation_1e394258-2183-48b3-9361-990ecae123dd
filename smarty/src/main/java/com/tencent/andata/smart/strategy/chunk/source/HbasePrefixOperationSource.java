package com.tencent.andata.smart.strategy.chunk.source;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import static com.tencent.andata.utils.ExceptionWrapperUtil.function;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.model.HbaseTable;
import com.tencent.andata.smart.enums.Scene;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.AsyncTable;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.client.ScanResultConsumer;
import org.apache.hadoop.hbase.filter.PrefixFilter;

@Slf4j
public class HbasePrefixOperationSource implements ConversationSource {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper MAPPER = new ObjectMapper();

    private final AsyncConnection asyncConnection;
    private final Map<Scene, HbaseTable> sceneTableMap;
    private final ExecutorService executorService;

    public HbasePrefixOperationSource(
            AsyncConnection asyncConnection,
            Map<Scene, HbaseTable> sceneTableMap,
            ExecutorService executorService) {
        this.asyncConnection = asyncConnection;
        this.sceneTableMap = sceneTableMap;
        this.executorService = executorService;
    }

    @Override
    public CompletableFuture<String> getOperation(Strategy strategy) {
        HbaseTable table = sceneTableMap.get(strategy.scene);
        if (table == null) {
            logger.error("[HbasePrefixOperationSource] No table configuration for scene: " + strategy.scene);
        }

        assert table != null;
        return getOperationsFromHbase(buildPrefixScan(strategy, table), table)
                .thenApply(function(MAPPER::writeValueAsString));
    }

    private Scan buildPrefixScan(Strategy strategy, HbaseTable table) {
        Scan scan = new Scan().setCaching(1000).setBatch(1000);
        scan.withStartRow(strategy.sceneIdentify.getBytes());
        scan.setFilter(new PrefixFilter(strategy.sceneIdentify.getBytes()));
        scan.addColumn(table.getColumnFamily().getBytes(), table.getQualifier().getBytes());
        return scan;
    }

    private CompletableFuture<List<JsonNode>> getOperationsFromHbase(Scan scan, HbaseTable table) {
        AsyncTable<ScanResultConsumer> asyncTable = asyncConnection.getTable(
                TableName.valueOf(table.getTableName()),
                executorService
        );

        return asyncTable.scanAll(scan).thenApply(results -> {
            List<JsonNode> jsonNodes = new ArrayList<>();
            results.forEach(r -> Arrays.stream(r.rawCells())
                    .forEach(consumer(c -> jsonNodes.add(MAPPER.readTree(CellUtil.cloneValue(c))))));
            return jsonNodes;
        });
    }
}