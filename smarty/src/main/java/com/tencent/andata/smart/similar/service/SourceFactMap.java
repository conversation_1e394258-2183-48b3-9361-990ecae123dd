package com.tencent.andata.smart.similar.service;

import static com.tencent.andata.smart.similar.util.JsonMergeUtils.formatLargeNumber;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SourceFactMap implements MapFunction<JsonNode, JsonNode> {

    private transient ObjectMapper mapper;
    private Logger log = LoggerFactory.getLogger(SourceFactMap.class);

    @Override
    public JsonNode map(JsonNode row) throws Exception {
        // 添加初始化检查
        if (mapper == null) {
            mapper = new ObjectMapper();
        }
        // 添加null检查，防止NullPointerException
        if (row == null) {
            log.warn("Received null row in map function, creating empty node");
            return mapper.createObjectNode();
        }

        ObjectNode node = mapper.createObjectNode();
        String ticketId = formatLargeNumber(row.get("ticket_id"));
        String uin = formatLargeNumber(row.get("uin"));
        node.put("ticket_id", ticketId);
        node.put("service_scene", uin);
        // 解析ISO格式的时间字符串为时间戳
        String createTimeStr = row.get("create_time").asText();
        long timestamp;
        try {
            // 处理ISO格式时间字符串，如 "2025-07-04T15:13:51" 或 "2025-08-12T08:47:25Z"
            LocalDateTime dateTime;
            if (createTimeStr.endsWith("Z")) {
                // 处理带Z后缀的UTC时间格式
                dateTime = LocalDateTime.parse(
                        createTimeStr.substring(0, createTimeStr.length() - 1),
                        DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } else {
                // 处理标准ISO本地时间格式
                dateTime = LocalDateTime.parse(createTimeStr,
                        DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            timestamp = dateTime.toEpochSecond(ZoneOffset.UTC) * 1000; // 转换为毫秒时间戳
        } catch (Exception e) {
            // 如果解析失败，尝试直接解析为Long（兼容旧格式）
            try {
                timestamp = Long.valueOf(createTimeStr);
            } catch (NumberFormatException nfe) {
                // 如果都失败，使用当前时间戳
                timestamp = System.currentTimeMillis();
                log.warn("Failed to parse create_time: {}, using current timestamp",
                        createTimeStr);
            }
        }
        node.put("ticket_create_time", timestamp < 0 ? System.currentTimeMillis() : timestamp);
        node.put("uin", row.get("uin").asText());
        Long serviceSceneChecked = Long.valueOf(row.get("service_scene_checked").asText());
        Long serviceScene = Long.valueOf(row.get("service_scene").asText());
        long useScene = (serviceSceneChecked != null && serviceSceneChecked > 0) ?
                serviceSceneChecked : (serviceScene != null ? serviceScene : -1);
        node.put("service_scene", useScene);
        node.put("service_scene_checked", useScene);
        return node;
    }
}