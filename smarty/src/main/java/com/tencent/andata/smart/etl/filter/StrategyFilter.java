package com.tencent.andata.smart.etl.filter;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FilterFunction;


/**
 * 过滤掉上下文无效的策略
 */
public class StrategyFilter implements FilterFunction<Strategy> {

    private static FlinkLog logger = FlinkLog.getInstance();

    @Override
    public boolean filter(Strategy strategy) throws Exception {
        return !isInvalidContent(strategy);
    }

    /**
     * 判断是否无效的上下文内容
     * 1. 内容为空或null
     * 2. 内容为"纯电销数据"
     *
     * @param strategy 待检查的策略对象
     * @return 如果内容无效返回true，否则返回false
     */
    private boolean isInvalidContent(Strategy strategy) {
        String conversation = strategy.chunk.conversation;
        if (isNullContent(conversation)) {
            logger.error("[StrategyFilter]无效的上下文内容, 上下文为空, sceneId: " + strategy.sceneIdentify);
            return true;
        }

        if ("纯电销数据".equals(conversation)) {
            logger.error("[StrategyFilter]纯电销数据, sceneId:" + strategy.sceneIdentify);
            return true;
        }

        if ("群消息未引用建单标题".equals(conversation)) {
            logger.error("[StrategyFilter]群消息未引用建单标题, sceneId:" + strategy.sceneIdentify);
            return true;
        }

        return false;
    }

    /**
     * 判断字符串是否为null或空值
     * 1. 检查是否为"null"(不区分大小写)
     * 2. 检查是否为空字符串(使用StringUtils.isEmpty)
     *
     * @param content 待检查的字符串
     * @return 如果为null或空值返回true，否则返回false
     */
    private static boolean isNullContent(String content) {
        return StringUtils.isEmpty(content)
                || "null".equalsIgnoreCase(content);
    }
}