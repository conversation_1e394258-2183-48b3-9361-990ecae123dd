package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class StrategyTriggerProcess extends ProcessFunction<Strategy, Strategy> {

    private static final FlinkLog logger = FlinkLog.getInstance();

    @Override
    public void processElement(Strategy strategy, Context context, Collector<Strategy> collector) throws Exception {
        // Trigger数据是否需要留存
        if (strategy.trigger.needPersist) {
            strategy.trigger.triggerTimestamp = System.currentTimeMillis();
            // TODO 后续再考虑Trigger数据单独下发
        }
        switch (strategy.trigger.type) {
            case Immediately:
                collector.collect(strategy);
                break;
            case Delay:
                // TODO 暂时不支持延迟触发
                throw new Exception("Delay trigger not supported");
        }
    }
}