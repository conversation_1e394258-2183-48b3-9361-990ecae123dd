package com.tencent.andata.smart.etl.process.example;

import com.tencent.andata.smart.etl.process.GenericSlidingWindowRateLimitingProcess;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 泛型滑动窗口限流器使用示例
 * 展示如何在不同数据类型上使用泛型限流器
 */
public class GenericRateLimiterUsageExample {

    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 示例1：Strategy类型限流（QPM=30）
        strategyRateLimitingExample(env);

        // 示例2：String类型限流（QPM=60）
        stringRateLimitingExample(env);

        // 示例3：自定义对象类型限流（QPM=120）
        customObjectRateLimitingExample(env);

        // 示例4：Integer类型限流（QPM=6）
        integerRateLimitingExample(env);

        env.execute("Generic Rate Limiter Usage Example");
    }

    /**
     * 示例1：Strategy类型限流
     * QPM=30：每分钟最多30个Strategy事件
     */
    private static void strategyRateLimitingExample(StreamExecutionEnvironment env) {
        // 创建Strategy数据流
        DataStream<Strategy> strategyStream = env.fromElements(
                createStrategy("strategy-1"),
                createStrategy("strategy-2"),
                createStrategy("strategy-3")
        );

        // 使用泛型限流器处理Strategy类型
        SingleOutputStreamOperator<Strategy> rateLimitedStrategyStream = strategyStream
                .keyBy(strategy -> strategy.sceneIdentify)
                .process(new GenericSlidingWindowRateLimitingProcess<>(
                        5,      // 10秒内最多5个事件
                        10000,  // 10秒窗口
                        2000,   // 每2秒检查
                        1,      // 每次输出1个缓存事件
                        Strategy.class // 指定类型
                ))
                .returns(new TypeHint<Strategy>() {})
                .name("Strategy_Rate_Limiting_QPM_30")
                .uid("Strategy_Rate_Limiting_QPM_30");

        rateLimitedStrategyStream.print("Strategy限流结果");
    }

    /**
     * 示例2：String类型限流
     * QPM=60：每分钟最多60个String事件
     */
    private static void stringRateLimitingExample(StreamExecutionEnvironment env) {
        // 创建String数据流
        DataStream<String> stringStream = env.fromElements(
                "message-1", "message-2", "message-3", "message-4", "message-5"
        );

        // 使用泛型限流器处理String类型
        SingleOutputStreamOperator<String> rateLimitedStringStream = stringStream
                .keyBy(str -> "string-key") // 所有String使用同一个key
                .process(new GenericSlidingWindowRateLimitingProcess<>(
                        10,     // 10秒内最多10个事件
                        10000,  // 10秒窗口
                        1000,   // 每1秒检查
                        2,      // 每次输出2个缓存事件
                        String.class // 指定类型
                ))
                .returns(new TypeHint<String>() {})
                .name("String_Rate_Limiting_QPM_60")
                .uid("String_Rate_Limiting_QPM_60");

        rateLimitedStringStream.print("String限流结果");
    }

    /**
     * 示例3：自定义对象类型限流
     * QPM=120：每分钟最多120个UserEvent事件
     */
    private static void customObjectRateLimitingExample(StreamExecutionEnvironment env) {
        // 创建UserEvent数据流
        DataStream<UserEvent> userEventStream = env.fromElements(
                new UserEvent("user1", "login"),
                new UserEvent("user2", "logout"),
                new UserEvent("user1", "click"),
                new UserEvent("user3", "purchase")
        );

        // 使用泛型限流器处理UserEvent类型
        SingleOutputStreamOperator<UserEvent> rateLimitedUserEventStream = userEventStream
                .keyBy(event -> event.getUserId()) // 按用户ID分组
                .process(new GenericSlidingWindowRateLimitingProcess<>(
                        20,     // 10秒内最多20个事件
                        10000,  // 10秒窗口
                        1000,   // 每1秒检查
                        3,      // 每次输出3个缓存事件
                        UserEvent.class // 指定类型
                ))
                .returns(new TypeHint<UserEvent>() {})
                .name("UserEvent_Rate_Limiting_QPM_120")
                .uid("UserEvent_Rate_Limiting_QPM_120");

        rateLimitedUserEventStream.print("UserEvent限流结果");
    }

    /**
     * 示例4：Integer类型限流
     * QPM=6：每分钟最多6个Integer事件（低频限流）
     */
    private static void integerRateLimitingExample(StreamExecutionEnvironment env) {
        // 创建Integer数据流
        DataStream<Integer> integerStream = env.fromElements(
                100, 200, 300, 400, 500, 600, 700, 800
        );

        // 使用泛型限流器处理Integer类型
        SingleOutputStreamOperator<Integer> rateLimitedIntegerStream = integerStream
                .keyBy(num -> "int-key") // 所有Integer使用同一个key
                .process(new GenericSlidingWindowRateLimitingProcess<>(
                        1,      // 10秒内最多1个事件
                        10000,  // 10秒窗口
                        2000,   // 每2秒检查
                        1,      // 每次输出1个缓存事件
                        Integer.class // 指定类型
                ))
                .returns(new TypeHint<Integer>() {})
                .name("Integer_Rate_Limiting_QPM_6")
                .uid("Integer_Rate_Limiting_QPM_6");

        rateLimitedIntegerStream.print("Integer限流结果");
    }

    /**
     * 创建测试用的Strategy对象
     */
    private static Strategy createStrategy(String id) {
        Strategy strategy = new Strategy();
        strategy.sceneIdentify = id;
        return strategy;
    }

    /**
     * 自定义用户事件类
     */
    public static class UserEvent {

        private String userId;
        private String action;

        public UserEvent() {} // Jackson需要无参构造函数

        public UserEvent(String userId, String action) {
            this.userId = userId;
            this.action = action;
        }

        public String getUserId() {return userId;}

        public void setUserId(String userId) {this.userId = userId;}

        public String getAction() {return action;}

        public void setAction(String action) {this.action = action;}

        @Override
        public String toString() {
            return "UserEvent{userId='" + userId + "', action='" + action + "'}";
        }
    }

    /**
     * 泛型限流器配置指南
     * <p>
     * QPM计算公式：rateLimit * (60000 / windowSize) = 目标QPM
     * <p>
     * 常用配置：
     * - QPM=30:  rateLimit=5,  windowSize=10000, slideStep=2000, batchSize=1
     * - QPM=60:  rateLimit=10, windowSize=10000, slideStep=1000, batchSize=2
     * - QPM=120: rateLimit=20, windowSize=10000, slideStep=1000, batchSize=3
     * - QPM=300: rateLimit=50, windowSize=10000, slideStep=500,  batchSize=5
     * <p>
     * 参数说明：
     * - rateLimit: 窗口时间内允许的最大事件数
     * - windowSize: 窗口大小（毫秒）
     * - slideStep: 滑动步长，定时器触发间隔（毫秒）
     * - batchSize: 每次定时器触发时输出的最大缓存事件数
     * - typeClass: 处理的数据类型Class，用于序列化/反序列化
     * <p>
     * 使用建议：
     * 1. slideStep越小，响应越快，但CPU开销越大
     * 2. batchSize应该小于rateLimit的20%
     * 3. 对于高频数据，建议使用较大的windowSize和较小的slideStep
     * 4. 对于低频数据，可以使用较小的windowSize和较大的slideStep
     */
}