package com.tencent.andata.smart.etl.process;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.etl.domain.RiskResult;
import com.tencent.andata.smart.etl.handler.SceneHandler;
import com.tencent.andata.smart.etl.manager.RetryManager;
import com.tencent.andata.smart.etl.manager.SceneHandlerManager;
import com.tencent.andata.smart.etl.repository.imp.JdbcCustomerRepository;
import com.tencent.andata.smart.etl.repository.imp.JdbcDutyRepository;
import com.tencent.andata.smart.etl.repository.imp.JdbcTicketRepository;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.etl.validator.WebIMDataValidator;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.utils.CompanyInfoUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import io.vavr.Tuple4;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

/**
 * 舆情结果处理函数
 * 处理不同场景下的策略数据，提取风险结果
 * 使用定时器实现WebIM场景的重试机制
 */
@RequiredArgsConstructor
public class PublicOpinionResultProcess extends KeyedProcessFunction<String, Strategy, Tuple2<String, String>> {

    private static final long serialVersionUID = 1L;
    private static final FlinkLog logger = FlinkLog.getInstance();
    private static Map<Integer, String> priorityMap = HashMap.of(
            -1, "L1",
            0, "L2",
            1, "L3",
            2, "L4",
            3, "L5");

    // 标记为transient的组件，避免序列化
    private transient SceneHandlerManager sceneHandlerManager;
    private transient WebIMDataValidator webIMDataValidator;

    // 这些仍然是必须的原始依赖
    private final CustomerRepository customerRepository;
    private final TicketRepository ticketRepository;
    private final DutyRepository dutyRepository;
    private transient RetryManager retryManager;
    private final ObjectMapper objectMapper;


    /**
     * 使用RainbowUtils创建PublicOpinionResultProcess的工厂方法
     */
    public static PublicOpinionResultProcess create(RainbowUtils rainbowUtils) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 初始化数据库配置
        DatabaseConf dataWareConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName("cdc.database.pgsql.dataware_r")
                .build();

        // 初始化Repository
        CustomerRepository customerRepository = new JdbcCustomerRepository(dataWareConf);
        TicketRepository ticketRepository = new JdbcTicketRepository(dataWareConf);
        DutyRepository dutyRepository = new JdbcDutyRepository(dataWareConf);

        return new PublicOpinionResultProcess(
                customerRepository,
                ticketRepository,
                dutyRepository,
                objectMapper
        );
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化transient组件
        this.sceneHandlerManager = new SceneHandlerManager(customerRepository, ticketRepository, dutyRepository);
        this.webIMDataValidator = new WebIMDataValidator();
        this.retryManager = new RetryManager();

        // 初始化状态
        retryManager.initState(getRuntimeContext());

        // 初始化Repository
        ((JdbcCustomerRepository) customerRepository).open();
        ((JdbcTicketRepository) ticketRepository).open();
        ((JdbcDutyRepository) dutyRepository).open();
    }

    @Override
    public void close() throws Exception {
        ((JdbcCustomerRepository) customerRepository).close();
        ((JdbcTicketRepository) ticketRepository).close();
        ((JdbcDutyRepository) dutyRepository).close();
        super.close();
    }

    @Override
    public void processElement(Strategy strategy, Context context, Collector<Tuple2<String, String>> collector) throws Exception {
        String key = context.getCurrentKey();
        Option<RiskResult> riskResultOption = processStrategy(strategy);

        if (riskResultOption.isDefined() && webIMDataValidator.isDataIncomplete(riskResultOption.get())) {
            // 数据不完整，交给重试管理器处理
            retryManager.scheduleRetry(key, strategy, context);
            // 不完整数据也下发，以便于入pg库
            riskResultOption.map(this::convertToTuple).peek(collector::collect);
            return;
        }

        // 数据完整，正常处理
        riskResultOption.map(this::convertToTuple).peek(collector::collect);
        // 清理状态
        retryManager.cleanupState(key);
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Tuple2<String, String>> out) throws Exception {
        String key = ctx.getCurrentKey();

        // 委托重试管理器处理定时器事件
        retryManager.handleTimer(key, ctx, (strategy) -> {
            Option<RiskResult> riskResultOption = processStrategy(strategy);

            // 检查数据是否完整
            if (riskResultOption.isDefined() && webIMDataValidator.isDataIncomplete(riskResultOption.get())) {
                // 数据仍不完整，继续重试
                riskResultOption.map(this::convertToTuple).peek(out::collect);
                return false;
            }

            // 数据已完整，处理并输出
            logger.info(String.format("[PublicOpinionResultProcess] WebIM strategy processed successfully after retry, key: %s", key));
            riskResultOption.map(this::convertToTuple).peek(out::collect);
            return true;
        });
    }

    /**
     * 处理策略数据，返回风险结果
     */
    private Option<RiskResult> processStrategy(Strategy strategy) {
        SceneHandler handler = sceneHandlerManager.getHandler(strategy.scene);
        return Option.of(handler).flatMap(h -> buildRiskResult(strategy, h));
    }

    /**
     * 从风险结果构建元组
     */
    private Tuple2<String, String> convertToTuple(RiskResult result) {
        return Try.of(() ->
                new Tuple2<>(getKeyByScene(result), objectMapper.writeValueAsString(result))
        ).getOrElse(new Tuple2<>("", ""));
    }

    /**
     * 根据场景获取键值
     */
    private String getKeyByScene(RiskResult result) {
        String scene = result.getScene();
        Scene enumScene = Scene.valueOf(scene);
        switch (enumScene) {
            case Ticket:
            case WebIM:
            case C2000:
                return result.getTicketId() + "_" + result.getPost();
            case Group:
                return result.getSceneIdentify();
            default:
                return "";
        }
    }

    /**
     * 构建风险结果对象
     */
    public Option<RiskResult> buildRiskResult(Strategy strategy, SceneHandler handler) {
        return handler.getTicketId(strategy)
                .flatMap(ticketId ->
                        Try.of(() -> {
                                    // 解析风险信息
                                    Option<Tuple2<String, String>> riskInfo = parseRiskInfo(strategy.analyzes[0].res);
                                    Tuple4<String, String, String, String> serviceScenees = handler.getServiceScenesName(strategy).get();
                                    return RiskResult
                                            .builder()
                                            .currentOperatorCompanyId(Try.of(() -> Integer.valueOf(getFieldText(handler, strategy, "current_operator_company_id"))).getOrNull())
                                            .priority(Try.of(() -> priorityMap.get(Integer.valueOf(getFieldText(handler, strategy, "priority"))).get()).getOrElse(""))
                                            .companyId(CompanyInfoUtils.getCompanyName(getFieldText(handler, strategy, "company_id")))
                                            .idr1(Try.of(() -> handler.getCurrentStaffIdr1(strategy).getOrElse("")).getOrElse(""))
                                            .serviceProviders(getFieldText(handler, strategy, "service_providers"))
                                            .customerName(handler.getCurrentCustomerName(strategy).getOrElse(""))
                                            .riskReason(riskInfo.map(u -> u.f0).getOrElse(""))
                                            .riskLevel(riskInfo.map(u -> u.f1).getOrElse(""))
                                            .dutyResponsible(handler.getDutyResponsible(strategy).getOrElse(""))
                                            .factAssign(getFieldText(handler, strategy, "fact_assign_duty_name"))
                                            .currentOperator(getFieldText(handler, strategy, "current_operator"))
                                            .aftersalesType(getFieldText(handler, strategy, "aftersales_type"))
                                            .triggerContent(handler.getTriggerContent(strategy).getOrElse(""))
                                            .serviceChannel(handler.getServiceChannel(strategy).getOrElse(""))
                                            .riskOperationId(handler.getOperationId(strategy).getOrElse(""))
                                            .salesChannel(getFieldText(handler, strategy, "sales_channel"))
                                            .triggerRiskType(handler.getRiskType(strategy).getOrElse(""))
                                            .salesBackup(getFieldText(handler, strategy, "sales_backup"))
                                            .responsible(getFieldText(handler, strategy, "responsible"))
                                            .uin(Long.valueOf(getFieldText(handler, strategy, "uin")))
                                            .eventTime(handler.getEventTime(strategy).getOrElse(0L))
                                            .post(handler.getCurrentPost(strategy).getOrElse(""))
                                            .question(getFieldText(handler, strategy, "question"))
                                            .title(getFieldText(handler, strategy, "title"))
                                            .url(getFieldText(handler, strategy, "url"))
                                            .msgSeq(handler.getMsgSeq(strategy).getOrNull())
                                            .triggerTime(strategy.trigger.triggerTimestamp)
                                            .serviceSceneLevel1Name(serviceScenees._1)
                                            .serviceSceneLevel2Name(serviceScenees._2)
                                            .serviceSceneLevel3Name(serviceScenees._3)
                                            .serviceSceneLevel4Name(serviceScenees._4)
                                            .sceneIdentify(strategy.sceneIdentify)
                                            .scene(strategy.scene.name())
                                            .pk(buildPk(strategy))
                                            .ticketId(ticketId)
                                            .build();
                                }).onFailure(e -> logger.error(String.format("[PublicOpinionResultProcess] strategy: %s Failed to build risk result, " +
                                        "errMsg: %s", Try.of(() -> objectMapper.writeValueAsString(strategy)).getOrElse("Unable to serialize strategy"), System.err)))
                                .toOption()
                );
    }

    private String buildPk(Strategy strategy) {
        return strategy.scene == Scene.Group ?
                String.format("%s@%s", strategy.sceneIdentify, getOperationIds(strategy)) :
                String.format("%s-%s", strategy.sceneIdentify, getOperationIds(strategy));
    }

    private String getFieldText(SceneHandler handler, Strategy strategy, String field) {
        Option<JsonNode> fieldData = handler.getTriggerDataByField(strategy, field);
        return fieldData.isDefined() && fieldData.get() != null ? fieldData.get().asText() : "";
    }

    private String getOperationIds(Strategy strategy) {
        return Arrays.stream(strategy.trigger.data)
                .map(data -> getOperationId(strategy.scene, data))
                .filter(Option::isDefined)
                .map(Option::get)
                .collect(Collectors.joining("-"));
    }

    private Option<Tuple2<String, String>> parseRiskInfo(String res) {
        return Try.of(() -> {
            JsonNode node = objectMapper.readTree(res);
            return new Tuple2<>(node.get("reason").asText(), node.get("risk_level").asText());
        }).toOption();
    }

    private Option<String> getOperationId(Scene scene, JsonNode data) {
        return Try.of(() -> {
            switch (scene) {
                case Ticket:
                    return String.valueOf(data.get("operation_id").asLong());
                case WebIM:
                    return data.get("value_of_primary_key").asText();
                case Group:
                case C2000:
                    return data.get("msg_id").asText();
                default:
                    return "";
            }
        }).filter(id -> !id.isEmpty()).toOption();
    }
}