package com.tencent.andata.smart;

import com.tencent.andata.smart.etl.PublicOpinionProcessETL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.java.utils.ParameterTool;

public class PublicOpinionApplication {
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        final PublicOpinionProcessETL etl = PublicOpinionProcessETL.builder()
                .rainbowUtils(rainbowUtils)
                .build();
        etl.run(flinkEnv);
        flinkEnv.env().execute("Public Opinion result process");
    }
}