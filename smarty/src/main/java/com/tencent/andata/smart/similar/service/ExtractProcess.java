package com.tencent.andata.smart.similar.service;

import static com.tencent.andata.smart.similar.util.JsonMergeUtils.addStringField;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.getTextValue;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.similar.config.ApplicationConfig;
import com.tencent.andata.smart.similar.model.ExtractFeaturesData;
import com.tencent.andata.smart.similar.model.ErrorTicket;
import com.tencent.andata.smart.similar.util.AndataPushClient;
import com.tencent.andata.smart.similar.util.ErrorTicketSink;
import com.tencent.andata.smart.similar.util.JsonMergeUtils;
import com.tencent.andata.utils.AsyncHttpClientUtils;
import com.tencent.andata.utils.polaris.PolarisUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class ExtractProcess extends ProcessFunction<JsonNode, JsonNode> {

    private transient FlinkLog zhiyanLogger = FlinkLog.getInstance();
    private transient AsyncHttpClientUtils asyncHttpClientUtils;
    private transient ScheduledExecutorService retryExecutor;
    private transient AndataPushClient andataPushClient = new AndataPushClient();
    private transient ErrorTicketSink errorTicketSink;  // 🔧 改为transient，在open中初始化
    private String chatId;
    private ObjectMapper mapper;
    // 🔧 新增：数据库配置信息（可序列化）
    private final String jdbcUrl;
    private final String dbUsername;
    private final String dbPassword;
    private final int batchSize;

    // 重试配置常量
    private static final int MAX_RETRY_ATTEMPTS = 20;
    private static final long RETRY_DELAY_MINUTES = 10;

    // HTTP连接失败计数器
    private final AtomicLong httpConnectionFailures = new AtomicLong(0);
    private final AtomicLong httpRetryCount = new AtomicLong(0);

    // 🔧 修改：构造函数接收外部传入的ErrorTicketSink
    public ExtractProcess(ObjectMapper mapper, String chatId, ApplicationConfig config) {
        this.mapper = mapper;
        this.chatId = chatId;

        // 🔧 从配置中提取数据库连接信息
        DatabaseConf errorDbConf = config.getAigcDbConf(); // 或者专门的错误数据库配置
        this.jdbcUrl = String.format("jdbc:postgresql://%s:%d/%s",
                errorDbConf.getDbHost(), errorDbConf.getDbPort(), errorDbConf.getDbName());
        this.dbUsername = errorDbConf.getUserName();
        this.dbPassword = errorDbConf.getPassword();
        this.batchSize = 3;  // 或者从配置中获取
    }

    @Override
    public void close() throws Exception {
        zhiyanLogger.info("[ExtractProcess] 开始关闭流程...");

        try {
            // 🔧 1. 先停止重试执行器，避免新的任务提交
            if (retryExecutor != null) {
                zhiyanLogger.info("[ExtractProcess] 正在关闭重试执行器...");
                retryExecutor.shutdown();
                if (!retryExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    zhiyanLogger.warn("[ExtractProcess] 重试执行器未在30秒内关闭，强制关闭");
                    retryExecutor.shutdownNow();
                }
            }

            // 🔧 2. 等待一段时间让正在进行的HTTP请求完成
            zhiyanLogger.info("[ExtractProcess] 等待HTTP请求完成...");
            Thread.sleep(3000);

            // 🔧 3. 强制刷新错误票据缓冲区（但不关闭，因为是共享的）
            if (errorTicketSink != null && errorTicketSink.isHealthy()) {
                zhiyanLogger.info("[ExtractProcess] 强制刷新错误票据缓冲区");
                zhiyanLogger.info(String.format("[ExtractProcess] ErrorTicketSink当前统计: %s",
                        errorTicketSink.getStatistics()));

                errorTicketSink.forceFlush();

                // 🔧 注意：不调用shutdown()，因为ErrorTicketSink是全局共享的
                // errorTicketSink.shutdown();  // 不要调用这个
            }

            // 4. 关闭HTTP客户端
            if (asyncHttpClientUtils != null) {
                zhiyanLogger.info("[ExtractProcess] 正在关闭HTTP客户端...");
                asyncHttpClientUtils.close();
            }

            super.close();

            zhiyanLogger.info(String.format("[ExtractProcess] 关闭完成，最终统计: HTTP连接失败次数=%d, 重试总次数=%d",
                    httpConnectionFailures.get(), httpRetryCount.get()));

        } catch (Exception e) {
            zhiyanLogger.error("[ExtractProcess] 关闭过程中发生异常: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        this.zhiyanLogger = FlinkLog.getInstance();
        this.andataPushClient = new AndataPushClient();
        this.asyncHttpClientUtils = new AsyncHttpClientUtils(30000, 30000);
        retryExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "ExtractProcess-Retry-Worker");
            t.setDaemon(false);
            return t;
        });

        // 🔧 在open中创建ErrorTicketSink，避免序列化问题
        this.errorTicketSink = new ErrorTicketSink(jdbcUrl, dbUsername, dbPassword, batchSize);

        zhiyanLogger.info("[ExtractProcess] 初始化完成，ErrorTicketSink已创建");
    }

    @Override
    public void processElement(JsonNode jsonNode, ProcessFunction<JsonNode, JsonNode>.Context context,
            Collector<JsonNode> collector) throws Exception {
        try {

            // 空值检查
            if (jsonNode == null) {
                zhiyanLogger.warn("输入的JsonNode为null，跳过处理");
                return;
            }

            // 从JsonNode中提取ticket_id
            String ticketId = getTextValue(jsonNode, "ticket_id", "");
            if (ticketId.trim().isEmpty()) {
                zhiyanLogger.warn("ticket_id为空，跳过处理");
                return;
            }

            Map<String, Object> body = new HashMap<>();
            body.put("question", ticketId);
            Map<String, Object> extraParams = new HashMap<>();
            extraParams.put("extract_text", 1);
            body.put("extra_params", extraParams);

            String requestId = UUID.randomUUID().toString();
            Map<String, String> headers = new HashMap<>();
            headers.put("request-id", requestId);

            String url = PolarisUtils.getOneInstance("Pre-release", "trpc.aigc.agent.AgentServiceHTTP");
            String ExtractFeaturesUri = "/api/v1/agent/ExtractFeatures";
            String FeaturesUrl = url + ExtractFeaturesUri;

            // 使用带重试机制的异步HTTP客户端进行特征抽取调用
            performHttpRequestWithRetry(FeaturesUrl, body, headers, ticketId, jsonNode, collector, requestId, 0);

            // 异步调用后立即返回，不阻塞当前线程
            return;
        } catch (Exception e) {
            zhiyanLogger.warn("特征提取失败: " + e.getMessage());
            // 同步异常时也要传递 requestId（如果已生成）
            try {
                String fallbackRequestId = UUID.randomUUID().toString();
                JsonNode jsonNodeWithRequestId = addStringField(jsonNode, "request-id", fallbackRequestId,
                        mapper);
                collector.collect(jsonNodeWithRequestId);
            } catch (Exception collectorException) {
                zhiyanLogger.error("在同步异常处理中输出数据失败: " + e.getMessage() + " "
                        + collectorException.getMessage());
            }
        }
    }

    /**
     * 带重试机制的HTTP请求方法
     */
    private void performHttpRequestWithRetry(String url, Map<String, Object> body, Map<String, String> headers,
            String ticketId, JsonNode jsonNode, Collector<JsonNode> collector, String requestId, int attemptCount) {

        try {
            ((AsyncHttpClientUtils) asyncHttpClientUtils).doHttpPost(url, body, headers)
                    .thenAccept(responseStr -> {
                        try {
                            // 空值检查
                            if (responseStr == null || responseStr.trim().isEmpty() || mapper == null) {
                                return;
                            }

                            // 解析响应为ExtractFeaturesData对象
                            ExtractFeaturesData extractFeaturesData = mapper.readValue(responseStr, ExtractFeaturesData.class);

                            // 检查BaseRsp中的错误码
                            if ((extractFeaturesData.getBaseRsp() != null
                                    && extractFeaturesData.getBaseRsp().getCode() == 999)
                                    || extractFeaturesData.getData() == null
                                    || extractFeaturesData.getData().getAnswer() == null) {

                                // 如果还有重试次数，则进行重试
                                if (attemptCount < MAX_RETRY_ATTEMPTS - 1) {
                                    zhiyanLogger.warn(String.format("%s 特征抽取接口返回999错误，第%d次重试，将在%d分钟后重试", ticketId, attemptCount + 1, RETRY_DELAY_MINUTES));

                                    // 延迟后重试
                                    if (extractFeaturesData.getBaseRsp().getMsg().trim()
                                            .equals("exceeded the call limit, please try again later")) {
                                        retryExecutor.schedule(() -> {
                                            performHttpRequestWithRetry(url, body, headers, ticketId, jsonNode,
                                                    collector, requestId, attemptCount + 1);
                                        }, RETRY_DELAY_MINUTES, TimeUnit.MINUTES);
                                    } else {
                                        // 发送告警消息
                                        // 所有重试都失败，执行告警操作和错误记录
                                        String baseMsg = extractFeaturesData.getBaseRsp().getMsg();
                                        String trimmedMsg = (baseMsg != null) ? baseMsg.trim() : "";
                                        Integer statusCode = extractFeaturesData.getBaseRsp().getCode();

                                        String alertMsg = ticketId + " 特征抽取接口返回错误,请确认无误后重刷,Code: " + statusCode + ", Message: " + trimmedMsg;
                                        zhiyanLogger.warn(alertMsg);
                                        if (!chatId.equals("nosend")) {
                                            andataPushClient.pushMessage(chatId, alertMsg);
                                        }
                                        //直接入库
                                        ErrorTicket apiErrorTicket = new ErrorTicket(
                                                ticketId,
                                                String.format(extractFeaturesData.getBaseRsp().getMsg().trim()),
                                                999
                                        );

                                        if (errorTicketSink != null) {
                                            errorTicketSink.writeAsync(apiErrorTicket);
                                        }
                                    }
                                    return;
                                } else {
                                    // 所有重试都失败，执行告警操作和错误记录
                                    String baseMsg = extractFeaturesData.getBaseRsp().getMsg();
                                    String trimmedMsg = (baseMsg != null) ? baseMsg.trim() : "";
                                    Integer statusCode = extractFeaturesData.getBaseRsp().getCode();

                                    String alertMsg = ticketId + " 特征抽取接口返回错误,请确认无误后重刷,Code: " + statusCode + ", Message: " + trimmedMsg;
                                    zhiyanLogger.warn(alertMsg);

                                    // 发送告警消息
                                    if (!chatId.equals("nosend")) {
                                        andataPushClient.pushMessage(chatId, alertMsg);
                                    }

                                    // 🔧 修复：使用同步写入确保重要错误被记录
                                    ErrorTicket errorTicket = new ErrorTicket(ticketId, baseMsg, statusCode);
                                    if (errorTicketSink != null) {
                                        boolean writeSuccess = errorTicketSink.writeSyncImmediate(errorTicket);
                                        zhiyanLogger.error(alertMsg + " - 同步写入结果: " + writeSuccess);
                                    }

                                    synchronized (collector) {
                                        JsonNode jsonNodeWithRequestId = addStringField(jsonNode, "request-id",
                                                requestId, mapper);
                                        collector.collect(jsonNodeWithRequestId);
                                    }
                                    return;
                                }
                            }

                            // 解析响应 - 修复JSON解析问题
                            String answerContent;
                            Object answerObj = extractFeaturesData.getData().getAnswer();
                            if (answerObj instanceof String) {
                                answerContent = (String) answerObj;
                            } else {
                                // 如果不是字符串，尝试序列化为JSON
                                answerContent = mapper.writeValueAsString(answerObj);
                            }

                            // 继续处理成功的响应 - 内联处理逻辑
                            JsonNode responseNode;
                            try {
                                responseNode = mapper.readTree(answerContent);
                            } catch (Exception e) {
                                // 如果解析失败，将 answerContent 作为字符串处理
                                ObjectNode objectNode = mapper.createObjectNode();
                                objectNode.put("question_description", answerContent);
                                responseNode = objectNode;
                            }

                            // 线程安全的数据输出 - 先添加 requestId，再合并数据
                            synchronized (collector) {
                                // 1. 先将 requestId 添加到原始数据
                                JsonNode jsonNodeWithRequestId = addStringField(jsonNode, "request-id", requestId, mapper);

                                // 2. 再合并特征抽取结果
                                JsonNode mergedNode = JsonMergeUtils.mergeJsonSafe(jsonNodeWithRequestId, responseNode);

                                collector.collect(mergedNode);
                            }

                        } catch (Exception e) {
                            zhiyanLogger.warn(
                                    "异步处理特征抽取响应时发生错误: " + ticketId + " " + e.getMessage());
                            // 🔧 修复：异常情况下也要传递 requestId
                            try {
                                synchronized (collector) {
                                    JsonNode jsonNodeWithRequestId = addStringField(jsonNode, "request-id",
                                            requestId, mapper);
                                    collector.collect(jsonNodeWithRequestId);
                                }
                            } catch (Exception collectorException) {
                                zhiyanLogger.error("在异常处理中输出数据失败: " + ticketId + " "
                                        + collectorException.getMessage());
                            }
                        }
                    })
                    .exceptionally(throwable -> {
                        zhiyanLogger.warn("HTTP请求异常: " + ticketId + " " + throwable.getMessage());

                        // 如果还有重试次数，则进行重试
                        if (attemptCount < MAX_RETRY_ATTEMPTS - 1) {
                            zhiyanLogger.warn(String.format("%s HTTP请求失败，第%d次重试，将在%d分钟后重试",
                                    ticketId, attemptCount + 1, RETRY_DELAY_MINUTES));

                            // 延迟后重试
                            retryExecutor.schedule(() -> {
                                performHttpRequestWithRetry(url, body, headers, ticketId, jsonNode,
                                        collector, requestId, attemptCount + 1);
                            }, RETRY_DELAY_MINUTES, TimeUnit.MINUTES);
                        } else {
                            // 所有重试都失败，输出原始数据
                            try {
                                synchronized (collector) {
                                    JsonNode jsonNodeWithRequestId = addStringField(jsonNode, "request-id",
                                            requestId, mapper);
                                    collector.collect(jsonNodeWithRequestId);
                                }
                            } catch (Exception collectorException) {
                                zhiyanLogger.error("在网络异常处理中输出数据失败: " + ticketId + " "
                                        + collectorException.getMessage());
                            }
                        }
                        return null;
                    });
        } catch (Exception e) {
            zhiyanLogger.warn("HTTP请求创建失败: " + ticketId + " " + e.getMessage());

            // 如果还有重试次数，则进行重试
            if (attemptCount < MAX_RETRY_ATTEMPTS - 1) {
                zhiyanLogger.warn(String.format("%s HTTP请求创建失败，第%d次重试，将在%d分钟后重试",
                        ticketId, attemptCount + 1, RETRY_DELAY_MINUTES));

                // 延迟后重试
                retryExecutor.schedule(() -> {
                    performHttpRequestWithRetry(url, body, headers, ticketId, jsonNode,
                            collector, requestId, attemptCount + 1);
                }, RETRY_DELAY_MINUTES, TimeUnit.MINUTES);
            } else {
                // 所有重试都失败，输出原始数据
                try {
                    synchronized (collector) {
                        JsonNode jsonNodeWithRequestId = addStringField(jsonNode, "request-id",
                                requestId, mapper);
                        collector.collect(jsonNodeWithRequestId);
                    }
                } catch (Exception collectorException) {
                    zhiyanLogger.error("在HTTP请求创建异常处理中输出数据失败: " + ticketId + " "
                            + collectorException.getMessage());
                }
            }
        }
    }
}