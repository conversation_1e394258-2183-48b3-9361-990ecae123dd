package com.tencent.andata.smart.access.config;

import lombok.Builder;
import lombok.Getter;
import org.apache.flink.connector.hbase.options.HBaseWriteOptions;
import org.apache.flink.connector.hbase.util.HBaseConfigurationUtil;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HConstants;

@Getter
@Builder
public class HBaseConfig {
    private final String zkQuorum;
    private final String zkNodeParent;

    @Builder.Default
    private final long bufferFlushMaxRows = 1000L;

    @Builder.Default
    private final long bufferFlushIntervalMillis = 3000L;

    @Builder.Default
    private final long bufferFlushMaxSizeInBytes = 5 * 1024 * 1024L;

    public Configuration getHBaseConfiguration() {
        Configuration hbaseConf = HBaseConfigurationUtil.getHBaseConfiguration();
        hbaseConf.set(HConstants.ZOOKEEPER_QUORUM, zkQuorum);
        hbaseConf.set(HConstants.ZOOKEEPER_ZNODE_PARENT, zkNodeParent);
        return hbaseConf;
    }

    public HBaseWriteOptions getWriteOptions() {
        return HBaseWriteOptions.builder()
                .setBufferFlushMaxRows(bufferFlushMaxRows)
                .setBufferFlushIntervalMillis(bufferFlushIntervalMillis)
                .setBufferFlushMaxSizeInBytes(bufferFlushMaxSizeInBytes)
                .build();
    }
}