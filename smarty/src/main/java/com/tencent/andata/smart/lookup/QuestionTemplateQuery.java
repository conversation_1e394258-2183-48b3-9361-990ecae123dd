package com.tencent.andata.smart.lookup;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.lookup.exception.QueryException;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.Collections;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;


public class QuestionTemplateQuery {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final HashMapJDBCLookupQuery questionTemplateQuery;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String INVALID_JSON_MSG = "template data is invalid json:%s";
    private static final String TEMPLATE_ERROR_MSG = "get conversation: %s template error";


    static {
        questionTemplateQuery = Try.of(() -> {
            DatabaseConf datawareDbConf = createDatabaseConfig();
            JDBCSqlBuilderImpl sqlBuilder = createSqlBuilder();
            HashMapJDBCLookupQuery query = new HashMapJDBCLookupQuery(
                    DatabaseEnum.PGSQL,
                    datawareDbConf,
                    sqlBuilder
            );
            query.open();
            return query;
        }).getOrElseThrow(throwable -> new RuntimeException(throwable));
    }

    private static DatabaseConf createDatabaseConfig() throws Exception {
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        return new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName("cdc.database.pgsql.dataware_ods")
                .build();
    }

    private static JDBCSqlBuilderImpl createSqlBuilder() {
        return JDBCSqlBuilderImpl.builder()
                .tableName("ods_question_template")
                .selectField(Collections.singletonList("data"))
                .conditionKeyList(Collections.singletonList("conversation_id"))
                .databaseEnum(DatabaseEnum.PGSQL)
                .limit(1);
    }

    public Option<String> getTicketTemplate(String conversationId) {
        return Try.of(() -> {
                    java.util.HashMap<String, Object> queryRes = questionTemplateQuery
                            .query(Maps.newHashMap(ImmutableMap.of("conversation_id", conversationId)))
                            .get(0);

                    return Option.of(queryRes.get("data"))
                            .map(String.class::cast)
                            .flatMap(this::parseTemplateData)
                            .map(this::formatTemplateData);
                })
                .onFailure(e -> logger.error(String.format(TEMPLATE_ERROR_MSG, conversationId)))
                .getOrElse(Option.none());
    }

    private Option<JsonNode> parseTemplateData(String data) {
        return Try.of(() -> {
                    JsonNode templateData = objectMapper.readTree(data);
                    if (!templateData.isArray()) {
                        throw new QueryException("template data is not an array");
                    }
                    return templateData;
                }).onFailure(e -> logger.error(String.format(INVALID_JSON_MSG, data)))
                .toOption();
    }

    private String formatTemplateData(JsonNode templateData) {
        return StreamSupport.stream(templateData.spliterator(), false)
                .map(this::formatTemplateElement)
                .filter(Option::isDefined)
                .map(Option::get)
                .collect(Collectors.joining("\n"));
    }

    private Option<String> formatTemplateElement(JsonNode element) {
        return Try.of(() -> String.format(
                "%s：%s",
                element.get("name_cn").asText(),
                element.get("value").asText()
        )).toOption();
    }
}