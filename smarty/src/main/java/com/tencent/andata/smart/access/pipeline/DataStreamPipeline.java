package com.tencent.andata.smart.access.pipeline;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.smart.access.config.AppConfig;
import com.tencent.andata.smart.access.imp.GetDataFromKafka;
import com.tencent.andata.smart.access.operators.AsyncRequestSmallModel;
import com.tencent.andata.smart.access.operators.C2000FilterFunc;
import com.tencent.andata.smart.access.operators.C2000MsgDataProcess;
import com.tencent.andata.smart.access.operators.DeduplicateProcess;
import com.tencent.andata.smart.access.operators.GroupMsgDataProcess;
import com.tencent.andata.smart.access.operators.IsBigCustomerProcess;
import com.tencent.andata.smart.access.operators.ServiceChannelProcess;
import com.tencent.andata.smart.access.operators.ServiceSceneProcess;
import com.tencent.andata.smart.access.operators.WebImMsgDataProcess;
import com.tencent.andata.smart.access.operators.WebImTicketIdMapFunc;
import com.tencent.andata.smart.access.sink.HBaseSinkFactory;
import com.tencent.andata.smart.access.sink.KafkaSinkFactory;
import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.StreamUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataStreamPipeline {

    private static final Logger log = LoggerFactory.getLogger(DataStreamPipeline.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final JSONUtils jsonUtils = new JSONUtils();

    // 定义侧输出流标签为静态常量
    private static final OutputTag<JsonNode> WEBIM_OUTPUT_TAG = new OutputTag<JsonNode>("webim-side-output") {};
    private static final OutputTag<JsonNode> GROUP_OUTPUT_TAG = new OutputTag<JsonNode>("group-side-output") {};
    private static final OutputTag<JsonNode> C2000_OUTPUT_TAG = new OutputTag<JsonNode>("c2000-side-output") {};
    private static DatabaseConf dwDbConf;
    private static DatabaseConf workDbConf;
    private static StreamExecutionEnvironment env;
    private static Map<String, MqConf> kafkaConfigs;
    private final AppConfig appConfig;
    private final HBaseSinkFactory hBaseSinkFactory;
    private final KafkaSinkFactory kafkaSinkFactory;
    private final AsyncRequestSmallModel asyncCallSmallModel;
    private final ConcurrentHashMap<String, DataStream<String>> kafkaSourceMap;

    public DataStreamPipeline(StreamExecutionEnvironment env, AppConfig appConfig) {
        this.appConfig = appConfig;
        DataStreamPipeline.env = env;
        dwDbConf = appConfig.getDWDBConf();
        workDbConf = appConfig.getWorkDBConf();
        kafkaConfigs = appConfig.getKafkaConfigs();
        this.kafkaSourceMap = createKafkaSources();
        this.kafkaSinkFactory = new KafkaSinkFactory();

        this.asyncCallSmallModel = AsyncRequestSmallModel
                .builder()
                .URL(appConfig.getSmallModelUrl())
                .build();

        this.hBaseSinkFactory = new HBaseSinkFactory(appConfig.getHbaseConfig());
    }

    private static ConcurrentHashMap<String, DataStream<String>> createKafkaSources() {
        ConcurrentHashMap<String, DataStream<String>> sources = new ConcurrentHashMap<>();
        kafkaConfigs.forEach((k, v) -> {
            if (!StringUtils.equalsIgnoreCase(k, "ansmartQuality")) {
                sources.put(k,
                        GetDataFromKafka
                                .builder()
                                .fEnv(env)
                                .name(k)
                                .kafkaConf(v)
                                .getSourceData());
            }
        });

        return sources;
    }

    public void setupPipeline() {
        // 处理工单流
        DataStream<JsonNode> ticketStream = processTicketStream();

        // 处理群消息流
        SingleOutputStreamOperator<HbaseRow> groupStream = processGroupStream();

        // 处理C2000消息流
        SingleOutputStreamOperator<HbaseRow> c2000Stream = processC2000Stream();

        // 处理WebIM流
        SingleOutputStreamOperator<HbaseRow> webimStream = processWebimStream();

        // 合并所有流并处理
        DataStream<JsonNode> mergedStream = mergeSideOutputStreams(
                webimStream,
                groupStream,
                c2000Stream,
                ticketStream
        );

        // 异步调用小模型
        DataStream<JsonNode> enrichedStream = callSmallModel(mergedStream);

        // 设置接收器
        setupSinks(enrichedStream, groupStream, webimStream, c2000Stream);
    }

    private DataStream<JsonNode> processTicketStream() {
        return kafkaSourceMap
                .get("ticketOperationStream")
                .map(objectMapper::readTree)
                .keyBy(json -> json.get("operation_id").asLong())
                .process(new DeduplicateProcess())
                .name("remove-duplicates-process")
                .process(new ServiceChannelProcess(workDbConf))
                .name("get-ticketBaseInfo-process")
                .process(new ServiceSceneProcess(dwDbConf))
                .name("ticket-service-scene-process");
    }

    private SingleOutputStreamOperator<HbaseRow> processGroupStream() {
        ArrayNode devGroupList = appConfig.getDevGroupList();

        List<String> devGroupIds = StreamUtils
                .streamOf(devGroupList)
                .filter(JsonNode::isTextual)
                .map(JsonNode::asText)
                .collect(Collectors.toList());

        DataStream<JsonNode> groupAllInOne = kafkaSourceMap
                .get("pcloudGroupStream")
                .union(kafkaSourceMap.get("knockGroupStream"))
                .union(kafkaSourceMap.get("slackGroupStream"))
                .union(kafkaSourceMap.get("qqGroupStream"))
                .map(objectMapper::readTree)
                .name("collect-all-group-msg-data");

        return groupAllInOne
                // 测试环境下，过滤出测试群
                // 正式环境下，由于devGroupIds为空，所以不做过滤
                .filter(json -> devGroupIds.isEmpty() || devGroupIds.contains(json.get("group_id").asText()))
                .process(new IsBigCustomerProcess(dwDbConf))
                .name("is-big-customer-process")
                .process(new GroupMsgDataProcess(GROUP_OUTPUT_TAG))
                .name("group-msg-data-process");
    }

    private SingleOutputStreamOperator<HbaseRow> processC2000Stream() {
        return kafkaSourceMap
                .get("C2000Stream")
                .map(objectMapper::readTree)
                .name("collect-c2000-msg-data")
                .filter(new C2000FilterFunc())
                .process(new ServiceChannelProcess(workDbConf))
                .name("c2000-get-ticketBaseInfo-process")
                .process(new C2000MsgDataProcess(C2000_OUTPUT_TAG))
                .name("c2000-msg-data-process");
    }

    private SingleOutputStreamOperator<HbaseRow> processWebimStream() {
        return kafkaSourceMap
                .get("webimSourceStream")
                .map(value -> jsonUtils.toSnakeCase(jsonUtils.flatten(objectMapper.readTree(value))))
                .map(new WebImTicketIdMapFunc()) // 为每个webim消息添加工单ID
                .process(new ServiceChannelProcess(workDbConf))
                .name("webim-get-ticketBaseInfo-process")
                .process(new ServiceSceneProcess(dwDbConf))
                .name("webim-service-scene-process")
                .process(new WebImMsgDataProcess(WEBIM_OUTPUT_TAG))
                .name("webim-msg-data-process");
    }

    private DataStream<JsonNode> mergeSideOutputStreams(
            SingleOutputStreamOperator<HbaseRow> webimStream,
            SingleOutputStreamOperator<HbaseRow> groupStream,
            SingleOutputStreamOperator<HbaseRow> c2000Stream,
            DataStream<JsonNode> ticketStream) {

        return ticketStream
                .union(groupStream.getSideOutput(GROUP_OUTPUT_TAG))
                .union(c2000Stream.getSideOutput(C2000_OUTPUT_TAG))
                .union(webimStream.getSideOutput(WEBIM_OUTPUT_TAG));
    }

    private DataStream<JsonNode> callSmallModel(DataStream<JsonNode> stream) {
        return AsyncDataStream.unorderedWait(
                stream,
                asyncCallSmallModel,
                5,
                TimeUnit.MINUTES,
                50
        );
    }

    private void setupSinks(
            DataStream<JsonNode> enrichedStream,
            SingleOutputStreamOperator<HbaseRow> groupStream,
            SingleOutputStreamOperator<HbaseRow> webimStream,
            SingleOutputStreamOperator<HbaseRow> c2000Stream) {

        // Kafka sink
        enrichedStream.sinkTo(kafkaSinkFactory.createKafkaSink(appConfig))
                .setParallelism(4)
                .name("all-data-sink-kafka");

        // HBase sinks
        String groupMsgDataTable = appConfig.getHbaseTableNames().get("Group_msg");
        String webimOperationTable = appConfig.getHbaseTableNames().get("Webim_operation");

        c2000Stream.addSink(hBaseSinkFactory.createSink(groupMsgDataTable)).name("c2000-msg-sink-hbase");
        groupStream.addSink(hBaseSinkFactory.createSink(groupMsgDataTable)).name("group-msg-sink-hbase");
        webimStream.addSink(hBaseSinkFactory.createSink(webimOperationTable)).name("webim-msg-sink-hbase");
    }
}