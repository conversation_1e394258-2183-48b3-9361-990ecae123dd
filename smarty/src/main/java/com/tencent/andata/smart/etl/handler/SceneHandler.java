package com.tencent.andata.smart.etl.handler;

import com.tencent.andata.smart.strategy.model.Strategy;
import io.vavr.Tuple4;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public interface SceneHandler {

    Option<Long> getTicketId(Strategy strategy);

    Option<String> getServiceChannel(Strategy strategy);

    Option<String> getTriggerContent(Strategy strategy);

    Option<String> getOperationId(Strategy strategy);

    Option<String> getRiskType(Strategy strategy);

    Option<String> getDutyResponsible(Strategy strategy);

    Option<Long> getEventTime(Strategy strategy);

    Option<String> getCurrentStaffIdr1(Strategy strategy);

    Option<String> getCurrentPost(Strategy strategy);

    Option<String> getCurrentCustomerName(Strategy strategy);

    Option<Integer> getMsgSeq(Strategy strategy);

    Option<JsonNode> getTriggerDataByField(Strategy strategy, String field);

    Option<Tuple4<String, String, String, String>> getServiceScenesName(Strategy strategy);
}