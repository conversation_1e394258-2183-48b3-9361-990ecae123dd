package com.tencent.andata.smart.etl.keyBy;

import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;
import static io.vavr.Predicates.is;
import static io.vavr.Predicates.anyOf;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.enums.Scene;
import io.vavr.collection.Array;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.flink.api.java.functions.KeySelector;

/**
 * 策略键选择器，用于根据不同场景类型生成数据分组的键
 * 使用vavr函数式API实现，提供更优雅的模式匹配和异常处理
 */
public class StrategyKeySelector implements KeySelector<Strategy, String> {

    private static final FlinkLog logger = FlinkLog.getInstance();

    /**
     * 场景到主键映射表，定义各种场景使用的主键字段名
     */
    private final Map<Scene, String> pkMap = new HashMap<Scene, String>() {{
        put(Scene.WebIM, "value_of_primary_key");
        put(Scene.Ticket, "operation_id");
        put(Scene.Group, "msg_id");
        put(Scene.C2000, "msg_id");
    }};

    @Override
    public String getKey(Strategy strategy) {
        // 使用vavr模式匹配按场景类型处理
        String pks = Array.of(strategy.trigger.data)
                .map(data -> Try.<String>of(() -> Match(strategy.scene)
                        .of(
                                // Ticket场景需要转换Long类型
                                Case($(is(Scene.Ticket)), () -> String.valueOf(data.get(pkMap.get(strategy.scene)).asLong())),
                                // WebIM、Group和C2000场景都是直接获取文本值，逻辑相同，合并处理
                                Case($(anyOf(is(Scene.WebIM), is(Scene.Group), is(Scene.C2000))), () -> data.get(pkMap.get(strategy.scene)).asText()),
                                // 默认情况抛出异常，表示不支持的场景类型
                                Case($(), () -> {throw new RuntimeException(String.format("[StrategyKeySelector] Not Support scene: %s", strategy.scene.name()));})
                        )
                ).getOrElseGet(e -> {
                    // 异常处理，记录错误并返回空字符串
                    logger.info(String.format("[StrategyKeySelector] strategy: %s, error: %s", strategy, e));
                    return "";
                }))
                .collect(Collectors.joining("-"));

        // 使用策略ID和数据主键组成完整的键
        return String.format("%s-%s", strategy.id, pks);
    }
}