package com.tencent.andata.smart.access.operators;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

public class DeduplicateProcessV2 extends KeyedProcessFunction<Long, JsonNode, JsonNode> {
    private ValueState<Boolean> hasExisted;

    @Override
    public void open(Configuration parameters) {
        ValueStateDescriptor<Boolean> descriptor = new ValueStateDescriptor<>("hasExistedState", Boolean.class);

        // 保存所有流水状态
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.hours(8))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        descriptor.enableTimeToLive(ttlConfig);
        hasExisted = getRuntimeContext().getState(descriptor);
    }

    @Override
    public void processElement(JsonNode json, Context ctx, Collector<JsonNode> out) throws Exception {
        // 检查状态，如果没有见过这个operation_id，输出并更新状态
        if (hasExisted.value() == null || !hasExisted.value()) {
            hasExisted.update(true);

            /*
             * 将建单时间作为create_time，
             * 当前流水的operate_time作为end_time，
             * 用于后续查询群消息
             */
            ((ObjectNode) json).put("data_type", "ticket_operation");
            out.collect(json);
        }
    }
}