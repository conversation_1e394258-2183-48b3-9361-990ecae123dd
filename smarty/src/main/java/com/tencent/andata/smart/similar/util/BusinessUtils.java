package com.tencent.andata.smart.similar.util;

import static com.tencent.andata.utils.TableUtils.generateUniqueServerId;


import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.cdc.deserializer.RowKindJsonDeserializationSchemaBase.TableIRowKindJson;
import com.tencent.andata.utils.cdc.deserializer.RowKindJsonDeserializationSchemaV2;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class BusinessUtils {


    /**
     * 生成MySQL的CDC Source
     *
     * @param databaseConf 数据库
     * @param debeziumDeserializationSchema 反序列化器
     * @return CDC DataStream
     */
    public static Source<String, ?, ?> getMySQLSource(
            String tableName,
            DatabaseConf databaseConf,
            DebeziumDeserializationSchema<String> debeziumDeserializationSchema) {

        Properties debeziumProps = new Properties();
        debeziumProps.setProperty("bigint.unsigned.handling.mode", "long");
        debeziumProps.setProperty("decimal.handling.mode", "double");
        return MySqlSource.<String>builder()
                .hostname(databaseConf.dbHost)
                .port(databaseConf.dbPort)
                .databaseList(databaseConf.dbName) // 自动添加数据库也可以
                .username(databaseConf.userName)
                .password(databaseConf.password)
                // 这个ID需要是全局唯一的，如果不唯一会出现问题, 加上abs，serverID不能为负数
                .serverId(generateUniqueServerId(tableName, "similar"))
                .tableList(String.format("%s.%s", databaseConf.dbName, tableName))
                .deserializer(debeziumDeserializationSchema)
                .scanNewlyAddedTableEnabled(true)
                // 由数据库把timestamp with timezone的转换成UTC
                .serverTimeZone("Asia/Shanghai")
                .debeziumProperties(debeziumProps)
                // 这里下游算子会在init阶段全量查询库，所以从最近查询就行
                .startupOptions(StartupOptions.latest())
                .build();
    }

    public static String buildKnowledge(String title, String content) {
        if (content == null || content.trim().isEmpty()) {
            return title;
        }
        return title + "\n" + content;
    }

    public static void configureStateAndCheckpoint(StreamExecutionEnvironment env) {
        env.getConfig().enableObjectReuse();
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                10, Time.of(1, TimeUnit.MINUTES), Time.of(10, TimeUnit.SECONDS)
        ));
    }


    public static String generateSimilarQuestionsPrompt(String prompt, String originalQuestion, int n) {
        return String.format(prompt, n, originalQuestion, n);
    }


    /**
     * 解析时间戳字符串为毫秒时间戳
     * 支持多种常见时间格式，包括但不限于：
     * - "2025-07-06 17:03:47.0", "2025-07-06 17:03:47.123"
     * - "2025-07-06 17:03:47", "2025/07/06 17:03:47"
     * - "2025-07-06T17:03:47", "2025-07-06T17:03:47.123Z"
     * - "2025-07-06", "2025/07/06"
     * - "17:03:47" (当天时间)
     */
    // 静态缓存 DateTimeFormatter 数组以提高性能
    public static final DateTimeFormatter[] TIMESTAMP_FORMATTERS = {
            // 完整日期时间格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.SS"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.S"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            // ISO格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            // 仅日期格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            // 仅时间格式（当天）
            DateTimeFormatter.ofPattern("HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("HH:mm:ss.SS"),
            DateTimeFormatter.ofPattern("HH:mm:ss.S"),
            DateTimeFormatter.ofPattern("HH:mm:ss")
    };

    // 统一使用东八区时区，确保时区一致性
    public static final ZoneOffset DEFAULT_ZONE_OFFSET = ZoneOffset.ofHours(8);

    public static Long parseTimestampString(String timestampStr, Long defaultValue) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return defaultValue;
        }

        String cleanStr = timestampStr.trim();

        // 尝试解析不同格式
        for (DateTimeFormatter formatter : TIMESTAMP_FORMATTERS) {
            try {
                LocalDateTime dateTime;

                // 处理仅时间格式（使用当天日期）
                if (cleanStr.matches("\\d{1,2}:\\d{2}:\\d{2}(\\.\\d{1,3})?")) {
                    LocalDate today = LocalDate.now();
                    dateTime = LocalDateTime.of(today, formatter.parse(cleanStr, java.time.LocalTime::from));
                }
                // 处理仅日期格式（使用00:00:00时间）
                else if (cleanStr.matches("\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}$")) {
                    LocalDate date = formatter.parse(cleanStr, LocalDate::from);
                    dateTime = date.atStartOfDay();
                }
                // 处理完整日期时间格式
                else {
                    // 统一处理所有格式，包括带Z后缀的UTC时间
                    if (cleanStr.endsWith("Z")) {
                        // 对于UTC时间，先解析为UTC，然后转换为东八区时间戳
                        dateTime = formatter.parse(cleanStr, LocalDateTime::from);
                        return dateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
                    } else {
                        dateTime = LocalDateTime.parse(cleanStr, formatter);
                    }
                }

                // 统一转换为毫秒时间戳（使用东八区时区）
                return dateTime.toInstant(DEFAULT_ZONE_OFFSET).toEpochMilli();

            } catch (java.time.format.DateTimeParseException e) {
                // 继续尝试下一个格式
                continue;
            } catch (Exception e) {
                // 记录非解析异常，可能是编程错误
                continue;
            }
        }

        return defaultValue;
    }

}