package com.tencent.andata.smart.etl.mapFunc;

import com.tencent.andata.smart.etl.process.RetryControlProcessFunction;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.tuple.Tuple3;

/**
 * 首次重试映射函数
 * 为需要重试的WebIM数据准备重试元组，设置初始重试计数为1
 */
public class FirstRetryMapFunction implements MapFunction<Strategy, Tuple3<Strategy, Integer, Long>> {

    private final int retryIntervalSeconds;

    /**
     * 构造函数
     *
     * @param retryIntervalSeconds 重试间隔时间（秒）
     */
    public FirstRetryMapFunction(int retryIntervalSeconds) {
        this.retryIntervalSeconds = retryIntervalSeconds;
    }

    /**
     * 将需要首次重试的策略转换为带有初始重试计数(1)和重试时间戳的元组
     *
     * @param strategy 需要重试的策略对象
     * @return 包含策略、重试计数(1)和下次重试时间的元组
     */
    @Override
    public Tuple3<Strategy, Integer, Long> map(Strategy strategy) {
        // 初次重试的策略，重试计数为1
        return RetryControlProcessFunction.createRetryTuple(strategy, 1, retryIntervalSeconds);
    }
}