package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.enums.CallDirectionType;
import com.tencent.andata.smart.enums.OperationType;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.utils.StaffInfoUtils;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.control.Option;
import io.vavr.control.Try;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 呼叫中心质检拼接实现
 * 负责处理工单流水和呼叫中心录音流水，生成质检系统可理解的文本描述
 */
public class CallCenterQASplice extends TicketOperationRCSplice {

    private static final FlinkLog logger = FlinkLog.getInstance();

    // 数据类型常量
    private static final String CC_OPERATION_TYPE = "cc_operation";
    private static final String REGEX = "客户:\\s*工号。\\r?\\n客户:\\s*\\d+。\\r?\\n客户:\\s*为您服务。\\r?\\n?";

    // 工单操作模板常量
    private static final String RETRIEVE_TEMPLATE = "\n%s，坐席%s进入工单处理客户问题";
    private static final String TRANSFER_TEMPLATE = "\n%s，坐席%s将工单转移（转单）给坐席%s";
    private static final String PROGRESS_TEMPLATE = "\n%s，坐席%s在工单中记录客户问题的处理进展（工单日志）：\n%s";
    private static final String CREATE_TEMPLATE = "\n%s，坐席%s创建工单，在工单中记录客户问题的处理进展（工单日志）：\n%s";
    private static final String CLOSE_TEMPLATE = "\n%s，坐席%s触发动作“结单”，并在工单中记录客户问题的处理进展（工单日志）：\n%s";

    // 呼叫中心操作模板常量
    private static final String OUTGOING_CALL_DESC = "通过电话外呼客户";
    private static final String INCOMING_CALL_DESC = "接听来自客户的电话";
    private static final String CALL_TEMPLATE = "\n%s，坐席%s %s，通话内容为：\n%s";


    /**
     * 根据场景类型处理不同的流水项
     *
     * @param item 流水项
     * @param strategy 策略
     * @return 拼接后的字符串
     */
    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        // 输入验证, item和strategy不能为空，且必须为呼叫中心通道
        if (item.isEmpty() || strategy.isEmpty() || !isCallCenterChannel(strategy)) {
            logger.warn("[CallCenterQASplice] Invalid input: item or strategy is empty");
            return "";
        }

        return Try.of(() -> {
                    JsonNode currentNode = item.get().getCurrent();
                    if (currentNode == null) {
                        logger.warn("[CallCenterQASplice] Current node is null");
                        return "";
                    }

                    // 根据数据类型路由到不同的处理器
                    if (isCallCenterOperation(currentNode)) {return processCallCenterItem(currentNode);}

                    return processTicketItem(currentNode);

                })
                .onFailure(e -> logger.error(String.format("[CallCenterQASplice] Error processing item: %s", e)))
                .getOrElse("");
    }

    /**
     * 判断是否为呼叫中心操作数据
     */
    private boolean isCallCenterOperation(JsonNode node) {
        return node.has("data_type") &&
                CC_OPERATION_TYPE.equals(node.get("data_type").asText());
    }

    /**
     * 处理工单流水
     *
     * @param currentNode 当前流水项
     * @return 拼接后的字符串
     */
    private String processTicketItem(JsonNode currentNode) {
        return Try.of(() -> {
                    // 安全地提取操作类型
                    OperationType operationType = extractOperationType(currentNode)
                            .getOrElseThrow(() -> new IllegalArgumentException("Invalid operation type"));

                    // 提取基础信息
                    TicketItemData itemData = extractTicketItemData(currentNode);

                    // 根据操作类型生成描述
                    return formatTicketOperation(operationType, itemData);
                })
                .onFailure(e -> logger.error(String.format("[CallCenterQASplice] Error processing ticket item: %s", e)))
                .getOrElse("");
    }

    /**
     * 安全地提取操作类型
     */
    private Option<OperationType> extractOperationType(JsonNode node) {
        return getOperationType(node)
                .flatMap(opCode -> Try.of(() -> OperationType.of(Integer.parseInt(opCode)))
                        .onFailure(e -> logger.warn("[CallCenterQASplice] Invalid operation code: " + opCode))
                        .toOption());
    }

    /**
     * 提取工单流水项数据
     */
    private TicketItemData extractTicketItemData(JsonNode node) {
        String operateTime = extractNodeText(node, "operate_time")
                .getOrElse("").replace("T", " ")
                .replace("+08:00", "");

        String innerReply = extractNodeText(node, "inner_reply")
                .map(this::cleanContent)
                .filter(StringUtils::isNotEmpty)
                .getOrElse("");

        String staffName = extractNodeText(node, "operator")
                .map(StaffInfoUtils::getUserName)
                .getOrElse("");

        String nextStaffName = extractNodeText(node, "next_operator")
                .map(StaffInfoUtils::getUserName)
                .getOrElse("");

        return new TicketItemData(operateTime, innerReply, staffName, nextStaffName);
    }

    /**
     * 安全地提取节点文本内容
     */
    private Option<String> extractNodeText(JsonNode node, String fieldName) {
        return Option.of(node)
                .filter(n -> n.has(fieldName))
                .map(n -> n.get(fieldName))
                .filter(n -> !n.isNull())
                .map(JsonNode::asText)
                .filter(StringUtils::isNotEmpty);
    }

    /**
     * 根据操作类型格式化工单操作描述
     */
    private String formatTicketOperation(OperationType operationType, TicketItemData data) {
        switch (operationType) {
            case RETRIEVE:
                return String.format(RETRIEVE_TEMPLATE, data.operateTime, data.staffName);
            case CREATE:
                return String.format(CREATE_TEMPLATE, data.operateTime, data.staffName, data.innerReply);
            case TRANSFER:
                return String.format(TRANSFER_TEMPLATE, data.operateTime, data.staffName, data.nextStaffName);
            case REPLY:
            case COMMENT:
            case EFFECTIVE_CALL:
                return String.format(PROGRESS_TEMPLATE, data.operateTime, data.staffName, data.innerReply);
            case CLOSE:
                return String.format(CLOSE_TEMPLATE, data.operateTime, data.staffName, data.innerReply);
            default:
                logger.warn("[CallCenterQASplice] Unsupported operation type: " + operationType);
                return "";
        }
    }

    /**
     * 处理呼叫中心录音流水
     *
     * @param currentNode 当前流水项
     * @return 拼接后的字符串
     */
    private String processCallCenterItem(JsonNode currentNode) {
        return Try.of(() -> {
                    // 提取呼叫中心数据
                    CallCenterItemData itemData = extractCallCenterItemData(currentNode);

                    // 格式化呼叫描述
                    return formatCallCenterOperation(itemData);
                })
                .onFailure(e -> logger.error(String.format("[CallCenterQASplice] Error processing call center item: %s", e)))
                .getOrElse("");
    }

    /**
     * 提取呼叫中心流水项数据
     */
    private CallCenterItemData extractCallCenterItemData(JsonNode node) {
        int callDirectionCode = node.has("call_direction") ? node.get("call_direction").asInt() : -1;
        CallDirectionType callDirection = CallDirectionType.fromCode(callDirectionCode);

        String callTime = extractNodeText(node, "create_time")
                .getOrElse("").replace("T", " ")
                .replace("+08:00", "")
                .replace(".0", "");

        String content = extractNodeText(node, "content")
                //.map(this::cleanContent)
                .getOrElse("");

        String userId = extractNodeText(node, "user_id")
                .getOrElse("");

        return new CallCenterItemData(callDirection, callTime, content.replaceFirst(REGEX, ""), userId);
    }

    /**
     * 格式化呼叫中心操作描述
     */
    private String formatCallCenterOperation(CallCenterItemData data) {
        String callTypeDesc = getCallTypeDescription(data.callDirection);
        return String.format(CALL_TEMPLATE, data.callTime, data.userId, callTypeDesc, data.content);
    }

    /**
     * 获取通话类型描述
     */
    private String getCallTypeDescription(CallDirectionType callDirection) {
        if (callDirection == null) {
            logger.warn("[CallCenterQASplice] Unknown call direction");
            return "进行通话";
        }

        switch (callDirection) {
            case INCOMING:
                return INCOMING_CALL_DESC;
            case OUTGOING:
                return OUTGOING_CALL_DESC;
            default:
                logger.warn("[CallCenterQASplice] Unsupported call direction: " + callDirection);
                return "进行通话";
        }
    }

    /**
     * 工单流水项数据容器
     */
    private static class TicketItemData {

        final String operateTime;
        final String innerReply;
        final String staffName;
        final String nextStaffName;

        TicketItemData(String operateTime, String innerReply, String staffName, String nextStaffName) {
            this.operateTime = operateTime;
            this.innerReply = innerReply;
            this.staffName = staffName;
            this.nextStaffName = nextStaffName;
        }
    }

    /**
     * 呼叫中心流水项数据容器
     */
    private static class CallCenterItemData {

        final CallDirectionType callDirection;
        final String callTime;
        final String content;
        final String userId;

        CallCenterItemData(CallDirectionType callDirection, String callTime, String content, String userId) {
            this.callDirection = callDirection;
            this.callTime = callTime;
            this.content = content;
            this.userId = userId;
        }
    }
}