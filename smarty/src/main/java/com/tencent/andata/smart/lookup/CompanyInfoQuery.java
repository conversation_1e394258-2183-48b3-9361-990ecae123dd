package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;

public class CompanyInfoQuery extends AbstractJDBCLookupQuery<String, String> {

    public CompanyInfoQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected String executeQuery(Connection connection, String companyId) throws Exception {
        String sql = "SELECT name FROM dim_companie_info WHERE id = ? LIMIT 1";
        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setString(1, companyId);
        final ResultSet resultSet = preparedStatement.executeQuery();
        // 遍历结果
        while (resultSet.next()) {
            // 公司名称
            return resultSet.getString(1);
        }
        return "";
    }
}