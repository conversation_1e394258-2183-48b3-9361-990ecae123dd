package com.tencent.andata.smart.similar;

import static com.tencent.andata.smart.similar.util.BusinessUtils.configureStateAndCheckpoint;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.addStringField;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.getLongValue;
import static com.tencent.andata.smart.similar.util.JsonMergeUtils.getTextValue;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.smart.access.operators.ServiceSceneProcess;
import com.tencent.andata.smart.etl.process.GenericSlidingWindowRateLimitingProcess;
import com.tencent.andata.smart.similar.config.ApplicationConfig;
import com.tencent.andata.smart.similar.model.DirectLLMResponse;
import com.tencent.andata.smart.similar.model.req.SimilarSearchRequest;
import com.tencent.andata.smart.similar.service.AsyncTaskManager;
import com.tencent.andata.smart.similar.service.ExtractProcess;
import com.tencent.andata.smart.similar.service.LLmProcess;
import com.tencent.andata.smart.similar.service.SimilarBuildMap;
import com.tencent.andata.smart.similar.service.SourceFactMap;
import com.tencent.andata.smart.similar.sink.EnhancedSinkFunction;
import com.tencent.andata.smart.similar.source.BatchJDBCSource;
import com.tencent.andata.smart.similar.util.BusinessUtils;
import com.tencent.andata.smart.similar.util.DirectResponseParser;
import com.tencent.andata.smart.similar.util.ErrorTicketSink;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.SnowflakeDistributeId;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.polaris.PolarisUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataRefreshApplication {

    private static final Logger log = LoggerFactory.getLogger(DataRefreshApplication.class);
    private static final SnowflakeDistributeId idWorker = new SnowflakeDistributeId(0, 28);

    private static final String rainbowGroup = "cprb.gpt.prompt";
    private static int parallelism;
    private static ErrorTicketSink globalErrorTicketSink;  // 🔧 新增：全局错误票据处理


    // 添加AsyncTaskManager静态变量
    private static AsyncTaskManager asyncTaskManager;

    // 添加批处理相关的静态变量
    private static BatchJDBCSource batchJDBCSource;

    public static void main(String[] args) throws Exception {
        // 1. 环境初始化
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        StreamExecutionEnvironment env = fEnv.env();
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        parallelism = fEnv.env().getParallelism();
        // 2. 加载配置
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
        ApplicationConfig config = loadConfiguration(rainbowUtils, parameterTool);

        // 3. 初始化服务组件
        initializeServices(config);

        ObjectMapper mapper = new ObjectMapper();

        // 提取GPT相关配置参数
        final String modelUrl = config.getModelUrl();
        final String modelToken = config.getModelToken();
        final String similarTicketPrompt = config.getSimilarTicketPrompt();
        final Integer similarGenerateNum = config.getGenerateNum();
        DatabaseConf aigcDbConf = config.getAigcDbConf();
        Boolean isGenerate = config.getIsGenerate();
        Boolean isIncrese = config.getIsIncrese();
        globalErrorTicketSink = new ErrorTicketSink(
                String.format("jdbc:postgresql://%s:%d/%s",
                        aigcDbConf.getDbHost(), aigcDbConf.getDbPort(), aigcDbConf.getDbName()),
                aigcDbConf.getUserName(),
                aigcDbConf.getPassword(),
                3  // 批次大小
        );

        // 1. 构建数据处理流水线
        DataStream<JsonNode> ticketSourceStream = createSourceStream(fEnv, tEnv, mapper,
                isIncrese, config, parameterTool);

        // 2. 特征提取
        SingleOutputStreamOperator<JsonNode> extractStream = buildExtractStreamFromJsonNode(ticketSourceStream, mapper,
                parameterTool.getLong("ratePerSecond", 1), parameterTool.get("pushid", ""),
                parameterTool, config);

        // 3. 构建数据处理流水线
        SingleOutputStreamOperator<JsonNode> enrichedSourceStream = enrichSourceStream(extractStream, mapper,
                config.getDwdDBConf(), config.getStarRocksQuery(), isIncrese);

        // 4. GPT处理
        SingleOutputStreamOperator<JsonNode> gptStream = buildGptStream(enrichedSourceStream, modelUrl, modelToken,
                similarTicketPrompt, similarGenerateNum, mapper, isGenerate);

        // 6. GPT响应解析
        SingleOutputStreamOperator<Tuple2<JsonNode, DirectLLMResponse>> parsedGptStream = buildGptParsingStream(
                gptStream);

        // 7. 相似问题构建
        SingleOutputStreamOperator<Tuple2<JsonNode, SimilarSearchRequest>> similarQuestionsStream = buildSimilarQuestionsStream(
                parsedGptStream, mapper);

        // 8. 异步写入ansearch
        SingleOutputStreamOperator<Tuple2<JsonNode, SimilarSearchRequest>> outputStream =
                buildAsyncAnswerSearchStream(similarQuestionsStream);

        // 9. 添加数据库写入Sink
        setupSinks(outputStream, aigcDbConf);

        // 10. 配置Flink执行环境
        configureExecution(env, tEnv);

        // 11. 添加优雅关闭钩子
        addUnifiedShutdownHook();

        // 12. 执行任务
        env.execute("similar-refresh-pipeline");
    }

    private static SingleOutputStreamOperator<JsonNode> enrichSourceStream(DataStream<JsonNode> ticketSourceStream,
            ObjectMapper mapper, DatabaseConf dwdDBConf, HashMapJDBCLookupQuery starockQuery, Boolean isIncrese) {

        return ticketSourceStream
                .process(new ServiceSceneProcess(dwdDBConf))
                .map(new MapFunction<JsonNode, JsonNode>() {
                    @Override
                    public JsonNode map(JsonNode jsonNode) throws Exception {
                        HashMap<String, Object> params = new HashMap<>(1);
                        params.put("ticket_id", getLongValue(jsonNode, "ticket_id", 0L));
                        List<HashMap<String, Object>> querys = starockQuery.query(params);
                        if (querys.size() != 0) {
                            HashMap<String, Object> queryResult = querys.get(0);
                            String closeTime = String.valueOf(queryResult.get("close_time"));
                            JsonNode res = addStringField(jsonNode, "ticket_close_time", closeTime, mapper);
                            return res;
                        } else {
                            return addStringField(jsonNode, "ticket_close_time", "0l", mapper);
                        }
                    }
                })
                .setParallelism(2)
                .name("03-enrichData");
    }


    /**
     * 构建特征提取流 - 处理JsonNode输入
     */
    private static SingleOutputStreamOperator<JsonNode> buildExtractStreamFromJsonNode(
            DataStream<JsonNode> stream, ObjectMapper mapper, long ratePerMin,
            String chatId, ParameterTool parameterTool, ApplicationConfig config) {
//        maxRequestsPerMinute

        return stream.keyBy(ticket -> "GLOBAL")
                .process(new GenericSlidingWindowRateLimitingProcess<>(
                        ratePerMin,     // 10秒内最多ratePerSecond个事件
                        60000,  // 10秒窗口
                        10000,   // 每1秒检查
                        (int) (ratePerMin / 6), // 批量输出约1/6的限流值
                        JsonNode.class // 指定类型
                )).returns(new TypeHint<JsonNode>() {
                })
                .process(new ExtractProcess(mapper, chatId, config))  // 🔧 传递全局ErrorTicketSink
                .name("02-ExtractFeatures")
                .disableChaining()
                .setParallelism(1);
    }

    private static ApplicationConfig loadConfiguration(RainbowUtils rainbowUtils, ParameterTool parameterTool)
            throws Exception {

        boolean isIncrese = parameterTool.getBoolean("isincrese");

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlWorkDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        DatabaseConf DWDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "postgresql", "dataware_r"))
                .build();

        JDBCSqlBuilderImpl StarRocksTableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("dwm_ticket_statistic")
                .selectAll()
                .conditionKeyList(Collections.singletonList("ticket_id"))
                .databaseEnum(PGSQL);

        DatabaseConf starocksDbConf = kvConfBuilder
                .setGroupName("cdc.database.pgsql.dataware")
                .build();

        DatabaseConf aigcDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "postgresql", "cprb_aigc"))
                .build();

        HashMapJDBCLookupQuery starRocksQuery = new HashMapJDBCLookupQuery(PGSQL, starocksDbConf,
                StarRocksTableBuilder);
        starRocksQuery.open();

        String gptGroup = "cprb.gpt.prompt";
        String modelUrl = rainbowUtils.getStringValue(gptGroup, "url");
        String modelToken = rainbowUtils.getStringValue(gptGroup, "token");

        String similarTicketPrompt = rainbowUtils.getStringValue(rainbowGroup, "similar_ticket_prompt");
        String similarGenerateNum = rainbowUtils.getStringValue(rainbowGroup, "similar_generate_num");
        boolean is_generate = Boolean.getBoolean(rainbowUtils.getStringValue("cprb.gpt.conf", "similar_generate_num"));

        return ApplicationConfig.builder()
                .mysqlWorkDBConf(mysqlWorkDBConf)
                .dwdDBConf(DWDBConf)
                .modelUrl(modelUrl)
                .modelToken(modelToken)
                .aigcDbConf(aigcDbConf)
                .similarTicketPrompt(similarTicketPrompt)
                .similarGenerateNum(similarGenerateNum)
                .starRocksQuery(starRocksQuery)
                .isGenerate(is_generate)
                .isIncrese(isIncrese)
                .build();
    }

    private static void initializeServices(ApplicationConfig config) {
        try {
            System.out.println(config.toString());
            asyncTaskManager = new AsyncTaskManager(18);

            if (asyncTaskManager == null) {
                throw new RuntimeException("AsyncTaskManager初始化失败");
            }

            log.info("服务组件初始化成功");

        } catch (Exception e) {
            log.error("服务组件初始化失败", e);
            throw new RuntimeException("服务组件初始化失败", e);
        }
    }


    private static DataStream<JsonNode> createSourceStream(FlinkEnv flinkEnv, StreamTableEnvironment tEnv,
            ObjectMapper mapper, Boolean isIncrese, ApplicationConfig config, ParameterTool parameterTool) {
        SourceFactMap sourceFactMap = new SourceFactMap();
        String request = parameterTool.get("sql", "");
        if (isIncrese) {
            final Source<String, ?, ?> Source =
                    BusinessUtils.getMySQLSource("t201_ticket", config.getMysqlWorkDBConf(),
                            new JsonDebeziumDeserializationSchema());
            return flinkEnv.env().fromSource(
                            Source,
                            WatermarkStrategy.noWatermarks(),
                            "01-201Source"
                    ).setParallelism(parallelism)
                    .process(new ProcessFunction<String, JsonNode>() {
                        @Override
                        public void processElement(
                                String s,
                                ProcessFunction<String, JsonNode>.Context context,
                                Collector<JsonNode> collector
                        ) throws JsonProcessingException {
                            final JsonNode data;
                            data = mapper.readValue(s, JsonNode.class);
                            final JsonNode after = data.get("after");
                            if (after != null && after.hasNonNull("ticket_id") && after.get("status").asInt() == 3) {
                                collector.collect(after);
                            }
                        }
                    }).map(sourceFactMap);
        } else {
            // 重刷逻辑：使用BatchJDBCSource进行分批查询
            String QUERY_SQL = String.format("%s", request);

            // 创建BatchJDBCSource实例
            BatchJDBCSource batchSource = new BatchJDBCSource(
                    QUERY_SQL,
                    config.getMysqlWorkDBConf(),
                    DatabaseEnum.MYSQL,
                    Integer.valueOf(parameterTool.get("batchSize", "-1")),
                    Integer.valueOf(parameterTool.get("batchIntervalMs", "-1"))
            );

            // 创建数据流
            DataStream<JsonNode> sourceStream = flinkEnv.env()
                    .addSource(batchSource)
                    .map(sourceFactMap)
                    .name("BatchJDBCSource-DataRefresh")
                    .setParallelism(1); // 设置并行度为1，确保顺序处理

            // 保存BatchJDBCSource引用
            batchJDBCSource = batchSource;

            return sourceStream;
        }

    }


    /**
     * Step 4: GPT处理 - 使用ProcessFunction with Rich functionality
     */
    private static SingleOutputStreamOperator<JsonNode> buildGptStream(
            SingleOutputStreamOperator<JsonNode> extractStream,
            String modelUrl, String modelToken, String similarTicketPrompt, Integer similarGenerateNum,
            ObjectMapper mapper, Boolean isGenerate) {

        return extractStream
                .process(new LLmProcess(isGenerate, mapper, similarTicketPrompt, similarGenerateNum, modelUrl,
                        modelToken))
                .name("04-GptProcess")
                .disableChaining()
                .setParallelism(4);


    }

    /**
     * Step 5: GPT响应解析
     */
    private static SingleOutputStreamOperator<Tuple2<JsonNode, DirectLLMResponse>> buildGptParsingStream(
            SingleOutputStreamOperator<JsonNode> gptStream) {

        MapFunction<JsonNode, Tuple2<JsonNode, DirectLLMResponse>> mapFunction = new MapFunction<JsonNode, Tuple2<JsonNode, DirectLLMResponse>>() {
            @Override
            public Tuple2<JsonNode, DirectLLMResponse> map(JsonNode jsonNode) throws Exception {
                if (!jsonNode.has("LLM_Res") || jsonNode.get("LLM_Res").isNull()) {
                    String ticketId = getTextValue(jsonNode, "ticket_id", "");
                    log.warn("GPT响应为空: ticket_id={}", ticketId);
                    return Tuple2.of(jsonNode, new DirectLLMResponse());
                }

                String llmRes = getTextValue(jsonNode, "LLM_Res", "");
                DirectLLMResponse response = DirectResponseParser.parseResponse(llmRes);
                return Tuple2.of(jsonNode, response);
            }
        };

        return gptStream
                .map(mapFunction)
                .name("05-GptParsing")
                .disableChaining()
                .setParallelism(parallelism);
    }


    /**
     * Step 7: 相似问题构建
     */
    private static SingleOutputStreamOperator<Tuple2<JsonNode, SimilarSearchRequest>> buildSimilarQuestionsStream(
            SingleOutputStreamOperator<Tuple2<JsonNode, DirectLLMResponse>> ticketInfoStream, ObjectMapper mapper) {

        return ticketInfoStream
                .map(new SimilarBuildMap(mapper, idWorker))
                .name("06-SimilarQuestionsBuild")
                .setParallelism(parallelism);
    }


    /**
     * Step 9: 异步写ansearch - 使用ProcessFunction复用客户端
     * 根据后端超时60秒，设置客户端55秒超时
     */
    private static SingleOutputStreamOperator<Tuple2<JsonNode, SimilarSearchRequest>> buildAsyncAnswerSearchStream(
            SingleOutputStreamOperator<Tuple2<JsonNode, SimilarSearchRequest>> filteredStream) {

        return filteredStream.process(
                        new ProcessFunction<Tuple2<JsonNode, SimilarSearchRequest>, Tuple2<JsonNode, SimilarSearchRequest>>() {

                            private transient ObjectMapper objectMapper;
                            private transient HttpClientUtils clientUtils;
                            private transient AsyncTaskManager localAsyncTaskManager;

                            @Override
                            public void open(Configuration parameters) throws Exception {
                                this.objectMapper = new ObjectMapper();
                                clientUtils = HttpClientUtils.builder()
                                        .connectTimeout(5000)            // 5秒连接超时
                                        .socketTimeout(55000)            // 55秒读取超时
                                        .maxRetries(3)                   // 重试3次
                                        .retryIntervalMs(10000)   // 重试间隔10秒
                                        .build();

                                // 在每个TaskManager中初始化AsyncTaskManager
                                try {
                                    localAsyncTaskManager = new AsyncTaskManager(30);
                                    log.info("AsyncTaskManager initialized successfully in ProcessFunction");
                                } catch (Exception e) {
                                    log.error("Failed to initialize AsyncTaskManager in ProcessFunction", e);
                                    localAsyncTaskManager = null;
                                }
                            }

                            @Override
                            public void processElement(
                                    Tuple2<JsonNode, SimilarSearchRequest> input,
                                    Context context,
                                    Collector<Tuple2<JsonNode, SimilarSearchRequest>> collector) throws Exception {

                                SimilarSearchRequest searchRequest = input.f1;
                                JsonNode originalData = input.f0;
                                String ticketId = getTextValue(originalData, "ticket_id");
                                String titles = searchRequest.getData().stream().map(i -> i.getFields().getTitle())
                                        .collect(Collectors.joining(","));

                                String auri = PolarisUtils.getOneInstance("Production", "trpc.ansearch.apigw_inter.SearchAPIHttp");
                                String addUrl = auri + "/add/data";
                                // 安全获取相似问题数量，避免空指针异常
                                int similarQuestionCount =
                                        (searchRequest.getData() != null) ? searchRequest.getData().size() : 0;
                                log.info("异步写入ansearch: ticket_id={}, 相似问题数量={}",
                                        ticketId, similarQuestionCount);

                                // 异步发送到ansearch，不阻塞PG写入
                                if (localAsyncTaskManager != null && searchRequest != null && clientUtils != null
                                        && addUrl != null) {
                                    try {
                                        if (!titles.trim().equals("")) {
                                            localAsyncTaskManager.submitAnswerSearchTask(searchRequest, clientUtils, addUrl,
                                                    ticketId);
                                            log.debug("异步任务提交成功: ticket_id={}", ticketId);
                                        } else {
                                            log.debug("异步任务titles为空: ticket_id={}", ticketId);
                                        }

                                    } catch (Exception e) {
                                        log.error("异步任务提交失败: ticket_id={}, error={}", ticketId, e.getMessage(), e);
                                    }
                                } else {
                                    log.warn(
                                            "跳过异步任务提交，参数检查失败: localAsyncTaskManager={}, searchRequest={}, clientUtils={}, addUrl={}",
                                            localAsyncTaskManager != null, searchRequest != null, clientUtils != null,
                                            addUrl != null);
                                }

                                // 立即发送到下游进行PG写入
                                collector.collect(input);
                            }

                            @Override
                            public void close() throws Exception {
                                try {
                                    if (clientUtils != null) {
                                        clientUtils.close();
                                    }
                                } catch (Exception e) {
                                    log.error("Error closing HttpClientUtils", e);
                                }

                                try {
                                    if (localAsyncTaskManager != null) {
                                        localAsyncTaskManager.shutdown();
                                        log.info("AsyncTaskManager shutdown completed");
                                    }
                                } catch (Exception e) {
                                    log.error("Error shutting down AsyncTaskManager", e);
                                }

                                super.close();
                            }
                        })
                .name("07-AsyncAnswerSearch")
                .setParallelism(parallelism);
    }

    private static void setupSinks(SingleOutputStreamOperator<Tuple2<JsonNode, SimilarSearchRequest>> outputStream,
            DatabaseConf db) {

        //正式环境
        EnhancedSinkFunction similarQuestionsSink = new EnhancedSinkFunction(
                String.format("jdbc:postgresql://%s:%d/%s", db.getDbHost(), db.getDbPort(), db.getDbName()),
                db.getUserName(),
                db.getPassword(),
                5  // 调整为更小的批次，便于前期排查问题
        );

        outputStream.addSink(similarQuestionsSink)
                .name("08-PostgreSQLSink")
                .setParallelism(parallelism); // 数据库写入适中并行度

    }

    private static void configureExecution(StreamExecutionEnvironment env, StreamTableEnvironment tEnv) {
        configureStateAndCheckpoint(env);
    }

    // 辅助方法


    // 🔧 修改：统一的关闭钩子
    private static void addUnifiedShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("开始应用程序关闭流程...");

            try {
                // 1. 关闭AsyncTaskManager
                if (asyncTaskManager != null) {
                    log.info("正在关闭AsyncTaskManager...");
                    asyncTaskManager.shutdown();
                    log.info("AsyncTaskManager关闭完成");
                }

                // 2. 关闭ErrorTicketSink
                if (globalErrorTicketSink != null) {
                    log.info("正在关闭ErrorTicketSink...");
                    globalErrorTicketSink.shutdown();
                    log.info("ErrorTicketSink关闭完成");
                }

                // 3. 关闭BatchJDBCSource
                if (batchJDBCSource != null) {
                    log.info("正在关闭BatchJDBCSource...");
                    // 如果BatchJDBCSource有关闭方法的话
                    log.info("BatchJDBCSource关闭完成");
                }

                log.info("应用程序关闭流程完成");

            } catch (Exception e) {
                log.error("关闭过程中发生异常", e);
            }
        }));
    }
}