package com.tencent.andata.smart.access.imp;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.smart.access.deserializer.NotNullKafkaDeserializer;
import com.tencent.andata.smart.access.interfaces.SourceActuator;
import java.util.Objects;
import java.util.Properties;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;


public class GetDataFromKafka implements SourceActuator<DataStream<String>> {

    private String name;
    private MqConf kafkaConf;

    private StreamExecutionEnvironment env;

    public static GetDataFromKafka builder() {
        return new GetDataFromKafka();
    }

    public GetDataFromKafka name(String name) {
        this.name = name;
        return this;
    }

    public GetDataFromKafka fEnv(StreamExecutionEnvironment env) {
        this.env = env;
        return this;
    }

    public GetDataFromKafka kafkaConf(MqConf kafkaConf) {
        this.kafkaConf = kafkaConf;
        return this;
    }

    @Override
    @SneakyThrows
    public DataStream<String> getSourceData() {
        Properties properties = new Properties();
        properties.setProperty("partition.discovery.interval.ms", "1800000");
        properties.setProperty("commit.offsets.on.checkpoint", "true");
        properties.setProperty("enable.auto.commit", "false");
        properties.setProperty("fetch.max.wait.ms", "10000");

        // 注册Kafka数据源
        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers(kafkaConf.getBroker())
                .setTopics(kafkaConf.getTopic())
                .setClientIdPrefix(UUID.randomUUID().toString())
                .setGroupId(kafkaConf.getGroup())
                .setDeserializer(new NotNullKafkaDeserializer())
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setProperties(properties)
                .build();

        return this.env
                .fromSource(source, WatermarkStrategy.noWatermarks(), name)
                .uid(name)
                .name(name)
                .filter(Objects::nonNull);
    }
}