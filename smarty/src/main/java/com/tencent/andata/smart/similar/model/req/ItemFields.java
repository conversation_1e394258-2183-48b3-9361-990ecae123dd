package com.tencent.andata.smart.similar.model.req;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ItemFields {

    private String source_name = "similar_search";
    private String source_type; // ticket/similarity_ticket
    private String source_id;
    private String service_scene_level1_id;
    private String service_scene_level2_id;
    private String service_scene_level3_id;
    private String service_scene_level4_id;
    private String service_scene_checked_id;
    private Integer cloud_type = 3; // 1-私有云 2-公有云 3-全部
    private String index_type = "qa";
    private Integer visible = 3; // 1-内部 2-外部 3-全部
    private String customer_uin;
    private String title;
    private String content;
    private String knowledge; // title+\n+content
    private Integer ticket_create_time;
    private Integer ticket_close_time;
    private String create_time; // "YYYY-MM-DD HH:mm:ss"
    private String update_time; // "YYYY-MM-DD HH:mm:ss"
    private String parent_ticket_id;
    private String parent_ticket_title;

    // 自动生成knowledge字段
    public void generateKnowledge() {
        this.knowledge = (this.title != null ? this.title : "") +
                "\n" +
                (this.content != null ? this.content : "");
    }

    /**
     * 获取create_time的时间戳格式（秒级）
     * 用于DataItem的create_time字段
     */
    public Integer getCreateTimeAsTimestamp() {
        if (this.create_time != null && !this.create_time.isEmpty()) {
            try {
                return (int) LocalDateTime.parse(this.create_time,
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                        .toEpochSecond(ZoneOffset.of("+8"));
            } catch (Exception e) {
                // 解析失败时返回当前时间戳
                return (int) (System.currentTimeMillis() / 1000);
            }
        }
        return (int) (System.currentTimeMillis() / 1000);
    }

    /**
     * 设置当前时间为create_time和update_time
     */
    public void setCurrentTime() {
        String now = LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.create_time = now;
        this.update_time = now;
    }

    /**
     * 验证所有必需字段是否已设置
     */
    public boolean validateRequiredFields() {
        return source_name != null &&
                source_type != null &&
                source_id != null &&
                title != null &&
                content != null;
    }
}