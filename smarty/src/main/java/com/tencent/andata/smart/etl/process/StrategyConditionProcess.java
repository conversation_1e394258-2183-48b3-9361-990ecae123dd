package com.tencent.andata.smart.etl.process;

import static com.tencent.andata.utils.ExceptionWrapperUtil.function;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.utils.AviatorMatchUtils;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * 策略触发判断入口
 */
public class StrategyConditionProcess extends ProcessFunction<String, Strategy> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private ObjectMapper objectMapper;
    private io.vavr.collection.Map<Scene, String> sceneKeyMap;
    private List<Strategy> strategies;

    public StrategyConditionProcess(java.util.List<Strategy> strategies) {
        this.strategies = List.ofAll(strategies);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.objectMapper = new ObjectMapper();
        this.sceneKeyMap = HashMap.of(
                Scene.C2000, "conversation_id",
                Scene.WebIM, "conversation_id",
                Scene.Ticket, "ticket_id",
                Scene.Group, "group_id"
        );
    }

    @Override
    public void processElement(String input, Context context, Collector<Strategy> collector) {
        Try.of(() -> objectMapper.readTree(input))
                .onSuccess(data -> processStrategies(data, collector))
                .onFailure(e -> logger.error(String.format("invalid json data: %s, err_msg: %s", input, e)));
    }

    private void processStrategies(JsonNode data, Collector<Strategy> collector) {
        strategies.filter(strategy -> AviatorMatchUtils.isMatch(data, strategy.condition))
                .map(this::deepCopyStrategy)
                .filter(Objects::nonNull)
                .forEach(strategy -> {
                    setSceneIdentify(data, strategy);
                    collector.collect(evaluateExpressions(strategy));
                });
    }

    private Strategy deepCopyStrategy(Strategy strategy) {
        return Option.of(strategy)
                .map(function(s -> objectMapper.readValue(objectMapper.writeValueAsString(strategy), Strategy.class)))
                .map(this::removeStrategyAnalyze)
                .getOrNull();
    }

    private void setSceneIdentify(JsonNode data, Strategy strategy) {
        JsonNode sceneIdentify = data.get(this.sceneKeyMap.get(strategy.scene).get());
        switch (strategy.scene) {
            case Ticket:
                strategy.sceneIdentify = String.valueOf(sceneIdentify.asLong());
                break;
            case WebIM:
            case Group:
            case C2000:
                strategy.sceneIdentify = sceneIdentify.asText();
                break;
        }
        strategy.trigger.data = new JsonNode[]{data};
    }

    private Strategy evaluateExpressions(Strategy strategy) {
        AtomicReference<JsonNode> curr = new AtomicReference<>(objectMapper.convertValue(strategy, JsonNode.class));
        Option.of(strategy.chunk.aviatorExpressions)
                .filter(arr -> arr.length > 0)
                .flatMap(arr -> Option.of(List.of(arr)))
                .getOrElse(List.of())
                .filter(Objects::nonNull)
                .forEach(expression -> curr.set(AviatorMatchUtils.executeExpression(curr.get(), expression)));

        return objectMapper.convertValue(curr.get(), Strategy.class);
    }

    private JsonNode convertJsonNodeArray(Strategy strategy) {
        // 计算 aviator 表达式需要将trigger.data Object[]数组转换为JsonNode数组
        strategy.trigger.data = List.of(strategy.trigger.data)
                .map(function(item -> objectMapper.readTree(item.toString())))
                .toJavaArray(JsonNode[]::new);

        return objectMapper.convertValue(strategy, JsonNode.class);
    }

    private Strategy removeStrategyAnalyze(Strategy strategy) {
        if (strategy.scene == Scene.Ticket && strategy.trigger != null
                && strategy.trigger.data != null && strategy.trigger.data.length > 0) {

            // 安全获取service_channel值
            JsonNode serviceChannelNode = strategy.trigger.data[0].get("service_channel");
            if (!serviceChannelNode.isMissingNode()) {
                int serviceChannel = serviceChannelNode.asInt();
                if (serviceChannel == 27) {
                    // 过滤掉Service_Specifications分析项
                    strategy.analyzes = Arrays.stream(strategy.analyzes)
                            .filter(analyze -> !"Service_Specifications".equals(analyze.name))
                            .toArray(Analyze[]::new);
                }
            }
        }

        return strategy;
    }
}