package com.tencent.andata.smart.strategy.chunk.combine;

import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.stream.StreamSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

@Slf4j
public class OperationJsonCombiner implements JsonCombiner {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Override
    public String combine(HashMap<String, String> resMap) throws JsonProcessingException {
        ObjectNode result = MAPPER.createObjectNode();
        ArrayNode groupMsg = result.putArray("group_msg");
        ArrayNode operationData = result.putArray("operation_data");
        io.vavr.collection.List.ofAll(resMap.values())
            .map(value -> Try.of(() -> MAPPER.readTree(value)))
            .filter(Try::isSuccess)
            .map(Try::get)
            .forEach(node -> {
                Option.of(node.get("operation_data"))
                    .forEach(data -> StreamSupport.stream(data.spliterator(), false)
                        .forEach(operationData::add));

                Option.of(node.get("msg_data"))
                    .forEach(data -> StreamSupport.stream(data.spliterator(), false)
                        .forEach(groupMsg::add));
            });

        return MAPPER.writeValueAsString(result);
    }
}