package com.tencent.andata.smart.utils;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.lookup.CustomerInfoQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户信息工具类
 * 提供用户(UIN)到公司名称的查询和缓存功能
 */
public class CustomerInfoUtils {

    private static final String ERROR_MSG = "CustomerInfoUtils get info from db error: %s, id: %s";
    public static FlinkLog logger = FlinkLog.getInstance();

    // 最大缓存条目数
    private static final int MAX_CACHE_SIZE = 100000;
    // 缓存条目过期时间（分钟）
    private static final int CACHE_EXPIRATION_MINUTES = 24 * 60;

    // 使用ConcurrentHashMap存储缓存数据
    private static final Map<Long, CacheEntry> customerInfoCache = new ConcurrentHashMap<>();

    // 缓存统计信息
    private static final AtomicLong hitCount = new AtomicLong();
    private static final AtomicLong missCount = new AtomicLong();
    private static final AtomicLong loadCount = new AtomicLong();

    // 定时清理过期缓存的线程池
    private static final ScheduledExecutorService cleanupExecutor = new ScheduledThreadPoolExecutor(1,
            r -> {
                Thread thread = new Thread(r, "customer-info-cache-cleanup");
                thread.setDaemon(true);
                return thread;
            });

    public static CustomerInfoQuery dbQuery;

    // 缓存条目，记录值和创建时间
    private static class CacheEntry {

        final String value;
        final long createTime;

        CacheEntry(String value) {
            this.value = value;
            this.createTime = System.currentTimeMillis();
        }

        boolean isExpired() {
            long expirationMs = TimeUnit.MINUTES.toMillis(CACHE_EXPIRATION_MINUTES);
            return System.currentTimeMillis() - createTime > expirationMs;
        }
    }

    static {
        // 初始化数据库连接
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        DatabaseConf datawareDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.pgsql.dataware_r")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        dbQuery = Try.of(() -> {
            CustomerInfoQuery query = new CustomerInfoQuery(DatabaseEnum.PGSQL, datawareDbConf);
            query.open();
            return query;
        }).getOrElseThrow(e -> new RuntimeException(e));

        // 启动定时清理任务，每分钟检查一次过期缓存
        cleanupExecutor.scheduleAtFixedRate(() -> {
            try {
                int removed = cleanupExpiredEntries();
                if (removed > 0) {
                    logger.info("Cleaned up " + removed + " expired cache entries");
                }
            } catch (Exception e) {
                logger.error("Error during cache cleanup: {}", e.getMessage());
            }
        }, 1, 1, TimeUnit.DAYS);
    }

    /**
     * 获取公司名称
     * 首先尝试从缓存获取，如果缓存未命中则从数据库查询
     * 实现了自动过期功能，提高缓存数据的准确性
     *
     * @param parms 参数列表，parms.get(0)为uin，parms.get(1)为owner_uin
     * @return 包含公司名称的Option
     */
    public static Option<String> getCompanyName(List<Long> parms) {
        if (parms == null || parms.size() < 2) {
            logger.error("Invalid parameters for getCompanyName");
            return Option.none();
        }

        Long uin = parms.get(0);
        Long ownerUin = parms.get(1);

        // 检查缓存大小，如果超过最大限制，执行一次清理
        if (customerInfoCache.size() > MAX_CACHE_SIZE) {
            cleanupExpiredEntries();
        }

        // 先检查uin是否在缓存中
        CacheEntry cachedEntry = customerInfoCache.get(uin);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            hitCount.incrementAndGet();
            return Option.of(cachedEntry.value);
        } else if (cachedEntry != null) {
            // 缓存已过期，移除
            customerInfoCache.remove(uin);
        }

        // 再检查owner_uin是否在缓存中
        cachedEntry = customerInfoCache.get(ownerUin);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            hitCount.incrementAndGet();
            // 如果通过owner_uin找到，同时更新uin的缓存
            customerInfoCache.put(uin, cachedEntry);
            return Option.of(cachedEntry.value);
        } else if (cachedEntry != null) {
            // 缓存已过期，移除
            customerInfoCache.remove(ownerUin);
        }

        // 缓存未命中或已过期，从数据库查询
        missCount.incrementAndGet();
        return Try.of(() -> {
                    loadCount.incrementAndGet();
                    String customerName = dbQuery.query(parms);
                    // 只缓存非空结果
                    if (customerName != null && !customerName.isEmpty()) {
                        CacheEntry newEntry = new CacheEntry(customerName);
                        customerInfoCache.put(uin, newEntry);
                        // 只有当owner_uin与uin不同时才额外缓存
                        if (!uin.equals(ownerUin)) {
                            customerInfoCache.put(ownerUin, newEntry);
                        }
                    }
                    return customerName;
                })
                .onFailure(e -> logger.error(String.format(ERROR_MSG, e, parms)))
                .toOption();
    }

    /**
     * 清除所有缓存
     */
    public static void clearCache() {
        customerInfoCache.clear();
        hitCount.set(0);
        missCount.set(0);
        loadCount.set(0);
        logger.info("CustomerInfoUtils cache cleared");
    }

    /**
     * 清除过期的缓存条目
     *
     * @return 移除的条目数量
     */
    private static int cleanupExpiredEntries() {
        int count = 0;
        for (Map.Entry<Long, CacheEntry> entry : customerInfoCache.entrySet()) {
            if (entry.getValue().isExpired()) {
                customerInfoCache.remove(entry.getKey());
                count++;
            }
        }
        return count;
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息字符串
     */
    public static String getCacheStats() {
        return String.format(
                "CacheStats{hit=%d, miss=%d, load=%d, size=%d}",
                hitCount.get(), missCount.get(), loadCount.get(), customerInfoCache.size()
        );
    }

    /**
     * 关闭资源
     * 应在应用关闭时调用
     */
    public static void shutdown() {
        cleanupExecutor.shutdown();
        Try.run(() -> dbQuery.close())
                .onFailure(e -> logger.error("Error closing dbQuery: {}", e.getMessage()));
    }
}