package com.tencent.andata.smart.access.repository;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.access.model.TicketInfo;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 基于JDBC的工单仓库实现
 * 通过JDBC查询数据库获取工单信息
 */
public class JdbcTicketRepository implements TicketRepository {

    private static final String TABLE_NAME = "t201_ticket";
    private static final FlinkLog logger = FlinkLog.getInstance();

    private static final List<String> SELECT_FIELDS = Collections.unmodifiableList(Arrays.asList(
            "service_scene_checked",
            "service_channel",
            "current_operator",
            "service_scene",
            "fact_assign",
            "create_time",
            "responsible",
            "company_id",
            "owner_uin",
            "group_id",
            "priority",
            "question",
            "status",
            "post",
            "name",
            "uin",
            "title"
    ));

    private final DatabaseConf dbConf;
    private HashMapJDBCLookupQuery ticketQuery;

    /**
     * 构造函数
     *
     * @param dbConf 数据库配置
     */
    public JdbcTicketRepository(DatabaseConf dbConf) {
        this.dbConf = dbConf;
    }

    @Override
    public void open() throws Exception {
        JDBCSqlBuilderImpl sqlBuilder = JDBCSqlBuilderImpl.builder()
                .tableName(TABLE_NAME)
                .selectField(SELECT_FIELDS)
                .conditionKeyList(Collections.singletonList("ticket_id"))
                .limit(1);

        ticketQuery = new HashMapJDBCLookupQuery(DatabaseEnum.MYSQL, dbConf, sqlBuilder);
        ticketQuery.open();
        logger.info("[JdbcTicketRepository] Successfully opened database connection");
    }

    @Override
    public Optional<TicketInfo> findById(long ticketId) {
        try {
            HashMap<String, Object> params = new HashMap<>(1);
            params.put("ticket_id", ticketId);

            List<HashMap<String, Object>> results = ticketQuery.query(params);
            if (results == null || results.isEmpty()) {
                logger.warn("[JdbcTicketRepository] No ticket found for id: " + ticketId);
                return Optional.empty();
            }

            Map<String, Object> result = results.get(0);
            TicketInfo ticketInfo = mapToTicketInfo(ticketId, result);
            return Optional.of(ticketInfo);
        } catch (Exception e) {
            logger.error("[JdbcTicketRepository] Error querying ticket: " + ticketId + " - " + e.getMessage());
            return Optional.empty();
        }
    }

    @Override
    public void close() throws Exception {
        if (ticketQuery != null) {
            ticketQuery.close();
            logger.info("[JdbcTicketRepository] Database connection closed");
        }
    }

    /**
     * 将查询结果映射为TicketInfo对象
     *
     * @param ticketId 工单ID
     * @param result 查询结果Map
     * @return 构建的TicketInfo对象
     */
    private TicketInfo mapToTicketInfo(long ticketId, Map<String, Object> result) {
        return new TicketInfo.Builder(ticketId)
                .serviceSceneChecked((Integer) result.get("service_scene_checked"))
                .serviceChannel((Long) result.get("service_channel"))
                .currentOperator((String) result.get("current_operator"))
                .serviceScene((Integer) result.get("service_scene"))
                .factAssign((Integer) result.get("fact_assign"))
                .createTime((Timestamp) result.get("create_time"))
                .responsible((String) result.get("responsible"))
                .companyId((Integer) result.get("company_id"))
                .ownerUin((BigInteger) result.get("owner_uin"))
                .groupId((String) result.get("group_id"))
                .priority((Integer) result.get("priority"))
                .question((String) result.get("question"))
                .status((Integer) result.get("status"))
                .post((Integer) result.get("post"))
                .name((String) result.get("name"))
                .uin((BigInteger) result.get("uin"))
                .title((String) result.get("title"))
                .build();
    }
}