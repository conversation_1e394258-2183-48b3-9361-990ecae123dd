package com.tencent.andata.smart.enums;

import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.Getter;

/**
 * WebIM操作类型枚举
 */
@Getter
public enum WebIMOperationType {
    COMPLAIN("Complain", "投诉"),
    ASSIGN("AssignConversation", "转单"),
    CREATE("CreateConversation", "创建会话"),
    SEND_ZX_MSG("SendZXMsg", "坐席侧发送消息"),
    SEND_USER_MSG("SendUserMsg", "用户侧发送消息"),
    APPLY_FINISH("ApplyFinishConversation", "待客户确认结单"),
    AUTO_FINISH("AutoFinishConversation", "超过3天系统自动结单"),
    SEND_CHAT_CALLBACK_MSG("SendChatCallbackMsg", "企微回调发消息"),
    USER_ACTIVELY_FINISH("FinishUserConversation", "客户侧结束会话");


    private final String code;
    private final String desc;

    WebIMOperationType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Option<WebIMOperationType> fromCode(String code) {
        return Option.of(List.of(values())
                             .filter(t -> t.code.equals(code))
                             .getOrNull());
    }
} 