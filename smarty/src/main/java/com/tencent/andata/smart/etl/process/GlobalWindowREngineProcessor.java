package com.tencent.andata.smart.etl.process;

import com.tencent.andata.struct.regnine.REngineMessage;
import org.apache.flink.streaming.api.functions.windowing.ProcessAllWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

/**
 * 全局窗口处理器
 * 用于处理规则引擎消息的全局窗口，确保在写入Kafka之前有足够的延迟
 */
public class GlobalWindowREngineProcessor extends ProcessAllWindowFunction<REngineMessage, REngineMessage, TimeWindow> {

    @Override
    public void process(Context context, Iterable<REngineMessage> elements, Collector<REngineMessage> out) {
        // 直接转发所有消息
        elements.forEach(out::collect);
    }
}