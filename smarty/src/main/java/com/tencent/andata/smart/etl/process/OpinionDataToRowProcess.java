package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

import java.sql.Timestamp;

public class OpinionDataToRowProcess extends ProcessFunction<Tuple2<String, String>, Row> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private ObjectMapper objectMapper;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void processElement(Tuple2<String, String> in, ProcessFunction<Tuple2<String, String>, Row>.Context context, Collector<Row> collector) throws Exception {
        Row res = Row.withNames(RowKind.INSERT);
        JsonNode jsonData = this.objectMapper.readTree(in.f1);
        res.setField("pk", jsonData.get("pk").asText());
        res.setField("source", jsonData.get("scene").asText());
        res.setField("source_id", jsonData.get("scene_identify").asText());
        res.setField("ticket_id", jsonData.get("ticket_id").longValue());
        res.setField("service_scene_level1_name", jsonData.get("service_scene_level1_name").asText());
        res.setField("service_scene_level2_name", jsonData.get("service_scene_level2_name").asText());
        res.setField("service_scene_level3_name", jsonData.get("service_scene_level3_name").asText());
        res.setField("service_scene_level4_name", jsonData.get("service_scene_level4_name").asText());
        res.setField("service_channel", jsonData.get("service_channel").asText());
        res.setField("current_operator", jsonData.get("current_operator").asText());
        res.setField("customer_name", jsonData.get("customer_name").asText());
        res.setField("fact_assign", jsonData.get("fact_assign").asText());
        res.setField("url", jsonData.get("url").asText());
        res.setField("responsible", jsonData.get("responsible").asText());
        res.setField("risk_reason", jsonData.get("risk_reason").asText());
        res.setField("risk_level", jsonData.get("risk_level").asText());
        res.setField("priority", jsonData.get("priority").asText());
        res.setField("title", jsonData.get("title").asText().replaceAll("\u0000", ""));
        res.setField("question", jsonData.get("question").asText().replaceAll("\u0000", ""));
        res.setField("trigger_time", new Timestamp(jsonData.get("trigger_time").longValue()));
        res.setField("risk_operation_id", jsonData.get("risk_operation_id").asText());
        res.setField("trigger_risk_type", jsonData.get("trigger_risk_type").asText());
        res.setField("duty_responsible", jsonData.get("duty_responsible").asText());
        res.setField("uin", jsonData.get("uin").longValue());
        res.setField("trigger_content", jsonData.get("trigger_content").asText().replaceAll("\u0000", ""));
        res.setField("event_time", new Timestamp(jsonData.get("event_time").longValue()));
        res.setField("post", jsonData.get("post").asText());
        res.setField("company_id", jsonData.get("company_id").asText());
        res.setField("msg_seq", jsonData.get("msg_seq").intValue());

        collector.collect(res);
    }
}