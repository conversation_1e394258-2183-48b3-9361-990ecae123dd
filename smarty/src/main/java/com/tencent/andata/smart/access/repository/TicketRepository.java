package com.tencent.andata.smart.access.repository;

import com.tencent.andata.smart.access.model.TicketInfo;
import java.util.Optional;

/**
 * 工单数据访问接口
 * 定义与工单相关的数据访问操作
 */
public interface TicketRepository {

    /**
     * 根据工单ID查询工单信息
     *
     * @param ticketId 工单ID
     * @return 包含工单信息的Optional对象，如果不存在则返回empty
     */
    Optional<TicketInfo> findById(long ticketId);

    /**
     * 打开数据库连接
     *
     * @throws Exception 如果打开连接失败
     */
    void open() throws Exception;

    /**
     * 关闭数据库连接
     *
     * @throws Exception 如果关闭连接失败
     */
    void close() throws Exception;
}