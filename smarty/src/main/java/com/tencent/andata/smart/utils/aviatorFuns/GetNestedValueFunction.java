package com.tencent.andata.smart.utils.aviatorFuns;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import java.util.List;
import java.util.Map;

public class GetNestedValueFunction extends AbstractFunction {
    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject pathObj) {
        String path = FunctionUtils.getStringValue(pathObj, env);

        // 使用正则表达式分割路径，处理数组索引和属性访问
        String[] parts = path.split("\\.");
        Object current = env;

        for (String part : parts) {
            if (current == null) {
                return FunctionUtils.wrapReturn(null);
            }

            // 检查是否包含数组访问
            if (part.contains("[") && part.contains("]")) {
                // 分离属性名和数组索引
                String[] arrayAccess = part.split("\\[");
                String propertyName = arrayAccess[0];
                String indexStr = arrayAccess[1].replace("]", "");

                // 先获取属性值（如果有的话）
                if (!propertyName.isEmpty()) {
                    if (current instanceof Map) {
                        current = ((Map<?, ?>) current).get(propertyName);
                    } else {
                        return FunctionUtils.wrapReturn(null);
                    }
                }

                // 处理数组访问
                if (current instanceof List) {
                    try {
                        int index = Integer.parseInt(indexStr);
                        List<?> list = (List<?>) current;
                        if (index >= 0 && index < list.size()) {
                            current = list.get(index);
                        } else {
                            return FunctionUtils.wrapReturn(null);
                        }
                    } catch (NumberFormatException e) {
                        return FunctionUtils.wrapReturn(null);
                    }
                } else {
                    return FunctionUtils.wrapReturn(null);
                }
            } else {
                // 普通属性访问
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(part);
                } else {
                    return FunctionUtils.wrapReturn(null);
                }
            }
        }

        return FunctionUtils.wrapReturn(current);
    }

    @Override
    public String getName() {
        return "getNested";
    }
}