package com.tencent.andata.smart.utils.util;

import com.tencent.andata.smart.utils.AviatorMatchUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class ArrayNodeConverter implements JsonNodeConverter {

    @Override
    public boolean canConvert(JsonNode node) {
        return node.isArray();
    }

    @Override
    public Object convert(JsonNode node) {
        List<Object> list = new ArrayList<>();
        for (JsonNode element : node) {
            list.add(AviatorMatchUtils.convertJsonNodeToObject(element));
        }
        return list;
    }
}