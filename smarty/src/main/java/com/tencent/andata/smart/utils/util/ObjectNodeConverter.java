package com.tencent.andata.smart.utils.util;

import com.tencent.andata.smart.utils.AviatorMatchUtils;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class ObjectNodeConverter implements JsonNodeConverter {

    @Override
    public boolean canConvert(JsonNode node) {
        return node.isObject();
    }

    @Override
    public Object convert(JsonNode node) {
        Map<String, Object> nestedMap = new HashMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            nestedMap.put(field.getKey(), AviatorMatchUtils.convertJsonNodeToObject(field.getValue()));
        }
        return nestedMap;
    }
}