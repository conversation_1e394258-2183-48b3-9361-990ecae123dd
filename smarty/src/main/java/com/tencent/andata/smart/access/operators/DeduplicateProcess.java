package com.tencent.andata.smart.access.operators;

import com.tencent.andata.smart.utils.util.OptimizationHyperLogLog;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

public class DeduplicateProcess extends KeyedProcessFunction<Long, JsonNode, JsonNode> {
    private ValueState<OptimizationHyperLogLog> hyperLogLogState;
    @Override
    public void open(Configuration parameters) {
        ValueStateDescriptor<OptimizationHyperLogLog> descriptor = new ValueStateDescriptor<>("hyperLogLogState", OptimizationHyperLogLog.class);

        // 保存所有流水状态
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.hours(8))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        descriptor.enableTimeToLive(ttlConfig);
        hyperLogLogState = getRuntimeContext().getState(descriptor);
    }

    @Override
    public void processElement(JsonNode json, Context ctx, Collector<JsonNode> out) throws Exception {
        // 获取或初始化 HyperLogLog
        OptimizationHyperLogLog hyperLogLog = hyperLogLogState.value();
        if (hyperLogLog == null) {
            hyperLogLog = new OptimizationHyperLogLog(0.001); // 精度参数
        }

        // 提取 operation_id 作为去重的依据
        String operationId = json.get("operation_id").asText();

        // 记录当前的近似去重数量
        long currentCount = hyperLogLog.cardinality();

        // 更新 HyperLogLog
        hyperLogLog.offer(operationId);
        hyperLogLogState.update(hyperLogLog);

        // 只有在当前计数与更新后的计数不同的情况下，才输出
        if (currentCount != hyperLogLog.cardinality()) {
            ((ObjectNode) json).put("data_type", "ticket_operation");
            out.collect(json); // 输出去重后的数据
        }
    }
}