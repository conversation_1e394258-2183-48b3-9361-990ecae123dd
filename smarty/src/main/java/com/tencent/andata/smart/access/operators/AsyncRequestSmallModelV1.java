package com.tencent.andata.smart.access.operators;

import static com.tencent.andata.utils.ExceptionWrapperUtil.function;
import static org.apache.flink.util.concurrent.Executors.directExecutor;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.AsyncHttpClient;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.util.EntityUtils;

public class AsyncRequestSmallModelV1 extends RichAsyncFunction<JsonNode, JsonNode> {

    private static CloseableHttpAsyncClient httpClient;

    private static final FlinkLog logger = FlinkLog.getInstance();

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final String URL = "http://9.149.135.6:8080/v1/plugin/call";

    private static final List<String> MODELS = Arrays.asList(
            "time_effect_urge.default",
            "impact_service.default",
            "opinion_subdivision.default",
            "emotion_recognition.default"
    );

    @Override
    public void open(Configuration parameters) {
        httpClient = AsyncHttpClient.getHttpClient(300000, 300000, 100, 20);
        httpClient.start();
        logger.info("AsyncRequestSmallModel initialized.");
    }

    @Override
    public void asyncInvoke(JsonNode input, ResultFuture<JsonNode> resultFuture) {
        List<CompletableFuture<String>> futures = processInput(input);
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                         .thenAccept(v -> {
                             String result = futures.stream()
                                                    .map(CompletableFuture::join)
                                                    .collect(Collectors.joining(","));
                             handleResult(input, result, resultFuture);
                         });
    }

    private List<CompletableFuture<String>> processInput(JsonNode input) {
        String customerMessage = getCustomerMessage(input);
        if (StringUtils.isEmpty(customerMessage) || "null".equalsIgnoreCase(customerMessage)) {
            return Collections.singletonList(CompletableFuture.completedFuture(""));
        }

        return MODELS.stream()
                     .map(function(model -> fetchModelResult(customerMessage, model)))
                     .collect(Collectors.toList());
    }

    private CompletableFuture<String> fetchModelResult(String message, String model) throws Exception {
        HttpPost httpPost = createHttpPost(message, model);
        Future<HttpResponse> futureResponse = httpClient.execute(httpPost, null);
        return CompletableFuture.supplyAsync(() -> futureResponse, directExecutor())
                                .thenApply(function(response -> EntityUtils.toString(response.get().getEntity())))
                                .thenApply(function(response -> RiskType.getDescriptionByUuid(
                                        MAPPER.readTree(response)
                                              .get("data")
                                              .get("sentiment")
                                              .asText()))
                                );
    }

    private HttpPost createHttpPost(String message, String model) throws Exception {
        Map<String, Object> requestParams = createRequestParams(message, model);
        String jsonParams = MAPPER.writeValueAsString(requestParams);
        return AsyncHttpClient.doHttpPost(URL, jsonParams, ContentType.APPLICATION_JSON);
    }

    private Map<String, Object> createRequestParams(String message, String model) {
        Map<String, Object> markInfo = new HashMap<>();
        markInfo.put("CallerUID", "multimodel__Activity_1d50985");
        markInfo.put("PluginVersion", "v0.0.1");
        markInfo.put("PluginUID", model);

        Map<String, Object> requestDetail = new HashMap<>();
        requestDetail.put("request_id", UUID.randomUUID());
        requestDetail.put("sentence", new String[]{message});

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("RequestDetail", requestDetail);
        requestBody.put("MarkInfo", markInfo);

        return requestBody;
    }

    private void handleResult(JsonNode input, String result, ResultFuture<JsonNode> resultFuture) {
        ((ObjectNode) input).put("risk_type", result);
        logger.info(String.format("[AsyncRequestSmallModelProcess]: Model processing result: "
                + "data: %s, result: %s", input, result));

        resultFuture.complete(Collections.singleton(input));
    }

    private String getCustomerMessage(JsonNode json) {
        if (!json.has("data_type")) {
            return "";
        }

        String dataType = json.get("data_type").asText();
        String operationType = getOperationType(json, dataType);
        String userSendMessage = getUserSendMessage(json, dataType);

        switch (dataType) {
            case "ticket_operation":
                return "1".equals(operationType) ? userSendMessage : "";
            case "group":
            case "c2000":
                return "客户".equals(operationType) ? userSendMessage : "";
            case "webim":
                return "SendUserMsg".equals(operationType) || "SendChatCallbackMsg".equals(operationType) ? userSendMessage : "";
            default:
                return "";
        }
    }

    private String getUserSendMessage(JsonNode message, String dataType) {
        switch (dataType) {
            case "group":
            case "c2000":
            case "webim":
                return message.get("display_content").asText();
            case "ticket_operation":
                return message.get("extern_reply").asText();
            default:
                return "";
        }
    }

    private String getOperationType(JsonNode message, String dataType) {
        switch (dataType) {
            case "group":
            case "c2000":
                return message.get("sender_type").asText();
            case "webim":
                return message.get("rpc_name").asText();
            case "ticket_operation":
                return message.get("operator_type").asText();
            default:
                return "";
        }
    }

    public enum RiskType {
        INTERNAL_COMPLAINT("33874b39-602c-431c-b477-06159f9df7cc", "内部投诉"),
        PUBLIC_OPINION_SUPERVISION("2399f468-a596-45d8-8e19-0e556459f7a2", "舆情监管"),
        LITIGATION("b5cd807c-7b4b-4c09-9f30-8e47cdaf5857", "已诉讼"),
        GENERAL_REMINDER("0bc8d710-65df-4bb0-ac76-3f0cfb0f2ccc", "一般催单"),
        BUSINESS_SUSPECTED_IMPACT("fa7fb804-9745-42d0-8f98-70fc40ca860e", "业务疑似影响"),
        BUSINESS_IMPACT("090bba0b-2ae4-4c43-a4fe-2ee377900a7e", "业务影响"),
        NEGATIVE_EMOTION("ccd1c748-490f-4bf4-8723-4a35cf172b4e", "负面情绪"),
        URGENT_REMINDER("f75c2fc9-0560-4127-848f-23b72336dbb5", "紧急催单"),
        REPRODUCTION("4f3028f0-c1c8-436d-bbbe-efc728ae7794", "复现"),
        COMPENSATION_ISSUE("1c5687a8-7d77-4b3a-b6d0-9628be088ed7", "赔偿问题"),
        EMOTIONAL_EXCITEMENT("09e0aa91-7362-495d-8479-97c32ab42cdb", "情绪激动"),
        THREAT_CUT_VOLUME("7a40380b-bd56-4bf3-aa28-62acd2f9aa02", "威胁切量"),
        SELF_HARM_BEHAVIOR("6fe6dcf7-a8d7-45d5-8d6e-76b1e89869da", "自残行为"),
        DATA_LOSS("80fc4b0e-0348-4dc8-a514-5c14e1132e10", "数据丢失"),
        BILLING_ISSUE("5446978e-48cb-4c12-8cc6-5a418221d416", "计费问题"),
        POSITIVE_EMOTION("9a125007-e918-4fc9-bece-c9b50a6bf2a6", "正面情绪"),
        WECHAT_BUSINESS_AFFECTED("8f9d0e08-15a5-476b-b4d1-d79a6d3cecda", "微信业务受影响"),
        CERTIFICATE_ISSUE("ad4a0f48-2415-47b1-bca6-9d379ad50ff7", "证书问题"),
        NO_RISK("c57b7d1d-4446-40a4-b19a-f457d7f25e17", "无风险");

        private final String uuid;
        private final String description;

        RiskType(String uuid, String description) {
            this.uuid = uuid;
            this.description = description;
        }

        public static String getDescriptionByUuid(String uuid) {
            return Arrays.stream(RiskType.values())
                         .filter(type -> type.getUuid().equals(uuid))
                         .map(RiskType::getDescription)
                         .collect(Collectors.joining(","));
        }

        public String getUuid() {
            return uuid;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public void timeout(JsonNode input, ResultFuture<JsonNode> resultFuture) {
        logger.error("调用小模型超时，data: " + input);
        resultFuture.complete(Collections.singleton(input));
    }
}