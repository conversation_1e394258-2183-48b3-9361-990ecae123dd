package com.tencent.andata.smart.utils.aviatorFuns;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorLong;
import com.googlecode.aviator.runtime.type.AviatorObject;
import java.util.Map;

public class GetCurrentTimeStampFunction extends AbstractFunction {
    @Override
    public AviatorObject call(Map<String, Object> env) {
        // 返回当前时间戳(秒)
        long currentTimeStamp = System.currentTimeMillis();
        return AviatorLong.valueOf(currentTimeStamp);
    }

    @Override
    public String getName() {
        return "getCurrentTimeStamp";
    }
}