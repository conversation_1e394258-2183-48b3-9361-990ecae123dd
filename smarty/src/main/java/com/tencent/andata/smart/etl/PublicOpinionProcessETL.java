package com.tencent.andata.smart.etl;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.config.AppConfig;
import com.tencent.andata.smart.etl.filter.ModelResultFilter;
import com.tencent.andata.smart.etl.keyBy.StrategyKeySelector;
import com.tencent.andata.smart.etl.process.GlobalWindowREngineProcessor;
import com.tencent.andata.smart.etl.process.OpinionDataToREngineMessageProcess;
import com.tencent.andata.smart.etl.process.OpinionDataToRowProcess;
import com.tencent.andata.smart.etl.process.OpinionIntervalProcess;
import com.tencent.andata.smart.etl.process.PublicOpinionResultProcess;
import com.tencent.andata.smart.etl.serializer.KafkaREngineMessageSerializer;
import com.tencent.andata.smart.etl.serializer.KafkaStrategyStringDeserializer;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.struct.regnine.REngineMessage;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.sink.pg.ExecutionConfig;
import com.tencent.andata.utils.sink.pg.PgSinkBuilder;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.io.Serializable;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.types.Row;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.postgresql.Driver;

/**
 * 舆情处理ETL流程
 * 实现从Kafka消费模型结果、处理并输出到规则引擎和PG数据库
 * 使用KeyedProcessFunction的定时器实现WebIM场景的重试机制，支持Checkpoint
 * Kafka数据源 → 过滤数据 → 按场景分区(keyBy) → PublicOpinionResultProcess(使用重构后的组件) → 后续处理 → 输出
 */
@Builder
public class PublicOpinionProcessETL implements Serializable {

    // 静态变量
    public static final FlinkLog logger = FlinkLog.getInstance();
    // 规则引擎消息队列
    public static final String RENGINE_MSG_SINK_CONF = "mq.kafka.rengine_msg_mq";
    private static final String pgTableName = "smarty_public_opinion_risk";
    private static final String smartyDbConfGroup = "cdc.database.pgsql.smarty";
    private static final String modelResultMQGroup = "mq.kafka.ansmart_model_result";

    private transient RainbowUtils rainbowUtils;

    public void run(FlinkEnvUtils.FlinkEnv flinkEnv) throws Exception {
        // 获取数据源
        DataStream<Strategy> modelResultStream = getMqSource(flinkEnv);

        // 过滤出符合条件的舆情数据
        DataStream<Strategy> filteredStream = modelResultStream
                .filter(new ModelResultFilter(AppConfig.PublicOpinionConfig));

        // 处理策略数据获取风险结果数据
        // 注意：这里需要添加keyBy操作，对数据进行分区，以支持KeyedProcessFunction
        SingleOutputStreamOperator<Tuple2<String, String>> resultProcess = filteredStream
                .keyBy(new StrategyKeySelector())
                .process(PublicOpinionResultProcess.create(rainbowUtils));

        // 处理正常流程
        SingleOutputStreamOperator<String> riskResultStream = resultProcess
                .keyBy(value -> value.f0)
                .process(new OpinionIntervalProcess());

        // 处理数据并发送到规则引擎
        SingleOutputStreamOperator<REngineMessage> rEngineStream = riskResultStream
                .process(new OpinionDataToREngineMessageProcess());

        // 配置数据库
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(smartyDbConfGroup)
                .build();

        // Sink to PG
        resultProcess
                .process(new OpinionDataToRowProcess())
                .addSink(getPgSink(pgDBConf))
                .uid("SmartyPublicOpinionSinkToPGSql")
                .name("SmartyPublicOpinionSinkToPGSql");

        // 添加3秒全局窗口，确保PG写入优先于规则引擎
        SingleOutputStreamOperator<REngineMessage> windowedREngineStream = rEngineStream
                //.windowAll(GlobalWindows.create())
                //.trigger(PurgingTrigger.of(new DelayTrigger(Time.seconds(3))))
                .windowAll(TumblingProcessingTimeWindows.of(Time.seconds(3)))
                .process(new GlobalWindowREngineProcessor())
                .setParallelism(1)
                .name("GlobalWindowREngineProcessor")
                .uid("GlobalWindowREngineProcessor");

        // Sink to REngine
        String rEngineSinkUid = "SmartyPublicOpinionSinkToREngine";
        windowedREngineStream.sinkTo(getREngineKafkaSink())
                .uid(rEngineSinkUid)
                .name(rEngineSinkUid);
    }

    /**
     * PG Sink
     *
     * @param dbConf 数据库配置
     * @return SinkFunction
     */
    private SinkFunction<Row> getPgSink(DatabaseConf dbConf) {
        ExecutionConfig execConf = ExecutionConfig.builder()
                .batchSize(30)
                .maxRetries(5)
                .batchIntervalMs(200)
                .build();

        // 使用PgSinkBuilder创建PG Sink
        // 排除不需要写入数据库的列（自动生成或由数据库管理的列）
        return PgSinkBuilder.builder()
                .fromDatabaseConf(dbConf)
                .executionConfig(execConf)
                .loadSchemaFromDatabase(pgTableName)
                .excludeColumns("is_push", "risk_responsible", "create_time", "update_time")
                .build();
    }

    /**
     * 发送到规则引擎
     *
     * @return KafkaSink
     */
    private KafkaSink<REngineMessage> getREngineKafkaSink() {
        // 获取Kafka服务器和主题配置
        final String sinkAddr = rainbowUtils.getStringValue(RENGINE_MSG_SINK_CONF, "ENTRYPOINT");
        final String sinkTopic = rainbowUtils.getStringValue(RENGINE_MSG_SINK_CONF, "TOPICS");

        // 设置Kafka属性
        Properties properties = new Properties();
        properties.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, 15 * 60 * 1000);
        properties.put("max.request.size", "12582912");

        // 创建KafkaSink
        return KafkaSink.<REngineMessage>builder()
                .setKafkaProducerConfig(properties)
                .setBootstrapServers(sinkAddr)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema
                                .builder()
                                .setTopic(sinkTopic)
                                .setValueSerializationSchema(new KafkaREngineMessageSerializer())
                                .build()
                )
                .setTransactionalIdPrefix(String.format("SmartyPublicOpinionSink.%d", System.currentTimeMillis()))
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .build();
    }

    /**
     * 获取模型识别结果的MQ数据
     *
     * @param flinkEnv FlinkEnv
     * @return DataStream
     */
    public DataStream<Strategy> getMqSource(FlinkEnvUtils.FlinkEnv flinkEnv) {
        String entryPoint = rainbowUtils.getStringValue(modelResultMQGroup, "BROKERS");
        String topic = rainbowUtils.getStringValue(modelResultMQGroup, "TOPICS");
        // 使用风控的consumer
        String consumerGroup = rainbowUtils.getStringValue(modelResultMQGroup, "OPINION_CONSUMER");

        KafkaSource<Strategy> source = KafkaSource
                .<Strategy>builder()
                .setBootstrapServers(entryPoint)
                .setTopics(topic)
                // 从消费组提交offset开始消费，不存在则从最新的消费
                .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                .setGroupId(consumerGroup)
                .setDeserializer(new KafkaStrategyStringDeserializer())
                .setProperties(
                        new Properties() {{
                            // 在Checkpoint时提交偏移量
                            setProperty("commit.offsets.on.checkpoint", "true");
                            // 禁用自动提交偏移量
                            setProperty("enable.auto.commit", "false");
                            // 设置拉取超时为1秒
                            setProperty("fetch.max.wait.ms", "1000");
                        }}
                )
                .build();

        // 返回数据流
        return flinkEnv
                .env()
                .fromSource(source, WatermarkStrategy.noWatermarks(), "model_result_mq_source")
                .uid("model_result_mq_source")
                .name("model_result_mq_source")
                .setParallelism(4);
    }
}