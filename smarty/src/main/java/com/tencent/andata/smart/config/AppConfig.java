package com.tencent.andata.smart.config;

import static com.tencent.andata.smart.config.StrategyConfig.AGENT_STRATEGY_LIST;
import static com.tencent.andata.smart.config.StrategyConfig.OPINION_STRATEGY_LIST;
import static com.tencent.andata.smart.config.StrategyConfig.QUALITY_STRATEGY_LIST;

import com.tencent.andata.smart.strategy.model.Strategy;
import java.util.List;
import lombok.Builder;

/**
 * 模型识别在上层拆分应用，不同应用的策略不一致
 */
@Builder
public class AppConfig {

    // 策略列表
    public List<Strategy> strategies;
    // 消费数据总线MQ的ConsumerKey
    public String MQConsumerKey;
    // 调大模型的并发
    public int LLMParallelism;


    // 质检应用的配置
    public static AppConfig QualityInspectionConfig = AppConfig
            .builder()
            .strategies(QUALITY_STRATEGY_LIST)
            .MQConsumerKey("SMARTY_CONSUMER")
            .LLMParallelism(8)
            .build();

    // 风控应用的配置
    public static AppConfig PublicOpinionConfig = AppConfig
            .builder()
            .strategies(OPINION_STRATEGY_LIST)
            .MQConsumerKey("OPINION_CONSUMER")
            .LLMParallelism(10)
            .build();

    // Agent应用的配置
    public static AppConfig AgentAnalyzeConfig = AppConfig
            .builder()
            .strategies(AGENT_STRATEGY_LIST)
            .MQConsumerKey("AGENT_CONSUMER")
            .LLMParallelism(5)
            .build();

    public static AppConfig getAppByParams(String app) {
        switch (app) {
            case "Quality":
                return QualityInspectionConfig;
            case "Opinion":
                return PublicOpinionConfig;
            case "Agent":
                return AgentAnalyzeConfig;
            default:
                throw new RuntimeException("Only Support Quality and Opinion APP");
        }
    }
}