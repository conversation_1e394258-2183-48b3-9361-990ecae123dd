package com.tencent.andata.smart.utils;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.lookup.StaffDutyQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.HashMap;
import java.util.Properties;

public class DutyInfoUtils {

    private static final String ERROR_MSG = "DutyInfoUtils get info from db error: %s, duty_id: %s";
    public static FlinkLog logger = FlinkLog.getInstance();
    public static HashMap<Integer, String> dutyNameMap = new HashMap<Integer, String>();
    public static StaffDutyQuery dbQuery;

    static {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        DatabaseConf datawareDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.pgsql.dataware_r")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        dbQuery = Try.of(() -> {
            StaffDutyQuery query = new StaffDutyQuery(DatabaseEnum.PGSQL, datawareDbConf);
            query.open();
            return query;
        }).getOrElseThrow(e -> new RuntimeException(e));
    }

    public static String getDutyName(Integer dutyId) {
        return Option.of(dutyNameMap.get(dutyId))
                     .getOrElse(() -> Try.of(() -> {
                                             String dutyName = dbQuery.query(dutyId);
                                             dutyNameMap.put(dutyId, dutyName);
                                             return dutyName;
                                         }).onFailure(e -> logger.error(String.format(ERROR_MSG, e, dutyId)))
                                         .getOrNull());
    }
}