package com.tencent.andata.smart.access.operators;

import java.time.LocalDate;
import java.time.ZoneId;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 数据流处理
 * group消息过滤, 只保留当天的数据（由于群消息数据量过大，上游有可能会回刷数据）
 *
 * <AUTHOR>
 */
public class GroupMsgFilter implements FilterFunction<JsonNode> {

    @Override
    public boolean filter(JsonNode jsonNode) throws Exception {
        // 获取系统默认时区当天的0点时间戳
        long startTimestamp = LocalDate.now()
                                       .atStartOfDay(ZoneId.systemDefault())
                                       .toInstant()
                                       .toEpochMilli();

        return jsonNode.get("msg_time").asLong() > startTimestamp;
    }
}