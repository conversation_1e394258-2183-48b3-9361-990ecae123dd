package com.tencent.andata.smart.strategy.chunk.provider;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import io.vavr.control.Try;
import java.util.concurrent.CompletableFuture;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
public class RetryStrategyContextProvider implements ChunkContextProvider {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper MAPPER = new ObjectMapper();

    @SneakyThrows
    @Override
    public CompletableFuture<String> getContext(Strategy strategy) {
        JsonNode data = strategy.trigger.data[0];
        return CompletableFuture.completedFuture(data.get("context").asText());
    }

    @Override
    public boolean supports(Strategy strategy) {
        return Try.of(() -> strategy.trigger.data[0])
                .flatMap(data -> Try.of(() -> "retry_strategy".equals(data.get("data_type").asText())))
                .onFailure(e -> logger.error("Failed to check retry strategy support: " + e))
                .getOrElse(false);
    }
}