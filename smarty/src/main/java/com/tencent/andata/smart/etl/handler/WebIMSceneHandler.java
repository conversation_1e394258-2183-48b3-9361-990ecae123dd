package com.tencent.andata.smart.etl.handler;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.enums.TicketServiceChannel;
import com.tencent.andata.smart.etl.domain.CustomerStaffInfo;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.provider.ChunkContextProvider;
import com.tencent.andata.smart.strategy.chunk.provider.ChunkContextProviderFactory;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.StreamUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple4;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.text.SimpleDateFormat;
import java.util.NoSuchElementException;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.ConnectionFactory;

/**
 * WebIM场景处理器
 * 负责处理WebIM场景下的数据解析和特定字段提取
 * 实现非阻塞方式获取字段，支持后续通过侧输出流重试
 */
@Slf4j
public class WebIMSceneHandler extends AbstractSceneHandler {

    private static final FlinkLog logger = FlinkLog.getInstance();

    private final String zkQuorum;
    private final String zkNodeParent;
    private transient ChunkContextProvider contextProvider;

    public WebIMSceneHandler(
            DutyRepository dutyRepository,
            TicketRepository ticketRepository,
            CustomerRepository customerRepository) {

        // 调用父类构造函数
        super(dutyRepository, ticketRepository, customerRepository);

        // 初始化HBase连接信息
        RainbowUtils rainbow = RainbowAppConfig.getInstance();
        zkQuorum = rainbow.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM");
        zkNodeParent = rainbow.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT");
    }

    /**
     * 从节点中提取ticketId和post
     * 使用Try避免异常处理代码污染主逻辑
     */
    private static Tuple2<Long, String> extractTicketInfo(JsonNode node) {
        return Tuple.of(
                Try.of(() -> node.get("conversation_ticket_ids").asLong()).getOrElse(0L),
                Try.of(() -> node.get("post").asText()).getOrElse("")
        );
    }

    /**
     * 从operations中提取第一个有效的节点
     */
    private static Optional<JsonNode> findFirstValidNode(String operations) throws JsonProcessingException {
        return StreamUtils.streamOf(MAPPER.readValue(operations, ArrayNode.class))
                .filter(node -> node.has("service_scene_level2_name") && !node.get("service_scene_level2_name").asText().isEmpty())
                .filter(node -> node.has("service_channel") && node.get("service_channel").asInt() > 0)
                .filter(node -> node.get("conversation_ticket_ids").asLong() > 0L)
                .filter(node -> node.get("post").asInt() > 0)
                .findFirst();
    }


    /**
     * 获取ticketId
     * 若获取失败，则尝试从HBase中获取，但不进行阻塞重试
     */
    @Override
    public Option<Long> getTicketId(Strategy strategy) {
        Option<Long> ticketId = getFirstData(strategy)
                .flatMap((data) -> Option.of(data.get("conversation_ticket_ids")))
                .flatMap(node -> Try.of(node::asLong).toOption())
                .filter((id) -> id != null && id != 0L);

        if (ticketId.isEmpty()) {
            // 尝试从HBase中获取，但不进行阻塞重试
            return Try.of(() -> findFirstValidConversationTicket(strategy))
                    .filter(r -> r.isDefined() && r.get()._1 > 0L)
                    .map(r -> r.get()._1)
                    .toOption();
        }

        return ticketId;
    }

    /**
     * 获取operationId
     */
    @Override
    public Option<String> getOperationId(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("value_of_primary_key").asText());
    }

    /**
     * 获取eventTime
     */
    @Override
    public Option<Long> getEventTime(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("record_update_time").asText())
                .flatMap(timeStr -> Try.of(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeStr).getTime())
                        .toOption());
    }

    /**
     * 获取当前坐席IDR1
     */
    @Override
    public Option<String> getCurrentStaffIdr1(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("current_staff").asText())
                .filter(StringUtils::isNotEmpty)
                .onEmpty(() -> getFirstValidData(strategy).get().get("current_staff").asText())
                .flatMap(customerRepository::findByUid)
                .map(CustomerStaffInfo::getQIdr1);
    }

    /**
     * 获取当前坐席岗位
     */
    @Override
    public Option<String> getCurrentPost(Strategy strategy) {
        String res = getFirstData(strategy)
                .map(data -> data.get("post"))
                .filter(post -> post.asInt() > 0)
                .map(JsonNode::asText)
                .getOrElse(() -> {
                    // 尝试从HBase获取，但不阻塞
                    return Try.of(() -> findFirstValidConversationTicket(strategy))
                            .getOrElse(Option.none())
                            .map(tuple -> tuple._2)
                            .getOrElse("");
                });

        return Option.of(res);
    }

    /**
     * 获取服务通道
     * 非阻塞方式获取服务通道，若获取不到立即返回空，由侧输出流收集后重试
     */
    @Override
    public Option<String> getServiceChannel(Strategy strategy) {
        return getTriggerDataByField(strategy, "service_channel")
                .map(JsonNode::asInt)
                .filter(id -> id > 0)
                .onEmpty(() -> getFirstValidData(strategy).get().get("service_channel").asInt())
                .map(TicketServiceChannel::fromId)
                .map(TicketServiceChannel::getDescription);
    }

    /**
     * 获取当前坐席中文名
     */
    @Override
    public Option<String> getCurrentCustomerName(Strategy strategy) {
        return getTriggerDataByField(strategy, "customer_name")
                .filter(node -> !node.asText().isEmpty())
                .onEmpty(() -> getTriggerDataByField(strategy, "customerName"))
                .map(JsonNode::asText);
    }

    /**
     * 获取hbase连接
     */
    private AsyncConnection initHBaseConnection() throws Exception {
        Configuration conf = new Configuration();
        conf.set("hbase.zookeeper.quorum", zkQuorum);
        conf.set("zookeeper.znode.parent", zkNodeParent);

        return ConnectionFactory.createAsyncConnection(conf).get();
    }

    /**
     * 从 HBase 中查找第一个创建了工单并分配了坐席的数据（conversation ticket）
     *
     * @param strategy 策略对象
     * @return 包含工单ID和坐席岗位内容的元组，若无有效结果返回空Option
     */
    private Option<Tuple2<Long, String>> findFirstValidConversationTicket(Strategy strategy) {
        return Option.of(
                Try.of(() -> getContextProvider().getContext(strategy).get())
                        .flatMap(this::findFirstValidTicket)
                        .getOrNull()
        );
    }

    /**
     * 获取第一个有效的数据节点
     *
     * @param strategy 策略
     * @return 有效的数据节点
     */
    private Option<JsonNode> getFirstValidData(Strategy strategy) {
        return Try.of(() -> Option.ofOptional(findFirstValidNode(getContextProvider().getContext(strategy).get())))
                .recover(e -> {
                    logger.error(String.format("[WebIMSceneHandler] 获取有效数据失败: %s", e));
                    return Option.none();
                })
                .getOrElse(Option.none());
    }

    /**
     * 查找第一个有效的节点（post > 0）
     */
    private Try<Tuple2<Long, String>> findFirstValidTicket(String operations) {
        return Try.of(() -> findFirstValidNode(operations)
                .map(WebIMSceneHandler::extractTicketInfo)
                .orElseThrow(() -> new NoSuchElementException("No valid node found operations: " + operations)));
    }

    @Override
    public Option<Tuple4<String, String, String, String>> getServiceScenesName(Strategy strategy) {
        return getFirstData(strategy)
                .onEmpty(() -> getFirstValidData(strategy).get())
                .map(validData -> new Tuple4<>(
                        Try.of(() -> validData.get("service_scene_level1_name").asText()).getOrElse(""),
                        Try.of(() -> validData.get("service_scene_level2_name").asText()).getOrElse(""),
                        Try.of(() -> validData.get("service_scene_level3_name").asText()).getOrElse(""),
                        Try.of(() -> validData.get("service_scene_level4_name").asText()).getOrElse("")
                ));
    }

    /**
     * 获取ChunkContextProvider对象
     * 延迟初始化，仅在需要时创建
     */
    private ChunkContextProvider getContextProvider() {
        if (contextProvider == null) {
            try {
                contextProvider = (new ChunkContextProviderFactory(initHBaseConnection())).getProviders().stream()
                        .filter(p -> p.getClass().getSimpleName().equals("HbaseOperationChunkContextProvider"))
                        .findFirst()
                        .orElseThrow(() -> new NoSuchElementException("No HbaseOperationChunkContextProvider found"));
            } catch (Exception e) {
                logger.error(String.format("[WebIMSceneHandler] Failed to initialize contextProvider: %s", e.getMessage()));
                throw new RuntimeException("Failed to initialize contextProvider", e);
            }
        }
        return contextProvider;
    }
}