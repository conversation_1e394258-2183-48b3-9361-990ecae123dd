package com.tencent.andata.smart.access.operators;

import io.vavr.control.Try;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

public class WebImTicketIdMapFunc implements MapFunction<JsonNode, JsonNode> {

    /**
     * 从输入的JsonNode中提取conversation_ticket_ids字段的值，并将其作为ticket_id添加到输入中。
     * 如果conversation_ticket_ids字段不存在或解析失败，则默认使用0L作为ticket_id的值。
     *
     * @param input 输入的JsonNode对象，包含需要处理的JSON数据。
     * @return 处理后的JsonNode对象，已添加ticket_id字段。
     * @throws Exception 如果处理过程中发生异常。
     */
    @Override
    public JsonNode map(JsonNode input) throws Exception {
        Long ticketId = Try.of(() -> input.get("conversation_ticket_ids").asLong()).getOrElse(0L);
        ((ObjectNode) input).put("ticket_id", ticketId);
        return input;
    }
}