package com.tencent.andata.smart.etl.process;

import static org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

import com.tencent.andata.log.FlinkLog;
import java.util.HashMap;
import java.util.Locale;

import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;


/**
 * 这里要根据工单维度对风险间隔做过滤
 * 例如：五分钟之内工单风险没提升就不推送告警
 * 所以这里只做计算风险等级是否提升，以及距离上一次的风险间隔时间
 * 至于配置是否推送放在OLA流程中
 */
public class OpinionIntervalProcess extends KeyedProcessFunction<String, Tuple2<String, String>, String> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private HashMap<String, Integer> riskLevelMap;
    private ObjectMapper objectMapper;

    // 储存工单风险信息
    // f0: 风险时间， f1: 风险等级
    private ValueState<Tuple2<Long, String>> ticketRiskInfo;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        objectMapper = new ObjectMapper().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
        ValueStateDescriptor<Tuple2<Long, String>> descriptor =
                new ValueStateDescriptor<>(
                        "ticket_risk_info", // the state name
                        TypeInformation.of(new TypeHint<Tuple2<Long, String>>() {
                        })
                );
        ticketRiskInfo = getRuntimeContext().getState(descriptor);
        riskLevelMap = new HashMap<String, Integer>() {{
            put("D", 0);
            put("C", 1);
            put("B", 2);
            put("A", 3);
            put("S", 4);
        }};
    }

    @Override
    public void processElement(Tuple2<String, String> in, Context context, Collector<String> out) throws Exception {
        HashMap<String, Object> data = objectMapper.readValue(in.f1, new TypeReference<HashMap<String, Object>>() {});

        Long triggerTime = (Long) data.get("trigger_time");
        String riskLevel = (String) data.get("risk_level");

        // 获取上一次的风险等级
        Tuple2<Long, String> riskInfo = ticketRiskInfo.value();
        // 如果没有上一次，则直接下发
        if (riskInfo == null) {
            data.put("is_first_risk", true);
            data.put("risk_interval", 0);
            data.put("is_risk_up", true);
            ticketRiskInfo.update(Tuple2.of(triggerTime, riskLevel));
            out.collect(objectMapper.writeValueAsString(data));
            return;
        }

        Long preRiskTime = riskInfo.f0;
        String preRiskLevel = riskInfo.f1;
        // 计算与上一次的间隔，单位秒
        // 这里因为上游是过模型的，时长不一，不完全有序，所以取绝对值
        // 不完全有序是可以接受的，因为会去判断风险等级是否提升再推送
        // 如果业务要求完全有序的话就只有开窗来做了
        Long riskInterval = Math.abs(triggerTime - preRiskTime) / 1000;
        boolean isRiskUp = getRiskLevelInt(riskLevel) > getRiskLevelInt(preRiskLevel);
        data.put("is_first_risk", false);
        data.put("risk_interval", riskInterval);
        data.put("is_risk_up", isRiskUp);
        // 状态中保留最高风险等级
        ticketRiskInfo.update(Tuple2.of(triggerTime,
                getRiskLevelInt(riskLevel) > getRiskLevelInt(preRiskLevel) ? riskLevel : preRiskLevel));

        out.collect(objectMapper.writeValueAsString(data));
    }


    /**
     * 获取等级对应的数字，方便排序
     *
     * @param riskLevel 风险等级
     * @return 风险等级对应的数字
     */
    private Integer getRiskLevelInt(String riskLevel) {
        if (!riskLevelMap.containsKey(riskLevel.toUpperCase(Locale.ROOT))) {
            return 0;
        }
        return riskLevelMap.get(riskLevel.toUpperCase(Locale.ROOT));
    }
}