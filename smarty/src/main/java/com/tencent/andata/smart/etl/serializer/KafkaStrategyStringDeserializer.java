package com.tencent.andata.smart.etl.serializer;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.io.IOException;

public class KafkaStrategyStringDeserializer implements KafkaRecordDeserializationSchema<Strategy> {

    private final ObjectMapper objectMapper;

    public KafkaStrategyStringDeserializer() {
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void open(DeserializationSchema.InitializationContext context) throws Exception {
        KafkaRecordDeserializationSchema.super.open(context);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<Strategy> collector) throws IOException {
        collector.collect(objectMapper.readValue(consumerRecord.value(), Strategy.class));
    }

    @Override
    public TypeInformation<Strategy> getProducedType() {
        return TypeInformation.of(Strategy.class);
    }
}