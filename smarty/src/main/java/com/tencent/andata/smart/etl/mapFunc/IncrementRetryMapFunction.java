package com.tencent.andata.smart.etl.mapFunc;

import com.tencent.andata.smart.etl.process.RetryControlProcessFunction;
import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.tuple.Tuple3;

/**
 * 递增重试计数映射函数
 * 处理需要延迟重试的数据，递增重试次数并更新下次重试时间
 */
public class IncrementRetryMapFunction implements MapFunction<Tuple3<Strategy, Integer, Long>, Tuple3<Strategy, Integer, Long>> {

    private final int retryIntervalSeconds;

    /**
     * 构造函数
     *
     * @param retryIntervalSeconds 重试间隔时间（秒）
     */
    public IncrementRetryMapFunction(int retryIntervalSeconds) {
        this.retryIntervalSeconds = retryIntervalSeconds;
    }

    /**
     * 将需要延迟重试的元组转换为递增重试次数后的新元组
     *
     * @param tuple 包含策略、当前重试次数和时间戳的元组
     * @return 包含策略、递增后的重试次数和更新的下次重试时间的元组
     */
    @Override
    public Tuple3<Strategy, Integer, Long> map(Tuple3<Strategy, Integer, Long> tuple) {
        // 递增重试次数
        Strategy strategy = tuple.f0;
        int nextRetryCount = tuple.f1 + 1;

        // 创建新的重试元组，保留策略对象，增加重试次数，更新下次重试时间
        return RetryControlProcessFunction.createRetryTuple(strategy, nextRetryCount, retryIntervalSeconds);
    }
}