package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;

public class StaffDutyQuery extends AbstractJDBCLookupQuery<Integer, String> {

    public StaffDutyQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected String executeQuery(Connection connection, Integer dutyId) throws Exception {
        String sql = "SELECT duty_name FROM dim_incident_duty WHERE duty_id = ? AND is_valid = 1";
        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setLong(1, dutyId);
        final ResultSet resultSet = preparedStatement.executeQuery();
        // 遍历结果
        while (resultSet.next()) {
            // 队列名称
            return resultSet.getString(1);
        }
        return "";
    }
}