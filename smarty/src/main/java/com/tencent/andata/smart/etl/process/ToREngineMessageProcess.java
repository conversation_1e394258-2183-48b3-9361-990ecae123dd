package com.tencent.andata.smart.etl.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.struct.regnine.MessagePack;
import com.tencent.andata.struct.regnine.REngineMessage;
import com.tencent.andata.struct.regnine.builder.MessageDataBuilder;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class ToREngineMessageProcess extends ProcessFunction<String, REngineMessage> {

    public String tableName;
    private static final FlinkLog logger = FlinkLog.getInstance();

    public ToREngineMessageProcess(String icebergTableName) {
        this.tableName = icebergTableName;
    }

    @Override
    public void processElement(String in, Context context, Collector<REngineMessage> collector) throws Exception {
        collector.collect(covertToRengineMessage(tableName, convertToMessageRowData(in)));
    }

    /**
     * 构造REngineMessage
     *
     * @param tableName iceberg表名
     * @param rowData MessageRowData
     * @return
     */
    private REngineMessage covertToRengineMessage(String tableName, MessageRowData rowData) {
        // 创建 REngineMessage
        REngineMessage message = new REngineMessage();
        // 订阅表名
        message.setSubScribeName(tableName);
        message.setMessagePack(
                MessagePack.builder()
                        .addMessageData(
                                new MessageDataBuilder()
                                        .setDstTableName(tableName)
                                        .addRowData(rowData)
                                        .build()
                        )
                        .build()
        );
        logger.info(String.format("[ToREngineMessageProcess]message: %s", message));
        return message;
    }

    /**
     * 先转成RowData
     *
     * @param input 输入数据
     * @return MessageRowData
     */
    private MessageRowData convertToMessageRowData(String input) {
        // 按照REngine的数据格式构造数据
        return MessageRowData.of(
                "data_change",
                "insert",
                "{}",
                input,
                System.currentTimeMillis()
        );
    }
}