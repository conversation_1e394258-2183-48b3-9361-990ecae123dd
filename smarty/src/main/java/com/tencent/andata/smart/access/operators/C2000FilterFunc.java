package com.tencent.andata.smart.access.operators;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class C2000FilterFunc implements FilterFunction<JsonNode> {
    private static final Logger log = LoggerFactory.getLogger(C2000FilterFunc.class);
    private static final String TICKET_ID = "ticket_id";

    @Override
    public boolean filter(JsonNode node) {
        if (node == null) {
            log.debug("Received null node");
            return false;
        }

        try {
            JsonNode ticketIdNode = node.get(TICKET_ID);
            if (ticketIdNode == null || ticketIdNode.isNull()) {
                log.debug("Null or missing ticket_id in node: {}", node);
                return false;
            }

            if (!ticketIdNode.isTextual() && !ticketIdNode.isNumber()) {
                log.debug("Invalid ticket_id type in node: {}", node);
                return false;
            }

            String ticketId = ticketIdNode.asText();
            boolean isValid = StringUtils.isNotBlank(ticketId);

            if (!isValid) {
                log.debug("Empty or blank ticket_id in node: {}", node);
            }

            return isValid;
        } catch (Exception e) {
            log.error("Error filtering C2000 message: {}", node, e);
            return false;
        }
    }
}