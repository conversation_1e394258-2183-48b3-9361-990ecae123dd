package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import org.apache.calcite.tools.ValidationException;

public class CustomerInfoQuery extends AbstractJDBCLookupQuery<List<Long>, String> {

    public CustomerInfoQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected String executeQuery(Connection connection, List<Long> parms) throws Exception {
        String sql = "SELECT" +
                "          CASE WHEN LENGTH(owner_name) > 0 THEN owner_name" +
                "          ELSE nickname END AS name " +
                "        FROM dim_customer" +
                "        WHERE uin = ? OR owner_uin = ? LIMIT 1";
        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setLong(1, parms.get(0));
        preparedStatement.setLong(2, parms.get(1));
        final ResultSet resultSet = preparedStatement.executeQuery();
        // 遍历结果
        while (resultSet.next()) {
            // 公司名称
            return resultSet.getString(1);
        }
        return "";
    }
}