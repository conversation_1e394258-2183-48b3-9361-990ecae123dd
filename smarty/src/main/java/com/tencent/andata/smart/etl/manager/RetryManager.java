package com.tencent.andata.smart.etl.manager;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import java.io.Serializable;
import java.util.function.Function;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.api.common.functions.RuntimeContext;

/**
 * 重试管理器
 * 负责管理重试状态、调度重试和处理定时器事件
 */
public class RetryManager implements Serializable {

    private static final long serialVersionUID = 1L;

    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 60;

    // 重试间隔（秒）
    private static final int RETRY_INTERVAL_SECONDS = 30;

    private static final FlinkLog logger = FlinkLog.getInstance();

    // 状态：保存重试次数
    private transient MapState<String, Integer> retryCountState;

    // 状态：保存原始策略数据
    private transient MapState<String, Strategy> strategyState;

    /**
     * 初始化状态
     */
    public void initState(RuntimeContext runtimeContext) {
        retryCountState = runtimeContext.getMapState(
                new MapStateDescriptor<>("retry-count-state", String.class, Integer.class));

        strategyState = runtimeContext.getMapState(
                new MapStateDescriptor<>("strategy-state", String.class, Strategy.class));
    }

    /**
     * 调度重试
     * 增加重试计数并注册定时器
     */
    public void scheduleRetry(String key, Strategy strategy, KeyedProcessFunction<String, Strategy, ?>.Context context) throws Exception {
        // 获取当前重试次数
        int retryCount = retryCountState.contains(key) ? retryCountState.get(key) : 0;

        // 检查是否超过最大重试次数
        if (retryCount < MAX_RETRY_COUNT) {
            // 更新重试次数和保存策略
            retryCountState.put(key, retryCount + 1);
            strategyState.put(key, strategy);

            // 注册定时器，延迟后重试
            long timerTimestamp = context.timerService().currentProcessingTime() + RETRY_INTERVAL_SECONDS * 1000L;
            context.timerService().registerProcessingTimeTimer(timerTimestamp);

            logger.info(String.format("[RetryManager] WebIM strategy scheduled for retry, " +
                    "key: %s, retryCount: %s, nextRetryTime: %s", key, retryCount + 1, timerTimestamp));
        } else {
            // 超过最大重试次数，记录日志
            logger.warn(String.format("[RetryManager] WebIM strategy exceeded max retry attempts, " +
                    "key: %s, dropping data", key));
            // 清理状态
            cleanupState(key);
        }
    }

    /**
     * 处理定时器事件
     *
     * @param key 键值
     * @param ctx 定时器上下文
     * @param processFunction 处理函数，返回true表示处理成功，false表示需要继续重试
     */
    public void handleTimer(String key, KeyedProcessFunction<String, Strategy, ?>.OnTimerContext ctx,
            Function<Strategy, Boolean> processFunction) throws Exception {
        // 检查状态是否存在
        if (strategyState.contains(key)) {
            Strategy strategy = strategyState.get(key);

            // 调用处理函数
            boolean processed = processFunction.apply(strategy);

            if (!processed) {
                // 处理失败，需要继续重试
                int retryCount = retryCountState.get(key);

                if (retryCount < MAX_RETRY_COUNT) {
                    // 增加重试次数
                    retryCountState.put(key, retryCount + 1);

                    // 再次注册定时器
                    long nextTimerTimestamp = ctx.timerService().currentProcessingTime() + RETRY_INTERVAL_SECONDS * 1000L;
                    ctx.timerService().registerProcessingTimeTimer(nextTimerTimestamp);

                    logger.info(String.format("[RetryManager] WebIM strategy scheduled for retry, " +
                            "key: %s, retryCount: %s, nextRetryTime: %s", key, retryCount + 1, nextTimerTimestamp));
                } else {
                    // 超过最大重试次数，记录日志
                    logger.warn(String.format("[RetryManager] WebIM strategy exceeded max retry attempts after timer, " +
                            "key: %s, dropping data", key));
                    // 清理状态
                    cleanupState(key);
                }
            } else {
                // 处理成功，清理状态
                cleanupState(key);
            }
        }
    }

    /**
     * 清理状态
     */
    public void cleanupState(String key) throws Exception {
        if (retryCountState.contains(key)) {
            retryCountState.remove(key);
        }
        if (strategyState.contains(key)) {
            strategyState.remove(key);
        }
    }
}