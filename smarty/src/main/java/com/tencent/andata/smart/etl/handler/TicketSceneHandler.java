package com.tencent.andata.smart.etl.handler;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.enums.TicketServiceChannel;
import com.tencent.andata.smart.etl.domain.CustomerStaffInfo;
import com.tencent.andata.smart.etl.repository.inferface.CustomerRepository;
import com.tencent.andata.smart.etl.repository.inferface.DutyRepository;
import com.tencent.andata.smart.etl.repository.inferface.TicketRepository;
import com.tencent.andata.smart.strategy.model.Strategy;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.text.SimpleDateFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

@Slf4j
public class TicketSceneHandler extends AbstractSceneHandler {

    private static final FlinkLog logger = FlinkLog.getInstance();

    public TicketSceneHandler(
            DutyRepository dutyRepository,
            TicketRepository ticketRepository,
            CustomerRepository customerRepository) {
        super(dutyRepository, ticketRepository, customerRepository);
    }

    @Override
    public Option<Long> getTicketId(Strategy strategy) {
        return Option.of(strategy)
                .flatMap(s -> Option.of(s.sceneIdentify))
                .flatMap(data -> Try.of(() -> Long.parseLong(data)).toOption())
                .onEmpty(() -> logger.error("[TicketSceneHandler] Failed to get ticketId, strategy: " + strategy));
    }

    @Override
    public Option<String> getOperationId(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> String.valueOf(data.get("operation_id").asLong()));
    }

    @Override
    public Option<String> getTriggerContent(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("extern_reply").asText());
    }

    @Override
    public Option<Long> getEventTime(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("operate_time").asText().replace("T", " "))
                .flatMap(timeStr -> Try.of(() ->
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeStr).getTime()
                ).toOption());
    }

    @Override
    public Option<String> getCurrentStaffIdr1(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("current_operator").asText())
                .flatMap(customerRepository::findByUid)
                .map(CustomerStaffInfo::getQIdr1);
    }

    @Override
    public Option<String> getServiceChannel(Strategy strategy) {
        return getTriggerDataByField(strategy, "service_channel")
                .map(JsonNode::asInt)
                .map(TicketServiceChannel::fromId)
                .map(TicketServiceChannel::getDescription);
    }

    @Override
    public Option<String> getCurrentPost(Strategy strategy) {
        return getFirstData(strategy)
                .map(data -> data.get("post").asText());
    }

    @Override
    public Option<String> getCurrentCustomerName(Strategy strategy) {
        return getTriggerDataByField(strategy, "customerName")
                .map(JsonNode::asText);
    }
}