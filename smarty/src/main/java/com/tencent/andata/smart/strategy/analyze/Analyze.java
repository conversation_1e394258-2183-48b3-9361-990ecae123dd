package com.tencent.andata.smart.strategy.analyze;

import java.io.Serializable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Analyze implements Serializable {
    // 分析类型
    public AnalyzeType type;
    // 唯一名称
    public String name;
    // 描述
    public String desc;
    // 大模型Prompt，如有
    public String prompt;
    // 大模型结果
    public String res;
    // 调用哪个大模型
    public String model;
    // 大模型结果类型
    public ResType resType;
    // 大模型额外参数
    public ConcurrentHashMap<String, String> modelExtraParams;

    public CopyOnWriteArrayList<ConcurrentHashMap<String, Object>> messages;

    @Override
    public String toString() {
        return "Analyze{" +
                "type=" + type +
                ", name='" + name + '\'' +
                ", desc='" + desc + '\'' +
                ", prompt='" + prompt + '\'' +
                ", res='" + res + '\'' +
                ", model='" + model + '\'' +
                ", modelExtraParams=" + modelExtraParams +
                '}';
    }
}