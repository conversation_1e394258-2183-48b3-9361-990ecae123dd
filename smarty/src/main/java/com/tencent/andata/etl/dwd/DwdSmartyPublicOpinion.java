package com.tencent.andata.etl.dwd;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.etl.sql.DwdSmartyPublicOpinionSQL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdSmartyPublicOpinion {

    private static final Logger logger = LoggerFactory.getLogger(DwdImGroupRiskCtrl.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 自定义标签
        tEnv.createTemporaryView(
                "dwd_custom_labels",
                tEnv.sqlQuery(DwdSmartyPublicOpinionSQL.DWD_CUSTOM_LABEL_SQL)
        );

        // MC 舆情数据处理
        Table publicOpinionMCTable = tEnv.sqlQuery(
                DwdSmartyPublicOpinionSQL.DWD_SMARTY_PUBLIC_OPINION_MC
        );
        tEnv.createTemporaryView(
                "dwd_public_opinion_mc",
                publicOpinionMCTable
        );
        // CC 数据裁剪
        Table publicOpinionCCPruneTable = tEnv.sqlQuery(
                DwdSmartyPublicOpinionSQL.SMARTY_PUBLIC_OPINION_CC_ODS_PRUNE
        );
        publicOpinionCCPruneTable = tEnv.fromChangelogStream(
                tEnv.toChangelogStream(publicOpinionCCPruneTable)
        );
        tEnv.createTemporaryView(
                "ods_smarty_po_cc_source_prune",
                publicOpinionCCPruneTable
        );
        // CC 舆情数据处理
        Table publicOpinionCCTable = tEnv.sqlQuery(
                DwdSmartyPublicOpinionSQL.DWD_SMARTY_PUBLIC_OPINION_CC
        );
        tEnv.createTemporaryView(
                "dwd_public_opinion_cc",
                publicOpinionCCTable
        );
        // WEBIM数据剪枝
        Table publicOpinionWebimPruneTable = tEnv.sqlQuery(
                DwdSmartyPublicOpinionSQL.SMARTY_PUBLIC_OPINION_WEBIM_ODS_PRUNE
        );
        publicOpinionWebimPruneTable = tEnv.fromChangelogStream(
                tEnv.toChangelogStream(publicOpinionWebimPruneTable)
        );
        tEnv.createTemporaryView(
                "ods_smarty_po_webim_source_prune",
                publicOpinionWebimPruneTable
        );
        // webim 舆情数据处理
        Table publicOpinionWebIMTable = tEnv.sqlQuery(
                DwdSmartyPublicOpinionSQL.DWD_SMARTY_PUBLIC_OPINION_WEBIM
        );
        tEnv.createTemporaryView(
                "dwd_public_opinion_webim",
                publicOpinionWebIMTable
        );
        // 三通道union all
        Table dwdOpinionTable = tEnv.sqlQuery(
                DwdSmartyPublicOpinionSQL.DWD_IM_GROUP_RISK_DATA_PROCESS_SQL
        );
        tEnv.createTemporaryView(
                "dwd_opinion_tale",
                dwdOpinionTable
        );
        // 关联标注数据
        tEnv.createTemporaryView(
                "dwm_opinion_table",
                tEnv.sqlQuery(DwdSmartyPublicOpinionSQL.DWM_SMARTY_OPINION_SQL)
        );

        // 入库iceberg
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "dwm_opinion_table",
                "iceberg_dwm_smarty_public_opinion",
                tEnv.from("iceberg_dwm_smarty_public_opinion"),
                ICEBERG
        ));
        // 入库PG
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "dwm_opinion_table",
                "f_dwm_smarty_public_opinion",
                tEnv.from("f_dwm_smarty_public_opinion"),
                PGSQL
        ));
        // 入库starrocks
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "dwm_opinion_table",
                "flink_starrocks_dwm_smarty_public_opinion",
                tEnv.from("flink_starrocks_dwm_smarty_public_opinion"),
                ROCKS
        ));
    }
}
