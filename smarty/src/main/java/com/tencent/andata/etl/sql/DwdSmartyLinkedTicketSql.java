package com.tencent.andata.etl.sql;

public class DwdSmartyLinkedTicketSql {

    public static String DWD_SMARTY_LINKED_TICKET_SQL = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        *,\n"
            + "        row_number() over(partition by value_of_primary_key order by record_update_time desc) rn\n"
            + "    from iceberg_ods_linked_ticket /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + ")\n"
            + "select \n"
            + "    *\n"
            + "from tmp1\n"
            + "where rn = 1";
}
