package com.tencent.andata.etl.dwd;

import com.tencent.andata.etl.sql.DwdSmartyLinkedTicketSql;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import lombok.Builder;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@Builder
public class DwdSmartyLinkedTicket {
    private static final Logger logger = LoggerFactory.getLogger(DwdImGroupRiskCtrl.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        tEnv.createTemporaryView(
                "dwd_smarty_linked_ticket",
                tEnv.sqlQuery(
                        DwdSmartyLinkedTicketSql.DWD_SMARTY_LINKED_TICKET_SQL
                )
        );
        // 入库iceberg
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "dwd_smarty_linked_ticket",
                "iceberg_dwd_smarty_linked_ticket",
                tEnv.from("iceberg_dwd_smarty_linked_ticket"),
                ICEBERG
        ));
        // 入库PG
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "dwd_smarty_linked_ticket",
                "f_dwd_smarty_linked_ticket",
                tEnv.from("f_dwd_smarty_linked_ticket"),
                PGSQL
        ));
    }
}
