package com.tencent.andata.etl;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;


import java.util.Properties;

public class SapbCallCenterApplication {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        // Mysql表注册
        DatabaseConf qidianDB = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "qidian"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("ods_t904")
                        .tableBuilderStrategy(
                                new CDCTableBuilderStrategy(
                                        "t904_qidian_customer_call",
                                        MYSQL,
                                        qidianDB, ""
                                )
                        )
                        .build()
        );
        // Pgsql表注册
        DatabaseConf pgDb = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "andon"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("pg_t904")
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        "t904_qidian_customer_call",
                                        DatabaseEnum.PGSQL,
                                        pgDb
                                )
                        )
                        .build()
        );
        String startId = parameterTool.get("startId");
        // SINK
        tEnv.executeSql(String.format("insert into pg_t904\n"
                + "select \n"
                + " id,\n"
                + "cast(uin as bigint),\n"
                + "business,\n"
                + "created_at,\n"
                + "updated_at,\n"
                + "CallId,\n"
                + "cast(CallType as bigint),\n"
                + "SrcNumber,\n"
                + "DstNumber,\n"
                + "cast(StartCallTime as bigint),\n"
                + "cast(StartRingTime as bigint),\n"
                + "cast(ReceiveTime as bigint),\n"
                + "cast(EndCallTime as bigint),\n"
                + "cast(Duration as bigint),\n"
                + "Kfext,\n"
                + "cast(WaitTime as bigint),\n"
                + "cast(PhoneLostType as bigint),\n"
                + "cast(PhoneStage as bigint),\n"
                + "TransferSourceKfext,\n"
                + "SubCallid,\n"
                + "cast(PhoneLostTime as bigint),\n"
                + "cast(CallService as bigint),\n"
                + "StrRecordUrl,\n"
                + "cast(IvrDuration as bigint),\n"
                + "Country,\n"
                + "Province,\n"
                + "City,\n"
                + "cast(isRepeat as int),\n"
                + "lossNum,\n"
                + "cast(isVip as int),\n"
                + "cast(isConnect as int)\n"
                + "from ods_t904\n"
                + "where id > %s", startId));

        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("AnSmarty Risk Control Application");
    }
}
