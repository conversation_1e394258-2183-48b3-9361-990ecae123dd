package com.tencent.andata.etl.sql;

import com.tencent.andata.utils.struct.DatabaseConf;

public class LookUpJoinTableDDLSql {
    /**
     * cc lookup join表DDL
     *
     * @param db
     * @return
     */
    public static String publicOpinionCCRecordDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE public_opinion_cc_record\n"
                        + " (\n"
                        + "  `id` INT,\n"
                        + "  `content` STRING,\n"
                        + "  `is_hit_sensitive_word` INT,\n"
                        + "  `is_semantic_hit` INT,\n"
                        + "  `call_id_id` STRING,\n"
                        + "  `role` STRING,\n"
                        + "  CONSTRAINT `public_opinion_cc_record_pkey` PRIMARY KEY (`id`) NOT ENFORCED\n"
                        + ")\n"
                        + " \n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 'public_opinion_cc_record',\n"
                        + "  'url' = 'jdbc:postgresql://%s:%s/%s?"
                        + "reWriteBatchedInserts=true&?currentSchema=public&serverTimezone=Asia/Shanghai',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName
        );
    }

    /**
     * 自定义备至lookup join 表DDL
     *
     * @param db
     * @return
     */
    public static String smartyCustomFieldConfigDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE custom_field_config\n"
                        + " (\n"
                        + "  `id` INT,\n"
                        + "  `key` STRING,\n"
                        + "  `type` STRING,\n"
                        + "  `options` STRING,\n"
                        + "  `project_id` INT\n"
                        + ")\n"
                        + " \n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 'custom_field_config_v2_view',\n"
                        + "  'url' = 'jdbc:postgresql://%s:%s/%s?"
                        + "reWriteBatchedInserts=true&?currentSchema=public&serverTimezone=Asia/Shanghai',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName
        );
    }

    /**
     * 公司信息维表lookup join表DDL
     *
     * @param db
     * @return
     */
    public static String dimCompanyInfoDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE dim_company_info\n"
                        + " (\n"
                        + "  `company_id` BIGINT,\n"
                        + "  `company_name` STRING\n"
                        + ")\n"
                        + " \n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 'dim_company_info',\n"
                        + "  'url' = 'jdbc:postgresql://%s:%s/%s?"
                        + "reWriteBatchedInserts=true&?currentSchema=public&serverTimezone=Asia/Shanghai',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName
        );
    }

    /**
     * 会话ID、工单ID关系维表DDL
     *
     * @param db
     * @return
     */
    public static String dwmWebimBaseViewDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE dwm_webim_base_view\n"
                        + " (\n"
                        + "  `conversation_id` STRING,\n"
                        + "  `conversation_ticket_ids` STRING\n"
                        + ")\n"
                        + " \n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 'dwm_webim_base_view',\n"
                        + "  'url' = 'jdbc:postgresql://%s:%s/%s?"
                        + "reWriteBatchedInserts=true&?currentSchema=public&serverTimezone=Asia/Shanghai',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName
        );
    }

    /**
     * 员工虚拟职能组维表DDL
     *
     * @param db
     * @return
     */
    public static String t007StaffPostDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE t007_staff_group\n"
                        + " (\n"
                        + "  `group_id` INT,\n"
                        + "  `q_idr2` STRING\n"
                        + ")\n"
                        + " \n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 't007_staff_group',\n"
                        + "  'url' = 'jdbc:mysql://%s:%s/%s',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName
        );
    }

    /**
     * 员工虚拟职能组维表DDL
     *
     * @param db
     * @return
     */
    public static String t006StaffDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE t006_staff\n"
                        + " (\n"
                        + "  `user_id` STRING,\n"
                        + "  `group_id` INT\n"
                        + ")\n"
                        + " \n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 't006_staff',\n"
                        + "  'url' = 'jdbc:mysql://%s:%s/%s',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName
        );
    }
}