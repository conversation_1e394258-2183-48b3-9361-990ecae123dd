package com.tencent.andata.etl.sql;

public class DwmImGroupRiskCtrlSql {

    public static final String CALCULATE_FIRST_VIEW_TIME_SQL = ""
            + "WITH tmp1 AS (\n"
            + "  SELECT\n"
            + "    t1.*,\n"
            + "    CAST(t2.`record_update_time` AS STRING) AS `view_time`,\n"
            + "    CAST(null AS timestamp) AS `owner_first_view_time`,\n"
            + "    CAST(null AS timestamp) AS `leader_first_view_time`,\n"
            + "    t2.`viewer_rtx`\n"
            + "  FROM iceberg_source_dwd_im_group_risk_ctrl "
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/ AS t1\n"
            + "  LEFT JOIN iceberg_source_dwd_opinion_access_record  "
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/ AS t2\n"
            + "  ON ((t1.`owner` = t2.`viewer_rtx` OR t1.`leader` = t2.`viewer_rtx`)\n"
            + "  AND t2.`risk_id` = t1.`risk_id`\n"
            + "  AND t2.`record_update_time` BETWEEN t1.`alarm_time` "
            + "  AND t1.`alarm_time` + INTERVAL '8' HOUR)\n"
            + "),\n"
            + "tmp2 AS (\n"
            + "  SELECT\n"
            + "    tmp1.*,\n"
            + "    t3.`display_content`,\n"
            + "    CAST(t3.`msg_time` AS STRING) AS `msg_time`,\n"
            + "    t3.`rtx` AS `customer_service_rtx`,\n"
            + "    '' AS `first_response_css`,\n"
            + "    CAST(null AS timestamp) AS `css_first_response_time`,\n"
            + "    '' AS `css_first_response_content`\n"
            + "    FROM tmp1\n"
            + "    LEFT JOIN iceberg_source_dwd_im_group_message "
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/ AS t3\n"
            + "    ON (tmp1.`group_id` = t3.`group_id`\n"
            + "    AND t3.`sender_type` IN ('客服', '机器人')\n"
            + "    AND t3.`msg_time` BETWEEN tmp1.`alarm_time` "
            + "    AND tmp1.`alarm_time` + INTERVAL '8' HOUR)\n"
            + ")\n"
            + "SELECT *\n"
            + "FROM tmp2";
}