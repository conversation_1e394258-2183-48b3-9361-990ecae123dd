package com.tencent.andata.etl.tablemap;

public class DwdSmartyCommonMapping {
    public static final String ICEBERG_TO_FLINK_TABLE = ""
            + "[{\n"
            + "    \"icebergTable\": \"ods_conversation_badcase_feedback\",\n"
            + "    \"fTable\": \"iceberg_ods_conversation_badcase_feedback\",\n"
            + "    \"primaryKey\": \"value_of_primary_key\"\n"
            + "}, {\n"
            + "    \"icebergTable\": \"dwd_smarty_badcase\",\n"
            + "    \"fTable\": \"iceberg_dwd_smarty_badcase\",\n"
            + "    \"primaryKey\": \"value_of_primary_key\"\n"
            + "}]";

    public static String rdbTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_internal_service_badcase_feedback\",\n"
            + "        \"fTable\":\"pg_dwd_smarty_badcase\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_s360_group_frontend_flow\",\n"
            + "        \"fTable\":\"pg_dwd_im_group_risk_ctrl_access\"\n"
            + "    }"
            + "]";

}
