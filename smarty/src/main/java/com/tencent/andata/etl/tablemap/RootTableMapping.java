package com.tencent.andata.etl.tablemap;

public class RootTableMapping {
    public static final String PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"annotate_data\",\n"
            + "        \"fTable\": \"ods_smarty_po_annotate_data\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"cc_qidian_record\",\n"
            + "        \"fTable\": \"ods_smarty_po_cc_source\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"custom_labels\",\n"
            + "        \"fTable\": \"ods_smarty_po_custom_labels\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"public_opinion_keywords_source\",\n"
            + "        \"fTable\": \"ods_smarty_po_mc_source\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"public_opinion_webim_source\",\n"
            + "        \"fTable\": \"ods_smarty_po_webim_source\"\n"
            + "    }\n"
            + "]";
}
