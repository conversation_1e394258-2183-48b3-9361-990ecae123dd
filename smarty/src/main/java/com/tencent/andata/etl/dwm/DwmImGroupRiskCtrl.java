package com.tencent.andata.etl.dwm;

import static com.tencent.andata.etl.sql.DwmImGroupRiskCtrlSql.CALCULATE_FIRST_VIEW_TIME_SQL;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.google.common.collect.Lists;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;

import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwmImGroupRiskCtrl {

    private static final Logger logger = LoggerFactory.getLogger(DwmImGroupRiskCtrl.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        Table tbl = tEnv.sqlQuery(CALCULATE_FIRST_VIEW_TIME_SQL);

        DataStream<Row> ds = tEnv.toChangelogStream(
                tbl,
                Schema.newBuilder().primaryKey("value_of_primary_key").build(),
                ChangelogMode.upsert());

        SingleOutputStreamOperator<Row> dataStream = ds
                .keyBy(row -> row.<String>getFieldAs("value_of_primary_key"))
                .process(new FirstInteractFunction())
                .returns(ds.getType())
                .uid("first-interact-function");

        Table resultTable = tEnv.fromChangelogStream(
                dataStream,
                Schema.newBuilder().primaryKey("value_of_primary_key").build(),
                ChangelogMode.upsert()
        );

        tEnv.createTemporaryView("im_group_risk_statistic_view", resultTable);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "im_group_risk_statistic_view",
                "pg_sink_dwm_im_group_risk_ctrl",
                tEnv.from("pg_sink_dwm_im_group_risk_ctrl"),
                PGSQL
        ));
        stmtSet.addInsertSql(insertIntoSql(
                "im_group_risk_statistic_view",
                "flink_starrocks_dwm_im_group_risk_ctrl",
                tEnv.from("flink_starrocks_dwm_im_group_risk_ctrl"),
                ROCKS
        ));
    }

    private static class FirstInteractFunction extends KeyedProcessFunction<String, Row, Row> {

        private ListState<String> respState;

        private ValueState<HashMap<String, String>> viewState;

        private DateTimeFormatter df;

        @Override
        public void open(Configuration parameters) {
            // owner或leader查看风控告警信息的状态
            ValueStateDescriptor<HashMap<String, String>> viewStateDescriptor =
                    new ValueStateDescriptor<>("view-state",
                            TypeInformation.of(new TypeHint<HashMap<String, String>>() {
                            }));

            viewState = getRuntimeContext().getState(viewStateDescriptor);

            // 客服响应风控告警的状态
            ListStateDescriptor<String> respStateDescriptor = new ListStateDescriptor<>("resp-state", String.class);

            respState = getRuntimeContext().getListState(respStateDescriptor);

            df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        }


        @Override
        public void processElement(Row input, KeyedProcessFunction<String, Row, Row>.Context ctx, Collector<Row> out)
                throws Exception {

            // owner or leader view
            HashMap<String, String> viewMap = viewState.value();

            if (viewMap == null) {
                viewMap = new HashMap<>();
            }

            String owner = input.getFieldAs("owner");
            String ownerFirstViewTime = viewMap.get(owner);
            String leader = input.getFieldAs("leader");
            String leaderFirstViewTime = viewMap.get(leader);
            String viewTime = input.getFieldAs("view_time");
            String viewerRtx = input.getFieldAs("viewer_rtx");

            if (!StringUtils.isEmpty(owner) && owner.equals(viewerRtx)
                    && (StringUtils.isEmpty(ownerFirstViewTime) || ownerFirstViewTime.compareTo(viewTime) > 0)) {
                viewMap.put(owner, viewTime);
            }

            if (!StringUtils.isEmpty(leader) && leader.equals(viewerRtx)
                    && (StringUtils.isEmpty(leaderFirstViewTime) || leaderFirstViewTime.compareTo(viewTime) > 0)) {
                viewMap.put(leader, viewTime);
            }

            // 更新状态
            viewState.update(viewMap);

            input.setField(
                    "owner_first_view_time",
                    StringUtils.isEmpty(viewMap.get(owner))
                            ? null : LocalDateTime.parse(viewMap.get(owner).substring(0, 19), df)
            );
            input.setField(
                    "leader_first_view_time",
                    StringUtils.isEmpty(viewMap.get(leader))
                            ? null : LocalDateTime.parse(viewMap.get(leader).substring(0, 19), df)
            );

            // 客服响应
            List<String> responseList = Lists.newArrayList(respState.get());

            String key = ctx.getCurrentKey();
            String kfRspTime = input.getFieldAs("msg_time"); // 消息发送时间
            String kfRtx = input.getFieldAs("customer_service_rtx"); // 发言人rtx
            String kfRespContent = input.getFieldAs("display_content"); // 发言内容
            // 客服首次响应时间
            String kfFstRespTime = responseList.stream().skip(1).findFirst().orElse("");

            // 取response中的第二个元素，即客服首次响应时间
            // response 转为list

            if (
                    StringUtils.isEmpty(kfFstRespTime)
                            || StringUtils.isNotEmpty(kfRspTime)
                            && kfFstRespTime.compareTo(kfRspTime) > 0
            ) {
                responseList.clear();
                responseList.add(0, StringUtils.isEmpty(kfRtx) ? "" : kfRtx);
                responseList.add(1, StringUtils.isEmpty(kfRspTime) ? "" : kfRspTime);
                responseList.add(2, StringUtils.isEmpty(kfRespContent) ? "" : kfRespContent);
            }

            // 更新状态
            respState.update(responseList);

            input.setField("first_response_css", responseList.get(0));
            input.setField(
                    "css_first_response_time",
                    StringUtils.isEmpty(kfRspTime)
                            ? null : LocalDateTime.parse(responseList.get(1).substring(0, 19), df)
            );
            input.setField("css_first_response_content", responseList.get(2));

            // 输出结果
            out.collect(input);
        }
    }
}
