package com.tencent.andata.etl.dwd;

import static com.tencent.andata.etl.sql.DwdImGroupRiskCtrlSql.QUERY_GROUP_ACCESS_RECORD_SQL;
import com.tencent.andata.etl.sql.DwdSmartyCommonSql;
import com.tencent.andata.etl.tablemap.DwdSmartyCommonMapping;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import com.tencent.andata.utils.struct.DatabaseConf;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

@Builder
public class DwdSmartyCommon {
    private static final Logger logger = LoggerFactory.getLogger(DwdSmartyCommon.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // iceberg表注册
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwdSmartyCommonMapping.ICEBERG_TO_FLINK_TABLE, ArrayNode.class),
                tEnv,
                catalog
        );
        // PG表注册
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(
                        DwdSmartyCommonMapping.rdbTable2FlinkTable,
                        ArrayNode.class
                ),
                PGSQL,
                tEnv
        );

        // 访问记录 计算
        tEnv.createTemporaryView("im_group_risk_ctrl_access_view", tEnv.sqlQuery(QUERY_GROUP_ACCESS_RECORD_SQL));

        tEnv.createTemporaryView(
                "dwd_im_group_risk_ctrl_access",
                tEnv.sqlQuery(DwdSmartyCommonSql.DWD_SMARTY_RISK_ACCESS_RECORD_SQL)
        );
        // 访问记录 出库PG
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "dwd_im_group_risk_ctrl_access",
                "pg_dwd_im_group_risk_ctrl_access",
                tEnv.from("pg_dwd_im_group_risk_ctrl_access"),
                PGSQL
        )).addInsertSql(insertIntoSql(
                "im_group_risk_ctrl_access_view",
                "iceberg_sink_dwd_im_group_risk_ctrl_access",
                tEnv.from("iceberg_sink_dwd_im_group_risk_ctrl_access"),
                ICEBERG
        ));
        // BadCase 数据去重 & 计算
        tEnv.createTemporaryView(
                "f_dwd_smarty_badcase",
                tEnv.sqlQuery(DwdSmartyCommonSql.DWD_SMARTY_BAD_CASE_SQL)
        );
        // BadCase 出库PG
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "f_dwd_smarty_badcase",
                "pg_dwd_smarty_badcase",
                tEnv.from("pg_dwd_smarty_badcase"),
                PGSQL
        ));
        // BadCase 出库iceberg
        flinkEnv.stmtSet().addInsertSql(insertIntoSql(
                "f_dwd_smarty_badcase",
                "iceberg_dwd_smarty_badcase",
                tEnv.from("iceberg_dwd_smarty_badcase"),
                ICEBERG
        ));
    }
}
