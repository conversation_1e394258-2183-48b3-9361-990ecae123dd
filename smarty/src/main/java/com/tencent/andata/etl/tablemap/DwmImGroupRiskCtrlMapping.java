package com.tencent.andata.etl.tablemap;

public class DwmImGroupRiskCtrlMapping {

    public static final String ICEBERG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_group_risk_ctrl_access\",\n"
            + "        \"fTable\":\"iceberg_source_dwd_opinion_access_record\",\n"
            + "        \"primaryKey\":\"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_group_risk_ctrl\",\n"
            + "        \"fTable\":\"iceberg_source_dwd_im_group_risk_ctrl\",\n"
            + "        \"primaryKey\":\"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_group_message\",\n"
            + "        \"fTable\":\"iceberg_source_dwd_im_group_message\",\n"
            + "        \"primaryKey\":\"pk\"\n"
            + "    }\n"
            + "]";

    public static final String PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_im_group_risk_ctrl\",\n"
            + "        \"fTable\": \"pg_sink_dwm_im_group_risk_ctrl\"\n"
            + "    }\n"
            + "]";
}