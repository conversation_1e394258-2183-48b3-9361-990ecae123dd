package com.tencent.andata.etl.sql;

public class DwdImGroupRiskCtrlSql {

    public static final String QUERY_IM_GROUP_RISK_CTRL_SQL = ""
            + "SELECT\n"
            + "  `value_of_primary_key`,\n"
            + "  `record_update_time`,\n"
            + "  CAST(`id` AS STRING) AS `risk_id`,\n"
            + "  `statement` AS `risk_content`,\n"
            + "  alarm_url AS `risk_link`,\n"
            + "  '' AS `ticket_blong`,\n"
            + "  '' AS `queue`,\n"
            + "  '' AS `agent_id`,\n"
            + "  `group_id`,\n"
            + "  '' AS `group_name`,\n"
            + "  '' AS `ltc_id`,\n"
            + "  '' AS `ltc_name`,\n"
            + "  `service_name` AS `service_provider`,\n"
            + "  `service_channel`,\n"
            + "  CAST(`uin` AS STRING) AS `uin`,\n"
            + "  '' AS `cid`,\n"
            + "  '' AS `gid`,\n"
            + "  '' AS `gid_name`,\n"
            + "  `customer_name`,\n"
            + "  `need_create_chat`,\n"
            + "  `is_valid`,\n"
            + "  `risk_type_list`,\n"
            + "  `risk_source`,\n"
            + "  `risk_level`,\n"
            + "  `is_complaint`,\n"
            + "  case\n"
            + "     when `is_success_complain` = '' or `is_success_complain` is null then null\n"
            + "     else CAST(`is_success_complain` AS INT) \n"
            + "  end AS `is_success_complaint`,\n"
            + "  `complain_type`,\n"
            + "  `complain_condition`,\n"
            + "  `is_claim`,\n"
            + "  `is_created_chat`,\n"
            + "  `is_annotated`,\n"
            + "  `annotate_user`,\n"
            + "  `annotate_time`,\n"
            + "  `time_delay`,\n"
            + "  `finally_payment`,\n"
            + "  `alarm_time`,\n"
            + "  '' AS `push_time`,\n"
            + "  '' AS `push_model`,\n"
            + "  '' AS `recog_type`,\n"
            + "  '' AS `ticket_id`,\n"
            + "  5 AS `im_group_type`,\n"
            + "  `alarm_person`,\n"
            + "  `process_time`\n"
            + "FROM iceberg_source_ods_public_opinion_production_group  /*+ OPTIONS('streaming'='true', "
            + "'monitor-interval'='1s')*/\n"
            + "\n"
            + "UNION ALL\n"
            + "\n"
            + "SELECT\n"
            + "  `value_of_primary_key`,\n"
            + "  `record_update_time`,\n"
            + "  `request_uid` AS `risk_id`,\n"
            + "  `risk_content`,\n"
            + "  `risk_link`,\n"
            + "  `ticket_blong`,\n"
            + "  `queue`,\n"
            + "  `agent_id`,\n"
            + "  `group_id`,\n"
            + "  `group_name`,\n"
            + "  '' AS `ltc_id`,\n"
            + "  '' AS `ltc_name`,\n"
            + "  '' AS `service_provider`,\n"
            + "  `service_channel`,\n"
            + "  '' AS `uin`,\n"
            + "  '' AS `cid`,\n"
            + "  '' AS `gid`,\n"
            + "  '' AS `gid_name`,\n"
            + "  `customer_name`,\n"
            + "  `need_create_chat`,\n"
            + "  `is_valid`,\n"
            + "  `risk_type_list`,\n"
            + "  `risk_source`,\n"
            + "  `risk_level`,\n"
            + "  0 AS `is_complaint`,\n"
            + "  0 AS `is_success_complaint`,\n"
            + "  '' AS `complain_type`,\n"
            + "  '' AS `complain_condition`,\n"
            + "  '' AS `is_claim`,\n"
            + "  `is_created_chat`,\n"
            + "  `is_annotated`,\n"
            + "  `annotate_user`,\n"
            + "  `annotate_time`,\n"
            + "  0 AS `time_delay`,\n"
            + "  '' AS `finally_payment`,\n"
            + "  `alarm_time`,\n"
            + "  `system_push_time` AS `push_time`,\n"
            + "  `push_model`,\n"
            + "  `recog_type`,\n"
            + "  '' AS `ticket_id`,\n"
            + "  4 AS `im_group_type`,\n"
            + "  '' AS `alarm_person`,\n"
            + "  `process_time`\n"
            + "FROM iceberg_source_wechatpay_group /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + "\n"
            + "UNION ALL\n"
            + "\n"
            + "SELECT\n"
            + "  `value_of_primary_key`,\n"
            + "  `record_update_time`,\n"
            + "  `request_uid` AS `risk_id`,\n"
            + "  `risk_content`,\n"
            + "  '' AS `risk_link`,\n"
            + "  '' AS `ticket_blong`,\n"
            + "  '' AS `queue`,\n"
            + "  '' AS `agent_id`,\n"
            + "  `group_id`,\n"
            + "  `group_name`,\n"
            + "  `ltc_id`,\n"
            + "  `ltc_name`,\n"
            + "  `service_name` AS `service_provider`,\n"
            + "  `service_channel`,\n"
            + "  `uin`,\n"
            + "  `cid`,\n"
            + "  `gid`,\n"
            + "  `gid_name`,\n"
            + "  `customer_name`,\n"
            + "  `need_create_chat`,\n"
            + "  `is_valid`,\n"
            + "  `risk_type_list`,\n"
            + "  `risk_source`,\n"
            + "  `risk_level`,\n"
            + "  `is_complaint`,\n"
            + "  `is_success_complain` AS `is_success_complaint`,\n"
            + "  `complain_type`,\n"
            + "  `complain_condition`,\n"
            + "  `is_claim`,\n"
            + "  `is_created_chat`,\n"
            + "  `is_annotated`,\n"
            + "  `annotate_user`,\n"
            + "  `annotate_time`,\n"
            + "  `time_delay`,\n"
            + "  `finally_payment`,\n"
            + "  `alarm_time`,\n"
            + "  `system_push_time` AS `push_time`,\n"
            + "  `push_model`,\n"
            + "  `recog_type`,\n"
            + "  '' AS `ticket_id`,\n"
            + "  3 AS `im_group_type`,\n"
            + "  '' AS `alarm_person`,\n"
            + "  `process_time`\n"
            + "FROM iceberg_source_private_cloud_casebycase_group "
            + " /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + "\n"
            + "UNION ALL\n"
            + "\n"
            + "SELECT\n"
            + "  `value_of_primary_key`,\n"
            + "  `record_update_time`,\n"
            + "  `request_uid` AS `risk_id`,\n"
            + "  `risk_content`,\n"
            + "  `url` AS `risk_link`,\n"
            + "  '' AS `ticket_blong`,\n"
            + "  '' AS `queue`,\n"
            + "  '' AS `agent_id`,\n"
            + "  `group_id`,\n"
            + "  '' AS `group_name`,\n"
            + "  `ltc_id`,\n"
            + "  `ltc_name`,\n"
            + "  `service_name` AS `service_provider`,\n"
            + "  `service_channel`,\n"
            + "  `uin`,\n"
            + "  `cid`,\n"
            + "  `gid`,\n"
            + "  `gid_name`,\n"
            + "  `customer_name`,\n"
            + "  `need_create_chat`,\n"
            + "  `is_valid`,\n"
            + "  `risk_type_list`,\n"
            + "  `risk_source`,\n"
            + "  `risk_level`,\n"
            + "  `is_complaint`,\n"
            + "  `is_success_complain` AS `is_success_complaint`,\n"
            + "  `complain_type`,\n"
            + "  `complain_condition`,\n"
            + "  `is_claim`,\n"
            + "  `is_created_chat`,\n"
            + "  `is_annotated`,\n"
            + "  `annotate_user`,\n"
            + "  `annotate_time`,\n"
            + "  `time_delay`,\n"
            + "  `finally_payment`,\n"
            + "  `alarm_time`,\n"
            + "  `system_push_time` AS `push_time`,\n"
            + "  `push_model`,\n"
            + "  `recog_type`,\n"
            + "  '' AS `ticket_id`,\n"
            + "  2 AS `im_group_type`,\n"
            + "  '' AS `alarm_person`,\n"
            + "  `process_time`\n"
            + "FROM iceberg_source_private_cloud_im_group  /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + "UNION ALL\n"
            + "\n"
            + "SELECT\n"
            + "  `value_of_primary_key`,\n"
            + "  `record_update_time`,\n"
            + "  CAST(`id` AS STRING) AS `risk_id`,\n"
            + "  `statement` AS `risk_content`,\n"
            + "  alarm_url AS `risk_link`,\n"
            + "  '' AS `ticket_blong`,\n"
            + "  '' AS `queue`,\n"
            + "  '' AS `agent_id`,\n"
            + "  `group_id`,\n"
            + "  '' AS `group_name`,\n"
            + "  '' AS `ltc_id`,\n"
            + "  '' AS `ltc_name`,\n"
            + "  `service_name` AS `service_provider`,\n"
            + "  `service_channel`,\n"
            + "  CAST(`uin` AS STRING) AS `uin`,\n"
            + "  '' AS `cid`,\n"
            + "  '' AS `gid`,\n"
            + "  '' AS `gid_name`,\n"
            + "  `customer_name`,\n"
            + "  `need_create_chat`,\n"
            + "  `is_valid`,\n"
            + "  `risk_type_list`,\n"
            + "  `risk_source`,\n"
            + "  `risk_level`,\n"
            + "  `is_complaint`,\n"
            + "  case\n"
            + "     when `is_success_complain` = '' or `is_success_complain` is null then null\n"
            + "     else CAST(`is_success_complain` AS INT) \n"
            + "  end AS `is_success_complaint`,\n"
            + "  `complain_type`,\n"
            + "  `complain_condition`,\n"
            + "  `is_claim`,\n"
            + "  `is_created_chat`,\n"
            + "  `is_annotated`,\n"
            + "  `annotate_user`,\n"
            + "  `annotate_time`,\n"
            + "  `time_delay`,\n"
            + "  `finally_payment`,\n"
            + "  `alarm_time`,\n"
            + "  '' AS `push_time`,\n"
            + "  '' AS `push_model`,\n"
            + "  '' AS `recog_type`,\n"
            + "  '' AS `ticket_id`,\n"
            + "  1 AS `im_group_type`,\n"
            + "  `alarm_person`,\n"
            + "  `process_time`\n"
            + "FROM iceberg_source_bigcustomer_im_group  /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n";

    public static String getStaffOrgDdl(String dbHost, int dbPort, String dbName, String dbUser, String dbPassword) {
        return String.format(
                "CREATE TEMPORARY TABLE staff_org\n"
                        + "(\n"
                        + "  login_name STRING,\n"
                        + "  leader STRING\n"
                        + ") WITH (\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'url' = 'jdbc:mysql://%s:%s/%s',\n"
                        + "  'table-name' = 'django_tencent_org_staffs',\n"
                        + "  'username' = '%s',\n"
                        + "  'password' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '200000',\n"
                        + "  'lookup.cache.ttl' = '120 minute'\n"
                        + ")", dbHost, dbPort, dbName, dbUser, dbPassword);

    }

    public static final String JOIN_HBASE_SQL_QUERY = ""
            + "WITH tmp1 AS (\n"
            + "  SELECT\n"
            + "    t1.`value_of_primary_key`,\n"
            + "    t1.`record_update_time`,\n"
            + "    t1.`risk_id`,\n"
            + "    t1.`risk_content`,\n"
            + "    t1.`risk_link`,\n"
            + "    t1.`ticket_blong`,\n"
            + "    t1.`queue`,\n"
            + "    t1.`agent_id`,\n"
            + "    t1.`group_id`,\n"
            + "    t1.`group_name`,\n"
            + "    t1.`ltc_id`,\n"
            + "    t1.`ltc_name`,\n"
            + "    t1.`service_provider`,\n"
            + "    t1.`service_channel`,\n"
            + "    t1.`uin`,\n"
            + "    t1.`cid`,\n"
            + "    t1.`gid`,\n"
            + "    t1.`gid_name`,\n"
            + "    t1.`customer_name`,\n"
            + "    t1.`need_create_chat`,\n"
            + "    t1.`is_valid`,\n"
            + "    t1.`risk_type_list`,\n"
            + "    t1.`risk_source`,\n"
            + "    t1.`risk_level`,\n"
            + "    t1.`is_complaint`,\n"
            + "    t1.`is_success_complaint`,\n"
            + "    t1.`complain_type`,\n"
            + "    t1.`complain_condition`,\n"
            + "    t1.`is_claim`,\n"
            + "    t1.`is_created_chat`,\n"
            + "    t1.`is_annotated`,\n"
            + "    t1.`annotate_user`,\n"
            + "    t1.`annotate_time`,\n"
            + "    t1.`time_delay`,\n"
            + "    t1.`finally_payment`,\n"
            + "    t1.`alarm_time`,\n"
            + "    t1.`push_time`,\n"
            + "    t1.`push_model`,\n"
            + "    t1.`recog_type`,\n"
            + "    COALESCE(t2.`ticket_id`, t1.`ticket_id`) AS `ticket_id`,\n"
            + "    t1.`im_group_type`,\n"
            + "    t1.`alarm_person`,\n"
            + "    t1.`process_time`\n"
            + "  FROM im_group_risk_ctrl_view AS t1\n"
            + "  FULL JOIN iceberg_source_linked_ticket "
            + "     /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/ AS t2\n"
            + "  ON t1.risk_id = t2.uuid\n"
            + "),\n"
            + "tmp2 AS (\n"
            + "  SELECT\n"
            + "    t1.*,\n"
            + "    t2.`sales_supportor` AS `owner`\n"
            + "  FROM tmp1 AS t1\n"
            + "  LEFT JOIN pgsql_source_key_customer_info FOR SYSTEM_TIME AS OF t1.`process_time` AS t2\n"
            + "  ON (t1.`group_id` = t2.`channel_id` AND COALESCE(t1.`group_id`, '') <> '')\n"
            + "),\n"
            + "tmp3 AS (\n"
            + "  SELECT\n"
            + "    tmp2.*,\n"
            + "    t3.group_id as owner_vir_group_id\n"
            + "  FROM tmp2\n"
            + "  LEFT JOIN t006_staff FOR SYSTEM_TIME AS OF tmp2.`process_time` AS t3\n"
            + "  ON (COALESCE(tmp2.`owner`, 'owner') = t3.`user_id` AND COALESCE(tmp2.`group_id`, '') <> '')\n"
            + "),\n"
            + "tmp4 AS (\n"
            + "  SELECT\n"
            + "    tmp3.*,\n"
            + "    COALESCE(staff_group.`q_idr2`, t4.`leader`) as leader\n"
            + "  FROM tmp3\n"
            + "  LEFT JOIN t007_staff_group FOR SYSTEM_TIME AS OF tmp3.`process_time` AS staff_group\n"
            + "  ON (COALESCE(tmp3.`owner_vir_group_id`, -1) = staff_group.`group_id` "
            + "     AND COALESCE(tmp3.`group_id`, '') <> '')\n"
            + "  LEFT JOIN staff_org FOR SYSTEM_TIME AS OF tmp3.`process_time` AS t4\n"
            + "  ON (COALESCE(tmp3.`owner`, 'owner') = t4.`login_name` AND COALESCE(tmp3.`group_id`, '') <> '')\n"
            + "),\n"
            + "tmp5 AS (\n"
            + "  SELECT\n"
            + "    tmp4.*,\n"
            + "    get_json_object(t5.`data`, '$.short_url') AS `short_url`,\n"
            + "    CAST(get_json_object(t5.`data`, '$.status') AS BIGINT) AS `status`,\n"
            + "    CAST(get_json_object(t5.`data`, '$.priority') AS BIGINT) AS `priority`,\n"
            + "    IF(CAST(get_json_object(t5.`data`, '$.service_scene_checked') AS BIGINT) > 0,\n"
            + "       CAST(get_json_object(t5.`data`, '$.service_scene_checked') AS BIGINT),\n"
            + "       CAST(get_json_object(t5.`data`, '$.service_scene') AS BIGINT))\n"
            + "    AS `service_scene_id`\n"
            + "  FROM tmp4\n"
            + "  LEFT JOIN hbase_ticket_base_info FOR SYSTEM_TIME AS OF tmp4.`process_time` AS t5\n"
            + "  ON IF(tmp4.`ticket_id` = '', 'ticket_id', tmp4.`ticket_id`) = t5.rowkey\n"
            + ")\n"
            + "SELECT *\n"
            + "FROM tmp5\n"
            + "WHERE COALESCE(tmp5.`value_of_primary_key`, '') <> ''";

    public static final String QUERY_GROUP_ACCESS_RECORD_SQL = ""
            + "SELECT\n"
            + "  `value_of_primary_key`,\n"
            + "  `record_update_time`,\n"
            + "  IF(`record_id` > 0, CAST(`record_id` AS STRING), `request_uid`) AS `risk_id`,\n"
            + "  `viewer_rtx`,\n"
            + "  `source`,\n"
            + "  `viewer_dept`,\n"
            + "  `event_id`,\n"
            + "  `device`,\n"
            + "  `type`,\n"
            + "  `ticket_id`,\n"
            + "  `url`,\n"
            + "  `content`,\n"
            + "  `level`,\n"
            + "  `customer`,\n"
            + "  `feedback_name`,\n"
            + "  `risk_type`\n"
            + "FROM iceberg_source_ods_opinion_access_record "
            + "  /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/   ";
}