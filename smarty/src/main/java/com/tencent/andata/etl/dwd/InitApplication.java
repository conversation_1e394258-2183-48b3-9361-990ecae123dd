package com.tencent.andata.etl.dwd;

import com.tencent.andata.etl.sql.LookUpJoinTableDDLSql;
import com.tencent.andata.etl.tablemap.*;
import com.tencent.andata.utils.*;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.tencent.andata.etl.sql.DwdImGroupRiskCtrlSql.getStaffOrgDdl;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;


@Builder
public class InitApplication {

    private static final Logger logger = LoggerFactory.getLogger(DwdImGroupRiskCtrl.class);
    private final String icebergDbName;
    private final String pgDbName;
    ParameterTool paraTool;

    /**
     * 运行内容
     *
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        // PG CDC ODS表注册
        DatabaseConf smartyDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "smarty"))
                .build();
        ArrayNode nodes = mapper.readValue(RootTableMapping.PG_TABLE_TO_FLINK_TABLE, ArrayNode.class);
        buildSourceView(smartyDBConf, flinkEnv, nodes, DatabaseEnum.PGSQL, paraTool);
        StreamSupport.stream(nodes.spliterator(), false)
                .map(x -> x.get("rdbTable").asText())
                .forEach(table -> tEnv.createTemporaryView(table + "_time_view", tEnv.sqlQuery("select *,PROCTIME() as process_time from " + table + "_view")));

        // Iceberg表注册
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwdSmartyPublicOpinionMapping.ICEBERG_TO_FLINK_TABLE, ArrayNode.class),
                tEnv,
                catalog
        );
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwdSmartyLinkedTicketMapping.ICEBERG_TO_FLINK_TABLE, ArrayNode.class),
                tEnv,
                catalog
        );
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwdImGroupRiskCtrlMapping.ICEBERG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                tEnv,
                catalog
        );
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwmImGroupRiskCtrlMapping.ICEBERG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                tEnv,
                catalog
        );
        // 工单Hbase表注册
        TableUtils.hbaseTable2FlinkTable(
                "hbase_ticket_base_info",
                "Incident_ticket_base_info",
                "cf", tEnv
        );
        // lookup join表注册
        // 组织架构维表
        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "org"))
                .build();
        tEnv.executeSql(getStaffOrgDdl(
                mysqlDBConf.dbHost,
                mysqlDBConf.dbPort,
                mysqlDBConf.dbName,
                mysqlDBConf.userName,
                mysqlDBConf.password));

        // 虚拟智能组表
        DatabaseConf workDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();
        TableUtils.registerTable(
                tEnv,
                LookUpJoinTableDDLSql.t007StaffPostDDL(workDBConf)
        );
        TableUtils.registerTable(
                tEnv,
                LookUpJoinTableDDLSql.t006StaffDDL(workDBConf)
        );
        // 录音记录表
        DatabaseConf smartyReadDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "smarty_r"))
                .build();
        TableUtils.registerTable(
                tEnv,
                LookUpJoinTableDDLSql.publicOpinionCCRecordDDL(smartyReadDBConf)
        );
        // 标注自定义配置表
        TableUtils.registerTable(
                tEnv,
                LookUpJoinTableDDLSql.smartyCustomFieldConfigDDL(smartyReadDBConf)
        );
        DatabaseConf dataWareReadDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware_r"))
                .build();
        // 公司信息维表
        TableUtils.registerTable(
                tEnv,
                LookUpJoinTableDDLSql.dimCompanyInfoDDL(dataWareReadDBConf)
        );
        // Webim相关信息维表
        TableUtils.registerTable(
                tEnv,
                LookUpJoinTableDDLSql.dwmWebimBaseViewDDL(dataWareReadDBConf)
        );
        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(
                        DwdSmartyPublicOpinionMapping.rdbTable2FlinkTable,
                        ArrayNode.class
                ),
                PGSQL,
                tEnv
        );
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(
                        DwdSmartyLinkedTicketMapping.rdbTable2FlinkTable,
                        ArrayNode.class
                ),
                PGSQL,
                tEnv
        );
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(
                        DwdImGroupRiskCtrlMapping.PG_TABLE_TO_FLINK_TABLE,
                        ArrayNode.class),
                PGSQL,
                tEnv
        );
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(
                        DwmImGroupRiskCtrlMapping.PG_TABLE_TO_FLINK_TABLE,
                        ArrayNode.class),
                PGSQL,
                tEnv
        );
        // StarRocks 表注册
        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();
        TableUtils.rdbTable2FlinkTable(
                rocksDbConf,
                mapper.readValue(
                        DwdSmartyPublicOpinionMapping.starRocksTable2FlinkTable,
                        ArrayNode.class),
                ROCKS,
                tEnv
        );
    }
}
