package com.tencent.andata.etl.tablemap;

public class DwdSmartyLinkedTicketMapping {
    public static final String ICEBERG_TO_FLINK_TABLE = ""
            + "[{\n"
            + "    \"icebergTable\": \"ods_linked_ticket\",\n"
            + "    \"fTable\": \"iceberg_ods_linked_ticket\",\n"
            + "    \"primaryKey\": \"value_of_primary_key\"\n"
            + "}, {\n"
            + "    \"icebergTable\": \"dwd_smarty_linked_ticket\",\n"
            + "    \"fTable\": \"iceberg_dwd_smarty_linked_ticket\",\n"
            + "    \"primaryKey\": \"value_of_primary_key\"\n"
            + "}]";

    public static String rdbTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_smarty_linked_ticket\",\n"
            + "        \"fTable\":\"f_dwd_smarty_linked_ticket\"\n"
            + "    }"
            + "]";
}
