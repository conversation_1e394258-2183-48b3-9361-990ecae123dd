package com.tencent.andata.etl;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.operator.map.RowDataSplitCleanMap;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.TextNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;
import org.apache.iceberg.BaseTable;
import org.apache.iceberg.DistributionMode;
import org.apache.iceberg.Table;
import org.apache.iceberg.TableMetadata;
import org.apache.iceberg.TableOperations;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.sink.FlinkSink;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.bouncycastle.util.Strings;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;


public class ConsumerGroupMessageOdsToDwd {
    protected static final Logger LOG = LoggerFactory.getLogger(ConsumerGroupMessageOdsToDwd.class);
    public static JSONUtils jsonUtils = new JSONUtils();

    /**
     * 获取展示文本
     *
     * @param msgContent
     * @param msgType
     * @return
     * @throws JsonProcessingException
     */
    public static String getDisplayContent(String msgContent, String msgType) throws JsonProcessingException {
        try {
            StringBuilder content = new StringBuilder();
            ObjectNode msgData = jsonUtils.getJSONObjectNodeByString(msgContent);
            String text = parseMsg(msgData.get(msgType), msgType, content).toString();
            return text;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 解析消息
     *
     * @param statement
     * @param msgType
     * @param result
     * @return
     * @throws JsonProcessingException
     */
    public static StringBuilder parseMsg(JsonNode statement, String msgType, StringBuilder result)
            throws JsonProcessingException {
        HashMap<String, String> msgTransMap = new HashMap<String, String>() {{
            put("image", "[图片]");
            put("voice", "[语音]");
            put("video", "[视频]");
            put("file", "[文件]");
            put("chatrecord", "[聊天记录]");
        }};
        if (msgType.equals("text")) {
            if (statement instanceof TextNode) {
                result.append(jsonUtils.getJSONObjectNodeByString(statement.textValue()).get("content").asText());
            } else {
                result.append(statement.get("content").asText());
            }
        } else if (msgType.equals("mixed")) {
            JsonNode node = statement.get("item");
            Iterator<JsonNode> elements = node.elements();
            for (Iterator<JsonNode> it = elements; it.hasNext(); ) {
                ObjectNode item = (ObjectNode) it.next();
                result = parseMsg(item.get("content"), item.get("type").asText(), result);
            }
        } else {
            result.append(msgTransMap.getOrDefault(msgType, "[其他类型消息]"));
        }
        return result;
    }

    /**
     * 入口
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        String parallelism = parameterTool.get("parallelism", "1");
        String dwdGroupMessageEqFieldColumns = parameterTool.get("dwdGroupMessageEqFieldColumns");
        ArrayList<String> eqFieldColumns = new ArrayList<String>() {
        };
        eqFieldColumns.addAll(Arrays.asList(Strings.split(dwdGroupMessageEqFieldColumns, ',')));
        System.out.printf("eqFieldColumn: %s", eqFieldColumns);
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();
        configuration.setString("table.exec.resource.default-parallelism", parallelism);
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        configuration.setString("table.exec.iceberg.infer-source-parallelism.max", parallelism);
        configuration.setString("execution.runtime-mode", "streaming");

        String icebergDbName = parameterTool.get("icebergDbName");
        String odsGroupMessageName = parameterTool.get("odsGroupMessageName");
        Table odsTable = catalog.getTableInstance(
                icebergDbName,
                odsGroupMessageName
        );
        IcebergTableBuilderStrategy odsTableBuilderStrategy = new IcebergTableBuilderStrategy(odsTable);
        String odsTableDDL = FlinkTableDDL.builder()
                .tableBuilderStrategy(odsTableBuilderStrategy)
                .flinkTableName("ods_im_group_table")
                .build();
        TableUtils.registerTable(
                flinkEnv.streamTEnv(),
                odsTableDDL
        );

        String selectSql = ""
                + "select /*+ OPTIONS ('streaming' = 'true', 'monitor-interval' = '10s') */\n"
                + "    message\n"
                + "from ods_im_group_table\n"
                + "where ods_create_time > '2022-05-01'";

        DataStream<Tuple2<Boolean, RowData>> odsDS = flinkEnv.streamTEnv().toRetractStream(
                flinkEnv.streamTEnv().sqlQuery(selectSql),
                RowData.class
        );

        // 入库DWD
        String dwdGroupMessageName = parameterTool.get("dwdGroupMessageName");
        TableLoader dwdGroupMessageTableLoader = catalog.getTableLoaderInstance(
                icebergDbName,
                dwdGroupMessageName
        );
        Table dwdGroupMessageTable = dwdGroupMessageTableLoader.loadTable();
        RowType dwdGroupMessageTableRowType = catalog.getTableRowType(dwdGroupMessageTable);
        final JsonRowDataDeserializationSchema dwdGroupMessageDeserializationSchema =
                new JsonRowDataDeserializationSchema(
                        dwdGroupMessageTableRowType,
                        InternalTypeInfo.of(dwdGroupMessageTableRowType),
                        false,
                        true,
                        TimestampFormat.SQL
                );
        SingleOutputStreamOperator<RowData> dwdGroupMessageDS = odsDS
                .filter(new FilterFunction<Tuple2<Boolean, RowData>>() {
                    @Override
                    public boolean filter(Tuple2<Boolean, RowData> booleanRowDataTuple2) throws Exception {
                        return booleanRowDataTuple2.f0;
                    }
                })
                .flatMap(
                        new FlatMapFunction<Tuple2<Boolean, RowData>, RowData>() {
                            @Override
                            public void flatMap(
                                    Tuple2<Boolean, RowData> booleanRowDataTuple2,
                                    Collector<RowData> collector
                            ) throws Exception {
                                try {
                                    String message = booleanRowDataTuple2.f1.getString(0).toString();
                                    ObjectNode msgData = jsonUtils.getJSONObjectNodeByString(message);
                                    String msgID = msgData.get("msg_id").asText();
                                    String groupID = msgData.get("group_id").asText();
                                    msgData.put(
                                            "pk",
                                            String.format("%s-%s", groupID, msgID)
                                    );
                                    long msgTimestamp = msgData.get("msg_time").asLong();
                                    msgData.put(
                                            "msg_time",
                                            DateFormatUtils.cstTimestampToUTCString(msgTimestamp)
                                    );
                                    msgData.put(
                                            "display_content",
                                            getDisplayContent(
                                                    msgData.get("content").toString(),
                                                    msgData.get("msg_type").asText()
                                            )
                                    );
                                    collector.collect(dwdGroupMessageDeserializationSchema.deserialize(
                                            msgData.toString().getBytes(StandardCharsets.UTF_8)));
                                } catch (Exception e) {
                                    // 处理报错的话则跳过
                                    e.printStackTrace();
                                }
                            }
                        }
                );
        // 设置版本
        TableOperations upsertTableOperation = ((BaseTable) dwdGroupMessageTable).operations();
        TableMetadata upsertTableMetadata = upsertTableOperation.current();
        upsertTableOperation.commit(upsertTableMetadata, upsertTableMetadata.upgradeToFormatVersion(2));

        // Sink入库
        FlinkSink.forRowData(dwdGroupMessageDS.map(
                        new RowDataSplitCleanMap(
                                CDCUtils.convertRowTypeToFieldGetterTupleList(
                                        catalog.getTableRowType(dwdGroupMessageTable)
                                )
                        )
                ))
                .table(dwdGroupMessageTable)
                .tableLoader(dwdGroupMessageTableLoader)
                .distributionMode(DistributionMode.HASH)
                .writeParallelism(Integer.parseInt(parallelism))
                .equalityFieldColumns(eqFieldColumns)
                .upsert(true)
                .append()
                .disableChaining();
        CheckpointConfig ckConfig = flinkEnv.env().getCheckpointConfig();
        ckConfig.setCheckpointInterval(20 * 1000L);
        flinkEnv.env().disableOperatorChaining();
        flinkEnv.env().execute("Smarty Application");
    }
}
