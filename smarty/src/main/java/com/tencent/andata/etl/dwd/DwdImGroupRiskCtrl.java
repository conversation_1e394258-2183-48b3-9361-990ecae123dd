package com.tencent.andata.etl.dwd;

import static com.tencent.andata.etl.sql.DwdImGroupRiskCtrlSql.JOIN_HBASE_SQL_QUERY;
import static com.tencent.andata.etl.sql.DwdImGroupRiskCtrlSql.QUERY_GROUP_ACCESS_RECORD_SQL;
import static com.tencent.andata.etl.sql.DwdImGroupRiskCtrlSql.QUERY_IM_GROUP_RISK_CTRL_SQL;
import static com.tencent.andata.etl.sql.DwdImGroupRiskCtrlSql.getStaffOrgDdl;
import static com.tencent.andata.etl.tablemap.DwdImGroupRiskCtrlMapping.ICEBERG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.etl.tablemap.DwdImGroupRiskCtrlMapping.PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdImGroupRiskCtrl {

    private static final Logger logger = LoggerFactory.getLogger(DwdImGroupRiskCtrl.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        // all risk control group message union to one table
        tEnv.createTemporaryView("im_group_risk_ctrl_view", tEnv.sqlQuery(QUERY_IM_GROUP_RISK_CTRL_SQL));

        // lookup ticket base info and im group owner from hbase/pgsql
        tEnv.createTemporaryView("im_group_link_join_view", tEnv.sqlQuery(JOIN_HBASE_SQL_QUERY));

        tEnv.createTemporaryView("im_group_risk_ctrl_access_view", tEnv.sqlQuery(QUERY_GROUP_ACCESS_RECORD_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();

        stmtSet.addInsertSql(insertIntoSql(
                "im_group_link_join_view",
                "iceberg_sink_dwd_im_group_risk_ctrl",
                tEnv.from("iceberg_sink_dwd_im_group_risk_ctrl"),
                ICEBERG
        )).addInsertSql(insertIntoSql(
                "im_group_risk_ctrl_access_view",
                "iceberg_sink_dwd_im_group_risk_ctrl_access",
                tEnv.from("iceberg_sink_dwd_im_group_risk_ctrl_access"),
                ICEBERG
        ));
    }
}