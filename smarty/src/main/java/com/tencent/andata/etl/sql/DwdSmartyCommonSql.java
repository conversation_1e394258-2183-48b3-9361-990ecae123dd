package com.tencent.andata.etl.sql;

public class DwdSmartyCommonSql {

    public static String DWD_SMARTY_BAD_CASE_SQL = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        *,\n"
            + "        row_number() over(partition by value_of_primary_key order by record_update_time desc) rn\n"
            + "    from iceberg_ods_conversation_badcase_feedback "
            + "     /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + "), tmp2 as (\n"
            + "    select\n"
            + "        value_of_primary_key,\n"
            + "        record_update_time,\n"
            + "        feedback_user,\n"
            + "        feedback_content,\n"
            + "        conversation_id,\n"
            + "        conversation_content,\n"
            + "        risk_type,\n"
            + "        ticket_id,\n"
            + "        source,\n"
            + "        feedback_link,\n"
            + "        feedback_group_name,\n"
            + "        case \n"
            + "            when request_uid = '' or request_uid is null then\n"
            + "                REGEXP_EXTRACT(COALESCE(feedback_link, ''), '.*?\\?id=(.*?)&.*?', 1)\n"
            + "            else request_uid\n"
            + "        end as request_uid\n"
            + "    from tmp1\n"
            + "    where rn = 1\n"
            + ")\n"
            + "select\n"
            + "    *\n"
            + "from tmp2";

    public static final String DWD_SMARTY_RISK_ACCESS_RECORD_SQL = ""
            + "select\n"
            + "    value_of_primary_key,\n"
            + "    record_update_time,\n"
            + "    case\n"
            + "        when IS_DIGIT(risk_id) then cast(risk_id as bigint)\n"
            + "        else cast(null as bigint)\n"
            + "    end as source_id,\n"
            + "    viewer_rtx as rtx_name,\n"
            + "    record_update_time as click_time,\n"
            + "    source as alarm_source,\n"
            + "    viewer_dept as user_organization,\n"
            + "    event_id,\n"
            + "    cast(ticket_id as bigint) as ticket_id,\n"
            + "    type,\n"
            + "    device,\n"
            + "    risk_id as request_uid,\n"
            + "    url,\n"
            + "    content,\n"
            + "    level,\n"
            + "    customer,\n"
            + "    feedback_name,\n"
            + "    risk_type \n"
            + "from im_group_risk_ctrl_access_view";
}
