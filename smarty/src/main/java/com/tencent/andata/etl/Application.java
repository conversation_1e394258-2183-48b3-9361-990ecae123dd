package com.tencent.andata.etl;

import com.tencent.andata.etl.dwd.DwdImGroupRiskCtrl;
import com.tencent.andata.etl.dwd.DwdSmartyCommon;
import com.tencent.andata.etl.dwd.DwdSmartyLinkedTicket;
import com.tencent.andata.etl.dwd.DwdSmartyPublicOpinion;
import com.tencent.andata.etl.dwd.InitApplication;
import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

import com.tencent.andata.etl.dwm.DwmImGroupRiskCtrl;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.udf.ToJson;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    enum SubApplication {
        PUBLIC_OPINION, GROUP_RISK
    }

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        // 注册hive udf
        flinkEnv.hiveModuleV2().registryHiveUDF("to_json", ToJson.class.getName());

        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        String parallelism = parameterTool.get("parallelism", "1");
        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();
        configuration.setString("pipeline.name", "AnSmarty Risk Control Application");
        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");

        // 状态保留2天
        configuration.setString("table.exec.state.ttl", "172800000");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.dynamic-table-options.enabled", "true");

        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        configuration.setString("table.exec.resource.default-parallelism", parallelism);

        // 优化join性能
        configuration.setString("table.optimizer.join-reorder-enabled", "true");
        configuration.setString("table.optimizer.multiple-input-enabled", "true");
        configuration.setString("table.exec.disabled-operators", "NestedLoopJoin");
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");

        // get iceberg db name and pg db name from args
        String pgDbName = parameterTool.get("pgDbName");
        String icebergDbName = parameterTool.get("icebergDbName");
        String subApplicationStr = parameterTool.get("subApplication");
        List<SubApplication> subAppList = Arrays.stream(subApplicationStr.split(","))
                .map(SubApplication::valueOf)
                .collect(Collectors.toList());


        // instantiate the DWD ETL
        List<Object> appList = new ArrayList<>();
        appList.add(InitApplication.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).paraTool(parameterTool).build());
        if (subAppList.contains(SubApplication.PUBLIC_OPINION)) {
            appList.add(DwdSmartyPublicOpinion.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(DwdSmartyLinkedTicket.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(DwdSmartyCommon.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        }
        if (subAppList.contains(SubApplication.GROUP_RISK)) {
            appList.add(DwdImGroupRiskCtrl.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(DwmImGroupRiskCtrl.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        }

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));

        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("AnSmarty Risk Control Application");
    }
}