package com.tencent.andata.etl.sql;

public class DwdSmartyPublicOpinionSQL {

    public static String DWD_SMARTY_PUBLIC_OPINION_MC = ""
            + "select\n"
            + "    concat('1-', cast(id as string)) as value_of_primary_key,\n"
            + "    id,\n"
            + "    `statement` as risk_content,\n"
            + "    '' as risk_link,\n"
            + "    company_name as service_provider,\n"
            + "    service_channel as service_channel,\n"
            + "    customer_uin as uin,\n"
            + "    customer_name as customer_name,\n"
            + "    risk_type_desc as risk_type_list,\n"
            + "    alarm_time as alarm_time,\n"
            + "    hit_model as push_model,\n"
            + "    cast(ticket_id as string) as ticket_id,\n"
            + "    1 as opinion_type,\n"
            + "    url as short_url,\n"
            + "    is_alarmed as is_alarmed,\n"
            + "    is_semantic_hit as is_semantic_hit,\n"
            + "    keywords as keywords,\n"
            + "    send_time as send_time,\n"
            + "    '' as conversation_id,\n"
            + "    0 as need_feature,\n"
            + "    8 as project_id,\n"
            + "    feature_json as feature_json,\n"
            + "    case\n"
            + "           when role = '客服' and sender_name <> '' then sender_name\n"
            + "           else role\n"
            + "      end as role,\n"
            + "    process_time as process_time\n"
            + "from public_opinion_keywords_source_time_view\n"
            + "where create_time > '2024-08-01'";

    public static String SMARTY_PUBLIC_OPINION_CC_ODS_PRUNE = ""
            + "select\n"
            + "     *\n"
            + "from cc_qidian_record_time_view\n"
            + "where is_alarmed = 1 and create_time > '2024-08-01'\n";

    public static String DWD_SMARTY_PUBLIC_OPINION_CC = ""
            + "with ods_cc_table as (\n"
            + "     select \n"
            + "         *,\n"
            + "         PROCTIME() as proc_time\n"
            + "     from ods_smarty_po_cc_source_prune\n"
            + "),cc_record_detail as (\n"
            + "    select\n"
            + "        t1.call_id,\n"
            + "        t2.content as record_content,\n"
            + "        t2.role as role\n"
            + "    from ods_cc_table as t1\n"
            + "    left join public_opinion_cc_record FOR SYSTEM_TIME AS OF t1.proc_time AS t2\n"
            + "    on t1.call_id = t2.call_id_id and t2.is_semantic_hit = 1\n"
            + "), cc_record as (\n"
            + "    select\n"
            + "        call_id,\n"
            + "        LISTAGG(record_content, ',') as risk_content,\n"
            + "        max(role) as role\n"
            + "    from cc_record_detail\n"
            + "    group by call_id\n"
            + "), tmp1 as (\n"
            + "    select \n"
            + "        id,\n"
            + "        ticket_id,\n"
            + "        service_channel,\n"
            + "        alarm_time,\n"
            + "        is_alarmed,\n"
            + "        is_semantic_hit,\n"
            + "        send_time,\n"
            + "        hit_model,\n"
            + "        risk_type,\n"
            + "        call_id,\n"
            + "        proc_time\n"
            + "    from ods_cc_table\n"
            + "), tmp2 as (\n"
            + "    select \n"
            + "        tmp1.*,\n"
            + "        t2.risk_content,\n"
            + "        t2.role\n"
            + "    from tmp1 \n"
            + "    left join cc_record t2\n"
            + "    on tmp1.call_id = t2.call_id\n"
            + "), tmp3 as (\n"
            + "    select \n"
            + "        tmp2.*,\n"
            + "        CAST(get_json_object(t3.data, '$.company_id') AS BIGINT) AS company_id,\n"
            + "        get_json_object(t3.data, '$.short_url') AS short_url\n"
            + "    from tmp2\n"
            + "    left join hbase_ticket_base_info FOR SYSTEM_TIME AS OF tmp2.proc_time AS t3\n"
            + "    on cast(tmp2.ticket_id as string) = t3.rowkey\n"
            + "), tmp4 as (\n"
            + "    select \n"
            + "        tmp3.*,\n"
            + "        t4.company_name\n"
            + "    from tmp3\n"
            + "    left join dim_company_info FOR SYSTEM_TIME AS OF tmp3.proc_time AS t4\n"
            + "    on tmp3.company_id = t4.company_id\n"
            + ")\n"
            + "select\n"
            + "    concat('2-', cast(id as string)) as value_of_primary_key,\n"
            + "    id,\n"
            + "    risk_content as risk_content,\n"
            + "    '' as risk_link,\n"
            + "    company_name as service_provider,\n"
            + "    service_channel as service_channel,\n"
            + "    '' as uin,\n"
            + "    '' as customer_name,\n"
            + "    risk_type as risk_type_list,\n"
            + "    alarm_time as alarm_time,\n"
            + "    hit_model as push_model,\n"
            + "    cast(ticket_id as string) as ticket_id,\n"
            + "    2 as opinion_type,\n"
            + "    short_url as short_url,\n"
            + "    is_alarmed as is_alarmed,\n"
            + "    is_semantic_hit as is_semantic_hit,\n"
            + "    '' as keywords,\n"
            + "    send_time as send_time,\n"
            + "    '' as conversation_id,\n"
            + "    0 as need_feature,\n"
            + "    10 as project_id,\n"
            + "    '' as feature_json,\n"
            + "    role as role,\n"
            + "    proc_time as process_time\n"
            + " from tmp4";

    public static String SMARTY_PUBLIC_OPINION_WEBIM_ODS_PRUNE = ""
            + "    select\n"
            + "        id,\n"
            + "        conversation_id,\n"
            + "        keywords,\n"
            + "        alarm_time,\n"
            + "        `statement`,\n"
            + "        service_channel,\n"
            + "        is_semantic_hit,\n"
            + "        is_alarmed,\n"
            + "        send_time,\n"
            + "        hit_model,\n"
            + "        customer_name,\n"
            + "        customer_uin,\n"
            + "        risk_type_desc,\n"
            + "        need_feature,\n"
            + "        source,\n"
            + "        feature_json,\n"
            + "         case\n"
            + "           when role = '客服' and sender_name <> '' then sender_name\n"
            + "           else role\n"
            + "         end as role\n"
            + "    from public_opinion_webim_source_time_view\n"
            + "     where create_time > '2024-08-01'";

    public static String DWD_SMARTY_PUBLIC_OPINION_WEBIM = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        *,\n"
            + "     PROCTIME() as process_time\n"
            + "    from ods_smarty_po_webim_source_prune\n"
            + "), tmp2 as (\n"
            + "    select\n"
            + "        tmp1.*,\n"
            + "        t2.conversation_ticket_ids as ticket_id\n"
            + "    from tmp1\n"
            + "    left join dwm_webim_base_view FOR SYSTEM_TIME AS OF tmp1.process_time t2\n"
            + "    on tmp1.conversation_id = t2.conversation_id\n"
            + "), tmp3 as (\n"
            + "    select\n"
            + "        tmp2.*,\n"
            + "        get_json_object(t3.data, '$.short_url') AS short_url\n"
            + "    from tmp2\n"
            + "    left join hbase_ticket_base_info FOR SYSTEM_TIME AS OF tmp2.process_time AS t3\n"
            + "    on if(tmp2.ticket_id is null or tmp2.ticket_id = '', 'ticket_id', tmp2.ticket_id) = t3.rowkey\n"
            + ")\n"
            + "select\n"
            + "    concat('3-', cast(id as string)) as value_of_primary_key,\n"
            + "    id,\n"
            + "    `statement` as risk_content,\n"
            + "    '' as risk_link,\n"
            + "    '' as service_provider,\n"
            + "    service_channel as service_channel,\n"
            + "    customer_uin as uin,\n"
            + "    customer_name as customer_name,\n"
            + "    risk_type_desc as risk_type_list,\n"
            + "    alarm_time as alarm_time,\n"
            + "    hit_model as push_model,\n"
            + "    cast(ticket_id as string) as ticket_id,\n"
            + "    3 as opinion_type,\n"
            + "    short_url as short_url,\n"
            + "    is_alarmed as is_alarmed,\n"
            + "    is_semantic_hit as is_semantic_hit,\n"
            + "    keywords as keywords,\n"
            + "    send_time as send_time,\n"
            + "    conversation_id as conversation_id,\n"
            + "    need_feature as need_feature,\n"
            + "    9 as project_id, \n"
            + "    feature_json as feature_json,\n"
            + "    role as role,\n"
            + "    process_time as process_time\n"
            + "from tmp3";

    public static String DWD_CUSTOM_LABEL_SQL = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        t1.id,\n"
            + "        t1.annotation_id,\n"
            + "        t1.project_id,\n"
            + "        t1.cf_key,\n"
            + "        case\n"
            + "            when t2.type in ('el-select', 'el-radio-group') then \n"
            + "               get_json_object(t2.options, concat('$.',t1.cf_value_key))"
            + "            when t2.type = 'el-input' then\n"
            + "               t1.cf_value_key\n"
            + "        end as cf_value\n"
            + "    from custom_labels_time_view as t1\n"
            + "    left join custom_field_config FOR SYSTEM_TIME AS OF t1.process_time as t2\n"
            + "    on t1.cf_id = t2.id\n"
            + ")\n"
            + "select\n"
            + "    annotation_id,\n"
            + "    project_id,\n"
            + "    max(\n"
            + "        case \n"
            + "            when cf_key = 'custom_field_1' then cf_value\n"
            + "            else ''\n"
            + "        end \n"
            + "    ) as custom_field_1,\n"
            + "    max(\n"
            + "        case \n"
            + "            when cf_key = 'custom_field_2' then cf_value\n"
            + "            else ''\n"
            + "        end \n"
            + "    ) as custom_field_2,\n"
            + "    max(\n"
            + "        case \n"
            + "            when cf_key = 'custom_field_3' then cf_value\n"
            + "            else ''\n"
            + "        end \n"
            + "    ) as custom_field_3,\n"
            + "    max(\n"
            + "        case \n"
            + "            when cf_key = 'custom_field_4' then cf_value\n"
            + "            else ''\n"
            + "        end \n"
            + "    ) as custom_field_4,\n"
            + "    max(\n"
            + "        case \n"
            + "            when cf_key = 'custom_field_5' then cf_value\n"
            + "            else ''\n"
            + "        end \n"
            + "    ) as custom_field_5,\n"
            + "    max(\n"
            + "        case \n"
            + "            when cf_key = 'custom_field_6' then cf_value\n"
            + "            else ''\n"
            + "        end \n"
            + "    ) as custom_field_6\n"
            + "from tmp1 \n"
            + "group by project_id, annotation_id";

    public static String DWD_IM_GROUP_RISK_DATA_PROCESS_SQL = ""
            + "select \n"
            + "       value_of_primary_key,\n"
            + "       id,\n"
            + "       CURRENT_TIMESTAMP as record_update_time,\n"
            + "       risk_content,\n"
            + "       risk_link,\n"
            + "       service_provider,\n"
            + "       service_channel,\n"
            + "       uin,\n"
            + "       customer_name,\n"
            + "       risk_type_list,\n"
            + "       alarm_time,\n"
            + "       push_model,\n"
            + "       ticket_id,\n"
            + "       opinion_type,\n"
            + "       short_url,\n"
            + "       is_alarmed,\n"
            + "       is_semantic_hit,\n"
            + "       keywords,\n"
            + "       send_time,\n"
            + "       conversation_id,\n"
            + "       project_id,\n"
            + "       feature_json,\n"
            + "       need_feature,\n"
            + "       role\n"
            + "from dwd_public_opinion_mc\n"
            + "\n"
            + "union all\n"
            + "\n"
            + "select \n"
            + "       value_of_primary_key,\n"
            + "       id,\n"
            + "       CURRENT_TIMESTAMP as record_update_time,\n"
            + "       risk_content,\n"
            + "       risk_link,\n"
            + "       service_provider,\n"
            + "       service_channel,\n"
            + "       uin,\n"
            + "       customer_name,\n"
            + "       risk_type_list,\n"
            + "       alarm_time,\n"
            + "       push_model,\n"
            + "       ticket_id,\n"
            + "       opinion_type,\n"
            + "       short_url,\n"
            + "       is_alarmed,\n"
            + "       is_semantic_hit,\n"
            + "       keywords,\n"
            + "       send_time,\n"
            + "       conversation_id,\n"
            + "       project_id,\n"
            + "       feature_json,\n"
            + "       need_feature,\n"
            + "       role\n"
            + "from dwd_public_opinion_cc\n"
            + "\n"
            + "union all\n"
            + "\n"
            + "select \n"
            + "       value_of_primary_key,\n"
            + "       id,\n"
            + "       CURRENT_TIMESTAMP as record_update_time,\n"
            + "       risk_content,\n"
            + "       risk_link,\n"
            + "       service_provider,\n"
            + "       service_channel,\n"
            + "       uin,\n"
            + "       customer_name,\n"
            + "       risk_type_list,\n"
            + "       alarm_time,\n"
            + "       push_model,\n"
            + "       ticket_id,\n"
            + "       opinion_type,\n"
            + "       short_url,\n"
            + "       is_alarmed,\n"
            + "       is_semantic_hit,\n"
            + "       keywords,\n"
            + "       send_time,\n"
            + "       conversation_id,\n"
            + "       project_id,\n"
            + "       feature_json,\n"
            + "       need_feature,\n"
            + "       role\n"
            + "from dwd_public_opinion_webim";

    public static String DWM_SMARTY_OPINION_SQL = ""
            + "with tmp1 as (\n"
            + "    select \n"
            + "        value_of_primary_key,\n"
            + "        id,\n"
            + "        record_update_time,\n"
            + "        risk_content,\n"
            + "        risk_link,\n"
            + "        service_provider,\n"
            + "        service_channel,\n"
            + "        uin,\n"
            + "        customer_name,\n"
            + "        risk_type_list,\n"
            + "        TIMESTAMPADD(HOUR, -8, alarm_time) as alarm_time,\n"
            + "        push_model,\n"
            + "        ticket_id,\n"
            + "        opinion_type,\n"
            + "        short_url,\n"
            + "        is_alarmed,\n"
            + "        is_semantic_hit,\n"
            + "        keywords,\n"
            + "        TIMESTAMPADD(HOUR, -8, send_time) as send_time,\n"
            + "        conversation_id,\n"
            + "        project_id,\n"
            + "        feature_json,\n"
            + "        need_feature,\n"
            + "        role\n"
            + "    from dwd_opinion_tale\n"
            + "), tmp2 as (\n"
            + "    select\n"
            + "        tmp1.*,\n"
            + "        t2.is_annotated,\n"
            + "        t2.annotate_user,\n"
            + "        TIMESTAMPADD(HOUR, -8, t2.annotate_time) as annotate_time,\n"
            + "        t2.is_valid,\n"
            + "        t2.is_complaint,\n"
            + "        t2.risk_level,\n"
            + "        t2.is_created_chat,\n"
            + "        t2.recognition_mode as recog_type,\n"
            + "        t2.need_create_chat,\n"
            + "        t2.request_uid\n"
            + "    from tmp1\n"
            + "    left join annotate_data_time_view as t2\n"
            + "    on tmp1.id = t2.source_id and t2.project_id = tmp1.project_id\n"
            + "), tmp3 as (\n"
            + "    select \n"
            + "        tmp2.*,\n"
            + "        t3.custom_field_1 as is_claim,\n"
            + "        t3.custom_field_2 as finally_payment,\n"
            + "        t3.custom_field_3 as risk_source,\n"
            + "        t3.custom_field_4 as complain_type,\n"
            + "        case\n"
            + "           when t3.custom_field_5 = '是' then 1 else 0\n"
            + "        end as is_success_complaint,\n"
            + "        t3.custom_field_6 as complain_condition\n"
            + "    from tmp2\n"
            + "    left join dwd_custom_labels t3\n"
            + "    on tmp2.id = t3.annotation_id and t3.project_id = tmp2.project_id\n"
            + ")\n"
            + "select\n"
            + "    *,\n"
            + "    id as record_id\n"
            + "from tmp3";


}