CREATE TABLE dim_smarty_assist_collections
(
    `id`           INT      COMMENT '',
    `source_id`    STRING   COMMENT '',
    `update_time`  DATETIME COMMENT '',
    `create_time`  DATETIME COMMENT '',
    `source_type`  INT      COMMENT '1: 文章 2: 工单 3: 工具',
    `collector`    STRING   COMMENT '',
    `status`       INT      COMMENT '1: 收藏 2:取消收藏',
    `collector_id` BIGINT   COMMENT '',
    `channel_id`   STRING   COMMENT ''
) ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
