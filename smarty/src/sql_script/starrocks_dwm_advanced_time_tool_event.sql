CREATE TABLE dwm_advanced_time_tool_event
(
    `value_of_primary_key`           STRING COMMENT '主键',
    `record_update_time`             DATETIME COMMENT '记录更新时间',
    `module`                         STRING COMMENT '模块',
    `tool_id`                        STRING COMMENT '工具ID',
    `tool_name`                      STRING COMMENT '工具名称',
    `product_name`                   STRING COMMENT '产品名称',
    `principal`                      STRING COMMENT '责任人',
    `scenes`                         STRING COMMENT '场景',
    `creator`                        STRING COMMENT '创建者',
    `modifier`                       STRING COMMENT '修改者',
    `tool_status`                    STRING COMMENT '工具状态:1|开发中,2|发布中,3|已发布',
    `published`                      STRING COMMENT '是否发布:0|未发布过,1|已发布过',
    `beta_zip_addr`                  STRING COMMENT 'BETA版zip地址',
    `formal_zip_addr`                STRING COMMENT '正式版zip地址',
    `beta_version`                   STRING COMMENT 'BETA版版本号',
    `version`                        STRING COMMENT '版本号',
    `status`                         STRING COMMENT '状态',
    `created_at`                     DATETIME COMMENT '创建时间',
    `updated_at`                     DATETIME COMMENT '更新时间',
    `time_tool_input_duration`       BIGINT COMMENT '录入工具API时长',
    `time_tool_dev_duration`         BIGINT COMMENT '录入工具信息时长',
    `time_tool_debug_duration`       BIGINT COMMENT '工具调试时长',
    `time_tool_development_duration` BIGINT COMMENT '开发和调试工具时长',
    `time_tool_publish_duration`     BIGINT COMMENT '工具发布时长',
    `time_tool_all_duration`         BIGINT COMMENT '流程整体总时长',
    `tool_add_ts`                    BIGINT COMMENT '工具添加耗时',
    `tool_verify_ts`                 BIGINT COMMENT '工具验证耗时',
    `tool_config_ts`                 BIGINT COMMENT '工具配置耗时',
    `tool_release_ts`                BIGINT COMMENT '工具整体上线耗时'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
