CREATE  TABLE `dwm_smarty_public_opinion`
(
    `value_of_primary_key` STRING           COMMENT '主键',
    `record_update_time`   STRING           COMMENT '数据更新时间',
    `risk_content`         STRING           COMMENT '风险内容',
    `risk_link`            STRING           COMMENT '风险消息链接',
    `service_provider`     STRING           COMMENT '服务商',
    `service_channel`      STRING           COMMENT '服务通道',
    `uin`                  STRING           COMMENT '客户uin',
    `customer_name`        STRING           COMMENT '客户名称',
    `need_create_chat`     BIGINT           COMMENT '是否拉群告警',
    `is_valid`             BIGINT           COMMENT '是否有效',
    `risk_type_list`       STRING           COMMENT '风险类型列表',
    `risk_source`          STRING           COMMENT '风险来源',
    `risk_level`           STRING           COMMENT '风险级别',
    `is_complaint`         BIGINT           COMMENT '是否投诉',
    `is_success_complaint` BIGINT           COMMENT '是否成功投诉',
    `complain_type`        STRING           COMMENT '投诉类型',
    `complain_condition`   STRING           COMMENT '投诉概况',
    `is_claim`             STRING           COMMENT '是否索赔',
    `is_created_chat`      BIGINT           COMMENT '是否已创建群',
    `is_annotated`         BIGINT           COMMENT '是否已标注',
    `annotate_user`        STRING           COMMENT '标注人',
    `annotate_time`        STRING           COMMENT '标注时间',
    `finally_payment`      STRING           COMMENT '最终赔付',
    `alarm_time`           DATETIME         COMMENT '告警时间',
    `push_model`           STRING           COMMENT '命中模型',
    `recog_type`           STRING           COMMENT '识别方式',
    `ticket_id`            STRING           COMMENT '工单id',
    `opinion_type`         BIGINT           COMMENT '舆情类型:1-MC,2-CC,3-WebIM',
    `short_url`            STRING           COMMENT '短链接',
    `is_alarmed`           BIGINT           COMMENT '是否推送',
    `keywords`             STRING           COMMENT '命中关键词',
    `is_semantic_hit`      BIGINT           COMMENT '是否命中语义',
    `send_time`            DATETIME         COMMENT '发送时间',
    `conversation_id`      STRING           COMMENT '会话ID',
    `need_feature`         BIGINT           COMMENT '是否高置信度,只有webim通道有',
    `feature_json`         STRING           COMMENT '多特征值',
    `record_id`            BIGINT           COMMENT '消息ID',
    `request_uid`          STRING           COMMENT '风险唯一ID'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
COMMENT "风控舆情数据"
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);
