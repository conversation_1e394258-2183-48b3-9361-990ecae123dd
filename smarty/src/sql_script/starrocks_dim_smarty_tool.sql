CREATE TABLE dim_smarty_tool
(
    `id`               BIGINT    COMMENT '',
    `tool_id`          STRING    COMMENT '',
    `name`             STRING    COMMENT '',
    `tool_input`       STRING    COMMENT '',
    `path`             STRING    COMMENT '',
    `req`              STRING    COMMENT '',
    `rsp`              STRING    COMMENT '',
    `async`            INT       COMMENT '',
    `async_callback`   STRING    COMMENT '',
    `callback_timeout` INT       COMMENT '',
    `principal`        STRING    COMMENT '',
    `status`           INT       COMMENT '',
    `creator`          STRING    COMMENT '',
    `modifier`         STRING    COMMENT '',
    `created_at`       DATETIME COMMENT '',
    `updated_at`       DATETIME COMMENT '',
    `enabled`          BIGINT    COMMENT '是否启用, 0: 否，1: 是'
) ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
