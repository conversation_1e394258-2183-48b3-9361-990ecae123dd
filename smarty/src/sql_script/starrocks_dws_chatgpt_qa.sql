CREATE TABLE dws_chatgpt_qa
(
    `user` STRING COMMENT '提问人（rtx）',
    `question_time` STRING COMMENT '提问时间',
    `answer_time` STRING COMMENT '回答时间',
    `session_id` STRING COMMENT '会话id',
    `question_id` STRING COMMENT '问题id',
    `question` STRING COMMENT '问题',
    `answer` STRING COMMENT '回答',
    `has_copy` INT COMMENT '是否复制 1:已复制 0:未复制',
    `copy_count` INT COMMENT '滑屏复制次数',
    `feedback_action` INT COMMENT '是否点赞 0:未反馈 1:点赞 2:点踩',
    `model` STRING COMMENT '模型, youtu/glm',
    `search_id` STRING COMMENT '搜索ID，同一个提问的不同模型回答保持一致',
    `source` BIGINT COMMENT '1: 企业号 2: 智能辅助-工单控制台 3: 智能辅助-webim控制台 4: 智能辅助-cc控制台 5. 群工作台',
    `invoke_from` BIGINT COMMENT '唤醒来源：1: 流水， 2: 智能辅助',
    `model_id` STRING COMMENT '模型ID',
    `knowledge_injection` VARCHAR(1048576) COMMENT '知识注入内容',
    `knowledge_type` STRING COMMENT '知识注入类型',
    `is_knowledge_injection` BIGINT COMMENT '是否知识注入',
    `global_copy_count` INT COMMENT '全局复制次数',
    `is_adopt` BIGINT COMMENT '是否采纳。1:是 0:否',
    `is_from_smart_ask` BIGINT COMMENT '问答是否来自智能提问。0:否，1:是',
    `recommend_question_id` STRING COMMENT '推荐问题ID',
    `adopt_ticket_id` STRING COMMENT '被采纳的工单id'
) ENGINE=OLAP
DISTRIBUTED BY HASH(`user`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");

alter table dws_chatgpt_qa add column is_auto_exec int;
