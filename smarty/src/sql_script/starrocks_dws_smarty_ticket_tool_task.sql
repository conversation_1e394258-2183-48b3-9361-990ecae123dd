CREATE TABLE dws_smarty_ticket_tool_task
(
    `id`          BIGINT COMMENT '',
    `ticket_id`   STRING COMMENT '工单ID',
    `task_id`     STRING COMMENT 'task_id',
    `status`      INT COMMENT '状态',
    `tool_id`     STRING COMMENT '工具ID',
    `tool_name`   STRING COMMENT '工具名称',
    `create_time` DATETIME COMMENT '创建时间',
    `update_time` DATETIME COMMENT '修改时间',
    `feed_back`   STRING COMMENT '工具反馈',
    `reply_inner` STRING COMMENT '内部回复',
    `reply_outer` STRING COMMENT '外部回复',
    `duration`    BIGINT COMMENT '工具执行耗时'
) ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
