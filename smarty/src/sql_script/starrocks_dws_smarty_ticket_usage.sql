CREATE TABLE dws_smarty_ticket_usage
(
    `value_of_primary_key` STRING COMMENT '主键',
    `channel`              BIGINT COMMENT '使用类型：1-工单，2-知识库文章，3-官网文章，4-精选文章，5-自动执行工具，6-主动执行工具，7-必读',
    `object_id`            STRING COMMENT '对象ID',
    `user`                 STRING COMMENT '使用人',
    `event_time`           DATETIME COMMENT '操作时间',
    `ticket_id`            STRING COMMENT '工单ID'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
