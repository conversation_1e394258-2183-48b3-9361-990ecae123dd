CREATE TABLE ods_advanced_tool_event
(
    `value_of_primary_key` STRING COMMENT '',
    `record_update_time`   STRING COMMENT '',
    `object_id`            STRING COMMENT '',
    `uin`                  STRING COMMENT '',
    `user`                 STRING COMMENT '',
    `app_source`           INT COMMENT '',
    `scene`                INT COMMENT '',
    `tool_name`            STRING COMMENT '',
    `execution_start_time` STRING COMMENT '',
    `execution_end_time`   STRING COMMENT '',
    `version`              STRING COMMENT '',
    `tool_id`              STRING COMMENT '',
    `result`               STRING COMMENT ''
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");



