CREATE TABLE dwm_antool_tool_release_path_ts
(
    `tool_id`         STRING COMMENT '工具ID',
    `tool_add_ts`     BIGINT COMMENT '工具添加耗时',
    `tool_verify_ts`  BIGINT COMMENT '工具验证耗时',
    `tool_config_ts`  BIGINT COMMENT '工具配置耗时',
    `tool_release_ts` BIGINT COMMENT '工具整体上线耗时'
) ENGINE=OLAP
PRIMARY KEY(`tool_id`)
DISTRIBUTED BY HASH(`tool_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
